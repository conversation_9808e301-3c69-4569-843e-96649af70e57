# 🎨 Phase 8: Image Assets & DALL-E Prompts Guide

## Overview
This document provides detailed DALL-E prompts, file specifications, and placement instructions for all image assets needed in the SleepSound Coach app.

---

## 🎨 COLOR PALETTE REFERENCE

Use these colors consistently in all generated images:
- **Primary (Sky Blue)**: `#64B5F6`
- **Secondary (Soft Purple)**: `#2D1B4E`
- **Background (Deep Navy)**: `#0A1128`
- **Accent (Light Blue)**: `#90CAF9`

**Mood**: Peaceful, professional, premium, trustworthy, calming, nighttime

---

## 📱 IMAGE SPECIFICATIONS

### Required Sizes:
- **1x**: Base resolution
- **2x**: 2× base resolution (most common)
- **3x**: 3× base resolution (iPhone Pro models)

### Format:
- **PNG** with transparency for icons and illustrations
- **JPEG** for photographic images
- **PDF** for vector graphics (optional, recommended for scalability)

---

## 🖼️ IMAGE ASSETS NEEDED

### 1. ONBOARDING WELCOME ILLUSTRATION

**File Name**: `<EMAIL>`  
**Dimensions**: 600×600px (@2x), 900×900px (@3x)  
**Location**: `EnhancedOnboardingView` → `WelcomeScreen`  
**Purpose**: Main hero illustration for welcome screen

**DALL-E Prompt**:
```
Create a minimalist, modern illustration of a peaceful nighttime scene for a sleep tracking app. 
Show a crescent moon with small stars in a gradient sky transitioning from deep navy (#0A1128) 
at the top to soft purple (#2D1B4E) in the middle to sky blue (#64B5F6) at the bottom. 
Include subtle, abstract sound waves emanating from the moon in light blue (#90CAF9). 
The style should be flat design, geometric, calming, and professional. 
No text, no people, just abstract shapes representing sleep and sound. 
Transparent background. Aspect ratio 1:1.
```

**Implementation**:
```swift
Image("onboarding-welcome")
    .resizable()
    .scaledToFit()
    .frame(width: 200, height: 200)
```

---

### 2. SOUND DETECTION ILLUSTRATION

**File Name**: `<EMAIL>`  
**Dimensions**: 400×400px (@2x), 600×600px (@3x)  
**Location**: `FeaturesScreen` in onboarding  
**Purpose**: Illustrate sound detection feature

**DALL-E Prompt**:
```
Create a modern, minimalist illustration showing sound wave detection for a sleep app. 
Show a stylized microphone icon in sky blue (#64B5F6) with concentric circular sound waves 
radiating outward in varying shades of blue and purple (#2D1B4E, #90CAF9). 
Include small icons representing snoring (zzz), coughing (cloud), and talking (speech bubble) 
around the waves. Flat design style, geometric shapes, calming nighttime colors. 
Dark navy background (#0A1128). Transparent PNG. Aspect ratio 1:1.
```

**Implementation**:
```swift
Image("feature-sound-detection")
    .resizable()
    .scaledToFit()
    .frame(width: 150, height: 150)
```

---

### 3. AI BRAIN ILLUSTRATION

**File Name**: `<EMAIL>`  
**Dimensions**: 400×400px (@2x), 600×600px (@3x)  
**Location**: `IntelligenceScreen` in onboarding  
**Purpose**: Represent AI-powered insights

**DALL-E Prompt**:
```
Create a futuristic, minimalist illustration of an AI brain for a sleep tracking app. 
Show a stylized brain made of geometric shapes and neural network connections in gradient 
colors from sky blue (#64B5F6) to soft purple (#2D1B4E). Include small data points, 
charts, and sparkles around the brain to represent intelligence and insights. 
Add subtle moon and star symbols integrated into the neural network. 
Flat design, modern, professional, calming. Dark background. Transparent PNG. Aspect ratio 1:1.
```

**Implementation**:
```swift
Image("feature-ai-intelligence")
    .resizable()
    .scaledToFit()
    .frame(width: 150, height: 150)
```

---

### 4. EMPTY STATE - NO SESSIONS

**File Name**: `<EMAIL>`  
**Dimensions**: 500×400px (@2x), 750×600px (@3x)  
**Location**: `HistoryView` when no sleep sessions exist  
**Purpose**: Friendly empty state illustration

**DALL-E Prompt**:
```
Create a friendly, minimalist illustration for an empty state in a sleep tracking app. 
Show a peaceful sleeping moon character with closed eyes and a gentle smile, 
surrounded by soft clouds and small stars. Use calming colors: sky blue (#64B5F6), 
soft purple (#2D1B4E), and light blue (#90CAF9). Include a small bed or pillow 
beneath the moon. Flat design, cute but professional, inviting. 
Transparent background. Aspect ratio 5:4.
```

**Implementation**:
```swift
VStack(spacing: 20) {
    Image("empty-state-no-sessions")
        .resizable()
        .scaledToFit()
        .frame(width: 250, height: 200)
    
    Text("No Sleep Sessions Yet")
        .font(.title2.bold())
    
    Text("Start tracking your first night of sleep")
        .font(.subheadline)
        .foregroundColor(.secondary)
}
```

---

### 5. EMPTY STATE - NO INSIGHTS

**File Name**: `<EMAIL>`  
**Dimensions**: 500×400px (@2x), 750×600px (@3x)  
**Location**: `InsightsView` when insufficient data  
**Purpose**: Encourage more tracking

**DALL-E Prompt**:
```
Create a minimalist illustration showing a lightbulb with a moon inside it, 
representing sleep insights. The lightbulb should be outlined in sky blue (#64B5F6) 
with the moon in soft purple (#2D1B4E). Add small sparkles and data points around 
the bulb that are dimmed/faded to represent "not yet available". Include a small 
chart or graph icon that's partially filled. Flat design, modern, encouraging. 
Transparent background. Aspect ratio 5:4.
```

**Implementation**:
```swift
VStack(spacing: 20) {
    Image("empty-state-no-insights")
        .resizable()
        .scaledToFit()
        .frame(width: 250, height: 200)
    
    Text("Not Enough Data Yet")
        .font(.title2.bold())
    
    Text("Track 7 nights to unlock AI insights")
        .font(.subheadline)
        .foregroundColor(.secondary)
}
```

---

### 6. ACHIEVEMENT CELEBRATION

**File Name**: `<EMAIL>`  
**Dimensions**: 400×400px (@2x), 600×600px (@3x)  
**Location**: Achievement alerts and celebrations  
**Purpose**: Celebrate milestones

**DALL-E Prompt**:
```
Create a celebratory illustration for sleep tracking achievements. 
Show a trophy or medal with a moon and stars design in the center. 
Use gradient colors from sky blue (#64B5F6) to soft purple (#2D1B4E). 
Add confetti, sparkles, and small fireworks around the trophy in light blue (#90CAF9). 
Include subtle sleep-related icons (pillow, moon, zzz) in the background. 
Flat design, joyful, premium feel. Transparent background. Aspect ratio 1:1.
```

**Implementation**:
```swift
Image("achievement-celebration")
    .resizable()
    .scaledToFit()
    .frame(width: 120, height: 120)
```

---

### 7. SLEEP QUALITY EXCELLENT

**File Name**: `<EMAIL>`  
**Dimensions**: 300×300px (@2x), 450×450px (@3x)  
**Location**: Morning report for high-quality sleep  
**Purpose**: Visual feedback for great sleep

**DALL-E Prompt**:
```
Create a cheerful illustration representing excellent sleep quality. 
Show a happy, smiling sun rising over a peaceful sleeping moon. 
Use warm gradient colors: sky blue (#64B5F6) for the sun rays, 
soft purple (#2D1B4E) for the moon, and light blue (#90CAF9) for the background. 
Add small stars fading away and birds flying. Flat design, optimistic, energizing. 
Transparent background. Aspect ratio 1:1.
```

---

### 8. SLEEP QUALITY POOR

**File Name**: `<EMAIL>`  
**Dimensions**: 300×300px (@2x), 450×450px (@3x)  
**Location**: Morning report for poor sleep  
**Purpose**: Empathetic visual for poor sleep

**DALL-E Prompt**:
```
Create an empathetic illustration representing poor sleep quality. 
Show a tired moon with half-closed eyes surrounded by dark clouds. 
Use muted colors: darker purple (#2D1B4E) for clouds, desaturated blue for the moon. 
Add small lightning bolts or rain drops to represent restlessness. 
Keep it supportive, not negative. Flat design, empathetic, gentle. 
Transparent background. Aspect ratio 1:1.
```

---

### 9. COACHING TIP - EXERCISE

**File Name**: `<EMAIL>`  
**Dimensions**: 300×300px (@2x), 450×450px (@3x)  
**Location**: Coaching tips in InsightsView  
**Purpose**: Illustrate exercise recommendations

**DALL-E Prompt**:
```
Create a minimalist illustration showing exercise and sleep connection. 
Show a stylized figure running or doing yoga with a moon in the background. 
Use sky blue (#64B5F6) for the figure, soft purple (#2D1B4E) for the moon, 
and light blue (#90CAF9) for motion lines. Add small stars and a clock showing 
afternoon time. Flat design, motivating, clean. Transparent background. Aspect ratio 1:1.
```

---

### 10. COACHING TIP - CAFFEINE

**File Name**: `<EMAIL>`  
**Dimensions**: 300×300px (@2x), 450×450px (@3x)  
**Location**: Coaching tips in InsightsView  
**Purpose**: Illustrate caffeine recommendations

**DALL-E Prompt**:
```
Create a minimalist illustration about caffeine and sleep. 
Show a coffee cup with steam rising, and a clock showing afternoon time, 
with a moon in the background crossed out or dimmed. Use sky blue (#64B5F6) 
for the cup, soft purple (#2D1B4E) for the moon, and light blue (#90CAF9) 
for the steam. Add a small "no" symbol or warning icon. Flat design, 
informative, friendly. Transparent background. Aspect ratio 1:1.
```

---

### 11. COACHING TIP - BEDTIME ROUTINE

**File Name**: `<EMAIL>`  
**Dimensions**: 300×300px (@2x), 450×450px (@3x)  
**Location**: Coaching tips in InsightsView  
**Purpose**: Illustrate bedtime routine importance

**DALL-E Prompt**:
```
Create a calming illustration showing a bedtime routine. 
Show a sequence of small icons: book, meditation pose, moon, bed, 
connected by a flowing line in sky blue (#64B5F6). Use soft purple (#2D1B4E) 
for the background elements and light blue (#90CAF9) for highlights. 
Add small stars and a peaceful atmosphere. Flat design, serene, organized. 
Transparent background. Aspect ratio 1:1.
```

---

### 12. PREMIUM FEATURE - UNLIMITED

**File Name**: `<EMAIL>`  
**Dimensions**: 400×400px (@2x), 600×600px (@3x)  
**Location**: PaywallView feature illustrations  
**Purpose**: Represent unlimited access

**DALL-E Prompt**:
```
Create a premium illustration representing unlimited features. 
Show an infinity symbol made of flowing stars and moon shapes in gradient 
colors from sky blue (#64B5F6) to soft purple (#2D1B4E). Add sparkles, 
crown elements, and premium badges around it in light blue (#90CAF9). 
Include subtle sound waves and health icons. Flat design, luxurious, 
premium feel. Transparent background. Aspect ratio 1:1.
```

---

### 13. PREMIUM FEATURE - AI INSIGHTS

**File Name**: `<EMAIL>`  
**Dimensions**: 400×400px (@2x), 600×600px (@3x)  
**Location**: PaywallView feature illustrations  
**Purpose**: Represent AI-powered insights

**DALL-E Prompt**:
```
Create a premium illustration for AI insights feature. 
Show a glowing crystal ball or orb with data visualizations, charts, 
and neural network patterns inside. Use gradient from sky blue (#64B5F6) 
to soft purple (#2D1B4E) with light blue (#90CAF9) highlights. 
Add moon and star symbols, sparkles, and a premium glow effect. 
Flat design with subtle 3D elements, magical, intelligent. 
Transparent background. Aspect ratio 1:1.
```

---

### 14. PROFILE SETUP - DEMOGRAPHICS

**File Name**: `<EMAIL>`  
**Dimensions**: 300×300px (@2x), 450×450px (@3x)  
**Location**: ProfileSetupView demographics step  
**Purpose**: Illustrate personalization

**DALL-E Prompt**:
```
Create a friendly illustration representing user demographics and personalization. 
Show a stylized person silhouette made of geometric shapes with customization 
icons around it (age, height, weight symbols). Use sky blue (#64B5F6) for 
the figure, soft purple (#2D1B4E) for icons, and light blue (#90CAF9) for 
connecting lines. Add small stars and moon elements. Flat design, inclusive, 
modern. Transparent background. Aspect ratio 1:1.
```

---

### 15. PROFILE SETUP - SLEEP GOALS

**File Name**: `<EMAIL>`  
**Dimensions**: 300×300px (@2x), 450×450px (@3x)  
**Location**: ProfileSetupView sleep goals step  
**Purpose**: Illustrate goal setting

**DALL-E Prompt**:
```
Create an inspiring illustration for sleep goals. 
Show a target or bullseye with a moon in the center, surrounded by 
achievement stars and progress indicators. Use gradient from sky blue (#64B5F6) 
to soft purple (#2D1B4E). Add small icons representing time, quality, 
and consistency in light blue (#90CAF9). Include upward arrows and 
positive symbols. Flat design, motivating, goal-oriented. 
Transparent background. Aspect ratio 1:1.
```

---

## 📋 IMPLEMENTATION CHECKLIST

### For Each Image:
- [ ] Generate image using DALL-E with provided prompt
- [ ] Download in highest quality
- [ ] Resize to @2x dimensions
- [ ] Create @3x version (1.5× the @2x size)
- [ ] Optimize file size (use ImageOptim or similar)
- [ ] Add to Xcode Assets.xcassets
- [ ] Set rendering mode (template or original)
- [ ] Test on different screen sizes
- [ ] Verify colors match app palette
- [ ] Check accessibility (sufficient contrast)

---

## 🎨 ASSET CATALOG ORGANIZATION

```
Assets.xcassets/
├── Onboarding/
│   ├── onboarding-welcome.imageset/
│   ├── feature-sound-detection.imageset/
│   ├── feature-ai-intelligence.imageset/
│   └── profile-demographics.imageset/
├── EmptyStates/
│   ├── empty-state-no-sessions.imageset/
│   └── empty-state-no-insights.imageset/
├── Achievements/
│   └── achievement-celebration.imageset/
├── SleepQuality/
│   ├── sleep-quality-excellent.imageset/
│   └── sleep-quality-poor.imageset/
├── Coaching/
│   ├── coaching-exercise.imageset/
│   ├── coaching-caffeine.imageset/
│   └── coaching-routine.imageset/
└── Premium/
    ├── premium-unlimited.imageset/
    └── premium-ai-insights.imageset/
```

---

## 🔧 XCODE INTEGRATION

### Adding Images to Assets:
1. Open `Assets.xcassets` in Xcode
2. Right-click → New Image Set
3. Name it exactly as specified (without @2x/@3x suffix)
4. Drag @2x image to 2x slot
5. Drag @3x image to 3x slot
6. Set rendering mode if needed

### Using in SwiftUI:
```swift
Image("image-name")
    .resizable()
    .scaledToFit()
    .frame(width: 200, height: 200)
```

---

## 📊 TOTAL ASSETS SUMMARY

**Total Images Needed**: 15  
**Total Files** (with @2x and @3x): 30  
**Estimated Generation Time**: 2-3 hours  
**Estimated File Size**: ~5-10 MB total

---

## 🎯 PRIORITY ORDER

### High Priority (Implement First):
1. Onboarding welcome
2. Empty states (no sessions, no insights)
3. Profile setup illustrations

### Medium Priority:
4. Feature illustrations (sound detection, AI)
5. Sleep quality visuals
6. Coaching tips

### Low Priority (Nice to Have):
7. Achievement celebrations
8. Premium feature illustrations

---

## ✅ QUALITY CHECKLIST

Before finalizing each image:
- [ ] Colors match app palette exactly
- [ ] Style is consistent across all images
- [ ] Transparent background (PNG)
- [ ] Optimized file size (<200 KB per image)
- [ ] Looks good on both light and dark backgrounds
- [ ] Scales well at different sizes
- [ ] No text in images (use SwiftUI Text instead)
- [ ] Accessible (clear shapes, good contrast)
- [ ] Professional and premium feel
- [ ] Calming and sleep-appropriate mood

---

**Document Created**: October 1, 2025  
**Total Images**: 15  
**Ready for**: DALL-E Generation & Xcode Integration

