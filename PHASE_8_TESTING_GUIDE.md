# 🧪 Phase 8: Testing Guide

## Overview
This guide provides step-by-step testing procedures for all Phase 8 features.

---

## 🎯 TESTING CHECKLIST

### 1. User Profile System ✅

#### Test Profile Creation:
- [ ] Open app for first time
- [ ] Navigate to profile setup
- [ ] Enter age (test: 25)
- [ ] Select sex (test: Male)
- [ ] Enter weight (test: 75 kg)
- [ ] Enter height (test: 180 cm)
- [ ] Verify BMI calculates correctly (23.1)
- [ ] Set sleep goal (test: 8 hours)
- [ ] Set bedtime (test: 10:00 PM)
- [ ] Set wake time (test: 6:00 AM)
- [ ] Select fitness level (test: Moderate)
- [ ] Review summary screen
- [ ] Verify all data is correct
- [ ] Tap "Complete"
- [ ] Verify profile saves to database

#### Test Profile Calculations:
```swift
// Test Case 1: Normal BMI
Age: 30, Weight: 70kg, Height: 170cm
Expected BMI: 24.2 (Normal)
Expected Sleep: 7-9 hours
Expected Apnea Risk: Low

// Test Case 2: High BMI, Older
Age: 45, Weight: 95kg, Height: 175cm, Sex: Male
Expected BMI: 31.0 (Obese)
Expected Sleep: 7-9 hours
Expected Apnea Risk: High

// Test Case 3: Teenager
Age: 16, Weight: 60kg, Height: 165cm
Expected BMI: 22.0 (Normal)
Expected Sleep: 8-10 hours
Expected Apnea Risk: Low
```

#### Test Personalized Recommendations:
- [ ] Create profile with BMI > 30
- [ ] Verify sleep apnea warning appears
- [ ] Create profile with sedentary fitness
- [ ] Verify exercise recommendation
- [ ] Create profile as teenager
- [ ] Verify 8-10 hour recommendation
- [ ] Create profile as senior (70+)
- [ ] Verify 7-8 hour recommendation

---

### 2. Enhanced Onboarding ✅

#### Test Onboarding Flow:
- [ ] Delete app and reinstall (or clear data)
- [ ] Launch app
- [ ] Verify onboarding appears automatically
- [ ] **Screen 1: Welcome**
  - [ ] Verify animated moon icon
  - [ ] Verify app name and tagline
  - [ ] Verify 3 feature badges
  - [ ] Swipe to next screen
- [ ] **Screen 2: Features**
  - [ ] Verify 4 feature cards
  - [ ] Verify icons and descriptions
  - [ ] Swipe to next screen
- [ ] **Screen 3: Intelligence**
  - [ ] Verify AI brain icon
  - [ ] Verify 3 intelligence cards
  - [ ] Verify example insights
  - [ ] Swipe to next screen
- [ ] **Screen 4: Permissions**
  - [ ] Verify 3 permission rows
  - [ ] Verify explanations
  - [ ] Swipe to next screen
- [ ] **Screen 5: Get Started**
  - [ ] Verify completion checkmark
  - [ ] Tap "Create Your Profile"
  - [ ] Verify transition to profile setup

#### Test Skip Functionality:
- [ ] Restart onboarding
- [ ] Tap "Skip" button
- [ ] Verify confirmation alert appears
- [ ] Tap "Continue Setup"
- [ ] Verify stays in onboarding
- [ ] Tap "Skip" again
- [ ] Tap "Skip" in alert
- [ ] Verify creates default profile
- [ ] Verify goes to main app

#### Test Profile Setup:
- [ ] Complete onboarding to profile setup
- [ ] **Step 1: Welcome**
  - [ ] Verify progress indicator (1/6)
  - [ ] Tap "Continue"
- [ ] **Step 2: Demographics**
  - [ ] Verify progress indicator (2/6)
  - [ ] Adjust age with stepper
  - [ ] Select sex from picker
  - [ ] Tap "Back" - verify goes back
  - [ ] Tap "Continue"
- [ ] **Step 3: Physical**
  - [ ] Verify progress indicator (3/6)
  - [ ] Enter weight
  - [ ] Enter height
  - [ ] Verify BMI preview updates
  - [ ] Tap "Continue"
- [ ] **Step 4: Sleep Goals**
  - [ ] Verify progress indicator (4/6)
  - [ ] Adjust sleep goal slider
  - [ ] Set bedtime with picker
  - [ ] Set wake time with picker
  - [ ] Tap "Continue"
- [ ] **Step 5: Fitness**
  - [ ] Verify progress indicator (5/6)
  - [ ] Tap each fitness level
  - [ ] Verify selection highlights
  - [ ] Verify haptic feedback
  - [ ] Tap "Continue"
- [ ] **Step 6: Summary**
  - [ ] Verify progress indicator (6/6)
  - [ ] Verify all data is correct
  - [ ] Verify personalized insights
  - [ ] Tap "Complete"
  - [ ] Verify saves and closes

---

### 3. Share Functionality ✅

#### Test Share Feature:
- [ ] Create a test sleep session
- [ ] Go to History view
- [ ] Swipe left on session
- [ ] Tap "Share" button
- [ ] Verify haptic feedback
- [ ] Verify share sheet appears
- [ ] Verify share text includes:
  - [ ] Date
  - [ ] Duration
  - [ ] Quality
  - [ ] Quiet score
  - [ ] Event counts
  - [ ] App name
- [ ] Test sharing to:
  - [ ] Messages
  - [ ] Mail
  - [ ] Notes
  - [ ] Copy to clipboard
- [ ] Verify shared text is formatted correctly

#### Test Share Text Format:
```
Expected Format:
🌙 My Sleep Report from SleepSound Coach

📅 Date: Oct 1, 2025
⏱️ Duration: 7h 45m
⭐ Quality: Excellent
🎯 Quiet Score: 92/100

💤 Snoring: 3 events
😷 Coughing: 1 event
💬 Sleep Talking: 0 events

Track your sleep with SleepSound Coach!
```

---

### 4. Favorite Functionality ✅

#### Test Favorite Feature:
- [ ] Go to History view
- [ ] Swipe right on session
- [ ] Tap "Favorite" button
- [ ] Verify haptic feedback
- [ ] Verify star icon appears
- [ ] Swipe right again
- [ ] Verify shows "Unfavorite"
- [ ] Tap "Unfavorite"
- [ ] Verify star icon disappears
- [ ] Close and reopen app
- [ ] Verify favorite status persists

#### Test Favorite Persistence:
- [ ] Mark 3 sessions as favorite
- [ ] Force quit app
- [ ] Reopen app
- [ ] Verify all 3 are still favorited
- [ ] Unfavorite 1 session
- [ ] Force quit app
- [ ] Reopen app
- [ ] Verify only 2 are favorited

---

### 5. Color Theme Consistency ✅

#### Visual Inspection:
- [ ] Open ProfileSetupView
- [ ] Verify sky blue (#64B5F6) used for:
  - [ ] Progress indicators
  - [ ] Primary buttons
  - [ ] Accent text
  - [ ] Icons
- [ ] Verify soft purple (#2D1B4E) used for:
  - [ ] Gradients
  - [ ] Secondary elements
- [ ] Verify deep navy (#0A1128) used for:
  - [ ] Backgrounds
- [ ] Verify light blue (#90CAF9) used for:
  - [ ] Subtle highlights

#### Accessibility Check:
- [ ] Enable VoiceOver
- [ ] Navigate through onboarding
- [ ] Verify all elements are accessible
- [ ] Verify color contrast is sufficient
- [ ] Test with Dynamic Type (largest size)
- [ ] Verify text is readable

---

### 6. Animations & Haptics ✅

#### Test Animations:
- [ ] Open onboarding welcome screen
- [ ] Verify moon icon animates in
- [ ] Verify text fades in
- [ ] Navigate through profile setup
- [ ] Verify progress bar animates
- [ ] Verify screen transitions are smooth
- [ ] Toggle favorite
- [ ] Verify star animates

#### Test Haptic Feedback:
- [ ] Tap any button
- [ ] Verify haptic feedback (if on device)
- [ ] Toggle favorite
- [ ] Verify light haptic
- [ ] Tap share
- [ ] Verify medium haptic
- [ ] Complete profile
- [ ] Verify success haptic

---

### 7. Edge Cases ✅

#### Test Invalid Input:
- [ ] Enter weight: 0 kg
- [ ] Verify BMI calculation handles it
- [ ] Enter height: 0 cm
- [ ] Verify BMI calculation handles it
- [ ] Enter age: 150
- [ ] Verify app doesn't crash

#### Test Extreme Values:
- [ ] Age: 13 (minimum)
- [ ] Verify teenager recommendations
- [ ] Age: 100 (maximum)
- [ ] Verify senior recommendations
- [ ] Weight: 300 kg
- [ ] Verify high BMI warning
- [ ] Height: 250 cm
- [ ] Verify calculations work

#### Test Data Persistence:
- [ ] Create profile
- [ ] Force quit app
- [ ] Reopen app
- [ ] Verify profile data persists
- [ ] Update profile
- [ ] Force quit app
- [ ] Reopen app
- [ ] Verify updates persisted

---

### 8. Integration Testing ✅

#### Test with Existing Features:
- [ ] Create user profile
- [ ] Start sleep tracking
- [ ] End sleep session
- [ ] Verify session appears in history
- [ ] Share session
- [ ] Verify share works
- [ ] Favorite session
- [ ] Verify favorite persists
- [ ] View insights
- [ ] Verify personalized recommendations appear

#### Test Profile Impact on Insights:
- [ ] Create profile: Age 20, Sedentary
- [ ] View insights
- [ ] Verify exercise recommendations
- [ ] Update profile: Age 70, Active
- [ ] View insights
- [ ] Verify recommendations changed

---

## 🐛 BUG REPORTING

### If You Find a Bug:
1. **Describe the issue**
2. **Steps to reproduce**
3. **Expected behavior**
4. **Actual behavior**
5. **Screenshots** (if applicable)
6. **Device/iOS version**

### Example Bug Report:
```
Issue: BMI not calculating correctly
Steps:
1. Open profile setup
2. Enter weight: 70 kg
3. Enter height: 170 cm
Expected: BMI = 24.2
Actual: BMI = 0.0
Device: iPhone 15 Pro, iOS 18.0
```

---

## ✅ ACCEPTANCE CRITERIA

### Phase 8 is complete when:
- [ ] All tests pass
- [ ] No critical bugs
- [ ] Build succeeds
- [ ] Profile system works
- [ ] Onboarding flows smoothly
- [ ] Share functionality works
- [ ] Favorite functionality works
- [ ] Colors are consistent
- [ ] Animations are smooth
- [ ] Haptics work on device
- [ ] Data persists correctly
- [ ] Personalization works
- [ ] Recommendations are accurate

---

## 📊 TEST RESULTS TEMPLATE

```
Date: ___________
Tester: ___________

User Profile System:        [ ] Pass  [ ] Fail
Enhanced Onboarding:        [ ] Pass  [ ] Fail
Share Functionality:        [ ] Pass  [ ] Fail
Favorite Functionality:     [ ] Pass  [ ] Fail
Color Theme Consistency:    [ ] Pass  [ ] Fail
Animations & Haptics:       [ ] Pass  [ ] Fail
Edge Cases:                 [ ] Pass  [ ] Fail
Integration:                [ ] Pass  [ ] Fail

Bugs Found: ___________
Critical: ___________
High: ___________
Medium: ___________
Low: ___________

Overall Status: [ ] PASS  [ ] FAIL

Notes:
_________________________________
_________________________________
_________________________________
```

---

## 🎯 PRIORITY TESTING

### High Priority (Test First):
1. User profile creation
2. Onboarding flow
3. Share functionality
4. Favorite functionality
5. Data persistence

### Medium Priority:
6. Personalized recommendations
7. BMI calculations
8. Color consistency
9. Animations

### Low Priority:
10. Edge cases
11. Extreme values
12. Haptic feedback

---

**Testing Guide Created**: October 1, 2025  
**Total Test Cases**: 100+  
**Estimated Testing Time**: 2-3 hours  
**Ready for**: Manual Testing

