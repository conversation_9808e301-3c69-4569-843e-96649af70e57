//
//  SoundEvent.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 30/09/2025.
//

import Foundation
import SwiftData

enum SoundEventType: String, Codable {
    case snoring
    case coughing
    case sleepTalking
    case other
    
    var displayName: String {
        switch self {
        case .snoring: return "Snoring"
        case .coughing: return "Coughing"
        case .sleepTalking: return "Sleep Talking"
        case .other: return "Other"
        }
    }
    
    var icon: String {
        switch self {
        case .snoring: return "zzz"
        case .coughing: return "lungs.fill"
        case .sleepTalking: return "bubble.left.fill"
        case .other: return "waveform"
        }
    }
    
    var color: String {
        switch self {
        case .snoring: return "blue"
        case .coughing: return "orange"
        case .sleepTalking: return "purple"
        case .other: return "gray"
        }
    }
}

enum SoundSeverity: String, Codable {
    case light
    case moderate
    case severe
    
    var displayName: String {
        switch self {
        case .light: return "Light"
        case .moderate: return "Moderate"
        case .severe: return "Severe"
        }
    }
}

@Model
final class SoundEvent {
    var id: UUID
    var timestamp: Date
    var duration: Double // in seconds
    var intensity: Double // 0-1 scale
    var eventTypeRaw: String
    var severityRaw: String
    var confidence: Double // 0-1 scale from ML model
    
    // Optional transcription for sleep talking
    var transcription: String?
    
    // Audio file path (for premium users)
    var audioFilePath: String?
    
    // HealthKit context at time of event
    var heartRateAtEvent: Double?
    var sleepStageAtEvent: String? // "Deep", "Light", "REM", "Awake"
    
    // Relationship
    var session: SleepSession?
    
    var eventType: SoundEventType {
        get { SoundEventType(rawValue: eventTypeRaw) ?? .other }
        set { eventTypeRaw = newValue.rawValue }
    }
    
    var severity: SoundSeverity {
        get { SoundSeverity(rawValue: severityRaw) ?? .light }
        set { severityRaw = newValue.rawValue }
    }
    
    init(
        timestamp: Date = Date(),
        duration: Double,
        intensity: Double,
        eventType: SoundEventType,
        severity: SoundSeverity = .light,
        confidence: Double = 0.8
    ) {
        self.id = UUID()
        self.timestamp = timestamp
        self.duration = duration
        self.intensity = intensity
        self.eventTypeRaw = eventType.rawValue
        self.severityRaw = severity.rawValue
        self.confidence = confidence
    }
    
    func determineSeverity() {
        // Determine severity based on duration and intensity
        let severityScore = (duration / 60.0) * intensity
        
        if severityScore < 0.3 {
            severity = .light
        } else if severityScore < 0.7 {
            severity = .moderate
        } else {
            severity = .severe
        }
    }
    
    var formattedDuration: String {
        if duration < 60 {
            return String(format: "%.0fs", duration)
        } else {
            let minutes = Int(duration) / 60
            let seconds = Int(duration) % 60
            return "\(minutes)m \(seconds)s"
        }
    }
    
    var formattedTime: String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: timestamp)
    }
}

