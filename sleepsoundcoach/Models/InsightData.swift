//
//  InsightData.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 30/09/2025.
//

import Foundation
import SwiftData

@Model
final class InsightData {
    var id: UUID
    var generatedDate: Date
    var insightType: String // "weekly", "monthly", "correlation"
    var title: String
    var message: String
    var metric: String // e.g., "snoring_reduction", "sleep_quality_improvement"
    var value: Double
    var isPositive: Bool
    
    init(
        generatedDate: Date = Date(),
        insightType: String,
        title: String,
        message: String,
        metric: String,
        value: Double,
        isPositive: Bool = true
    ) {
        self.id = UUID()
        self.generatedDate = generatedDate
        self.insightType = insightType
        self.title = title
        self.message = message
        self.metric = metric
        self.value = value
        self.isPositive = isPositive
    }
}

