//
//  UserProfile.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 01/10/2025.
//

import Foundation
import SwiftData

@Model
class UserProfile {
    // MARK: - Demographic Data
    
    var age: Int
    var sex: Sex
    var weightKg: Double // Weight in kilograms
    var heightCm: Double // Height in centimeters
    var sleepGoalHours: Double // Target sleep duration in hours
    
    // MARK: - Preferences
    
    var preferredBedtime: Date
    var preferredWakeTime: Date
    var fitnessLevel: FitnessLevel
    
    // MARK: - Tracking
    
    var profileCreatedDate: Date
    var lastUpdatedDate: Date
    
    // MARK: - Computed Properties
    
    var bmi: Double {
        let heightM = heightCm / 100.0
        return weightKg / (heightM * heightM)
    }
    
    var bmiCategory: BMICategory {
        switch bmi {
        case ..<18.5:
            return .underweight
        case 18.5..<25:
            return .normal
        case 25..<30:
            return .overweight
        default:
            return .obese
        }
    }
    
    var recommendedSleepHours: ClosedRange<Double> {
        switch age {
        case 0..<6:
            return 10...13 // Preschoolers
        case 6..<13:
            return 9...11 // School age
        case 13..<18:
            return 8...10 // Teenagers
        case 18..<26:
            return 7...9 // Young adults
        case 26..<65:
            return 7...9 // Adults
        default:
            return 7...8 // Seniors (65+)
        }
    }
    
    var ageGroup: AgeGroup {
        switch age {
        case 0..<13:
            return .child
        case 13..<18:
            return .teenager
        case 18..<26:
            return .youngAdult
        case 26..<65:
            return .adult
        default:
            return .senior
        }
    }
    
    var sleepApneaRisk: SleepApneaRisk {
        // Risk factors: BMI > 30, age > 40, male sex
        var riskScore = 0
        
        if bmi > 30 {
            riskScore += 2
        } else if bmi > 25 {
            riskScore += 1
        }
        
        if age > 40 {
            riskScore += 1
        }
        
        if sex == .male {
            riskScore += 1
        }
        
        switch riskScore {
        case 0...1:
            return .low
        case 2...3:
            return .moderate
        default:
            return .high
        }
    }
    
    // MARK: - Initialization
    
    init(
        age: Int,
        sex: Sex,
        weightKg: Double,
        heightCm: Double,
        sleepGoalHours: Double = 8.0,
        preferredBedtime: Date = Calendar.current.date(from: DateComponents(hour: 22, minute: 0)) ?? Date(),
        preferredWakeTime: Date = Calendar.current.date(from: DateComponents(hour: 6, minute: 0)) ?? Date(),
        fitnessLevel: FitnessLevel = .moderate
    ) {
        self.age = age
        self.sex = sex
        self.weightKg = weightKg
        self.heightCm = heightCm
        self.sleepGoalHours = sleepGoalHours
        self.preferredBedtime = preferredBedtime
        self.preferredWakeTime = preferredWakeTime
        self.fitnessLevel = fitnessLevel
        self.profileCreatedDate = Date()
        self.lastUpdatedDate = Date()
    }
    
    // MARK: - Helper Methods
    
    func updateProfile(
        age: Int? = nil,
        sex: Sex? = nil,
        weightKg: Double? = nil,
        heightCm: Double? = nil,
        sleepGoalHours: Double? = nil,
        preferredBedtime: Date? = nil,
        preferredWakeTime: Date? = nil,
        fitnessLevel: FitnessLevel? = nil
    ) {
        if let age = age { self.age = age }
        if let sex = sex { self.sex = sex }
        if let weightKg = weightKg { self.weightKg = weightKg }
        if let heightCm = heightCm { self.heightCm = heightCm }
        if let sleepGoalHours = sleepGoalHours { self.sleepGoalHours = sleepGoalHours }
        if let preferredBedtime = preferredBedtime { self.preferredBedtime = preferredBedtime }
        if let preferredWakeTime = preferredWakeTime { self.preferredWakeTime = preferredWakeTime }
        if let fitnessLevel = fitnessLevel { self.fitnessLevel = fitnessLevel }
        
        self.lastUpdatedDate = Date()
    }
    
    func getPersonalizedRecommendation(for metric: String, value: Double) -> String {
        // Personalized recommendations based on user profile
        switch metric {
        case "sleep_duration":
            if value < recommendedSleepHours.lowerBound {
                return "You're getting \(String(format: "%.1f", recommendedSleepHours.lowerBound - value)) hours less than recommended for your age group (\(Int(age)) years). Try going to bed earlier."
            } else if value > recommendedSleepHours.upperBound {
                return "You're sleeping more than recommended. This might indicate underlying health issues. Consider consulting a doctor."
            } else {
                return "Great! You're within the recommended sleep range for your age group."
            }
            
        case "exercise":
            switch fitnessLevel {
            case .sedentary:
                return "Start with light exercise like 15-minute walks. Even small amounts can improve sleep quality."
            case .light:
                return "Aim for 30 minutes of moderate exercise, but finish 3-4 hours before bed."
            case .moderate:
                return "Your fitness level is good. Maintain your routine and avoid intense workouts close to bedtime."
            case .active:
                return "Great fitness level! Just ensure you're not overtraining, which can disrupt sleep."
            case .veryActive:
                return "Excellent fitness! Monitor for signs of overtraining and ensure adequate recovery time."
            }
            
        case "bmi":
            if sleepApneaRisk == .high {
                return "Your BMI (\(String(format: "%.1f", bmi))) puts you at higher risk for sleep apnea. Consider consulting a sleep specialist."
            } else if sleepApneaRisk == .moderate {
                return "Maintaining a healthy weight can improve sleep quality. Consider gradual weight management."
            } else {
                return "Your BMI is in a healthy range for good sleep quality."
            }
            
        default:
            return "Keep tracking your sleep to get personalized insights."
        }
    }
}

// MARK: - Enums

enum Sex: String, Codable, CaseIterable {
    case male = "Male"
    case female = "Female"
    case other = "Other"
    case preferNotToSay = "Prefer not to say"
}

enum FitnessLevel: String, Codable, CaseIterable {
    case sedentary = "Sedentary"
    case light = "Light Activity"
    case moderate = "Moderate Activity"
    case active = "Active"
    case veryActive = "Very Active"
    
    var description: String {
        switch self {
        case .sedentary:
            return "Little to no exercise"
        case .light:
            return "1-2 days/week"
        case .moderate:
            return "3-4 days/week"
        case .active:
            return "5-6 days/week"
        case .veryActive:
            return "Daily intense exercise"
        }
    }
}

enum AgeGroup: String, Codable {
    case child = "Child"
    case teenager = "Teenager"
    case youngAdult = "Young Adult"
    case adult = "Adult"
    case senior = "Senior"
}

enum BMICategory: String, Codable {
    case underweight = "Underweight"
    case normal = "Normal"
    case overweight = "Overweight"
    case obese = "Obese"
    
    var color: String {
        switch self {
        case .underweight:
            return "#FFA726" // Warning color
        case .normal:
            return "#66BB6A" // Success color
        case .overweight:
            return "#FFA726" // Warning color
        case .obese:
            return "#EF5350" // Error color
        }
    }
}

enum SleepApneaRisk: String, Codable {
    case low = "Low Risk"
    case moderate = "Moderate Risk"
    case high = "High Risk"
    
    var color: String {
        switch self {
        case .low:
            return "#66BB6A" // Success color
        case .moderate:
            return "#FFA726" // Warning color
        case .high:
            return "#EF5350" // Error color
        }
    }
    
    var recommendation: String {
        switch self {
        case .low:
            return "Your risk for sleep apnea is low. Continue maintaining a healthy lifestyle."
        case .moderate:
            return "You have moderate risk for sleep apnea. Monitor your sleep quality and consider weight management."
        case .high:
            return "You have elevated risk for sleep apnea. We recommend consulting a sleep specialist for evaluation."
        }
    }
}

