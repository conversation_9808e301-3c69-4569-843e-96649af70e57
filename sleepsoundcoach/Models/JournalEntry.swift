//
//  JournalEntry.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 30/09/2025.
//

import Foundation
import SwiftData

@Model
final class JournalEntry {
    var id: UUID
    var date: Date
    var content: String
    var mood: String // "Great", "Good", "Okay", "Poor", "Terrible"
    var energyLevel: Int // 1-5 scale
    var sentimentScore: Double // -1 to 1, from NaturalLanguage analysis
    
    // Correlations
    var sleepQualityPreviousNight: String?
    var disturbancesPreviousNight: Int?
    
    init(date: Date = Date(), content: String = "", mood: String = "Okay", energyLevel: Int = 3) {
        self.id = UUID()
        self.date = date
        self.content = content
        self.mood = mood
        self.energyLevel = energyLevel
        self.sentimentScore = 0.0
    }
    
    var moodEmoji: String {
        switch mood {
        case "Great": return "😄"
        case "Good": return "🙂"
        case "Okay": return "😐"
        case "Poor": return "😕"
        case "Terrible": return "😞"
        default: return "😐"
        }
    }
}

