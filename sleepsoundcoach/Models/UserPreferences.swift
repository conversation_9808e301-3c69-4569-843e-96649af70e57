//
//  UserPreferences.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 30/09/2025.
//

import Foundation
import SwiftData

@Model
final class UserPreferences {
    var id: UUID
    var hasCompletedOnboarding: Bool
    var hasGrantedMicrophonePermission: Bool
    var hasGrantedHealthKitPermission: Bool
    
    // User settings
    var enableSleepTalkingTranscription: Bool
    var enableAudioRecording: Bool
    var enableHealthKitIntegration: Bool
    var enableNotifications: Bool
    
    // Subscription
    var isPremiumUser: Bool
    var subscriptionExpiryDate: Date?
    
    // Preferences
    var preferredBedtime: Date?
    var preferredWakeTime: Date?
    var sleepGoalHours: Double
    
    // Privacy
    var allowDataAnalytics: Bool
    
    init() {
        self.id = UUID()
        self.hasCompletedOnboarding = false
        self.hasGrantedMicrophonePermission = false
        self.hasGrantedHealthKitPermission = false
        
        self.enableSleepTalkingTranscription = true
        self.enableAudioRecording = false
        self.enableHealthKitIntegration = true
        self.enableNotifications = true
        
        self.isPremiumUser = false
        self.subscriptionExpiryDate = nil
        
        self.sleepGoalHours = 8.0
        self.allowDataAnalytics = false
    }
}

