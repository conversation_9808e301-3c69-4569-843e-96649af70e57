//
//  SleepSession.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 30/09/2025.
//

import Foundation
import SwiftData

@Model
final class SleepSession {
    var id: UUID
    var startTime: Date
    var endTime: Date?
    var isActive: Bool
    var isFavorite: Bool // NEW: Mark sessions as favorites
    
    // Sound event statistics
    var totalSnoreEvents: Int
    var totalCoughEvents: Int
    var totalSleepTalkEvents: Int
    var totalOtherEvents: Int
    
    var averageSnoreDuration: Double
    var maxSnoreDuration: Double
    var snoreSeverityIndex: Double // 0-100 scale
    
    // HealthKit correlation data
    var averageHeartRate: Double?
    var deepSleepMinutes: Double?
    var remSleepMinutes: Double?
    var lightSleepMinutes: Double?
    var oxygenSaturation: Double?

    // NEW: Additional HealthKit metrics
    var heartRateVariability: Double?  // HRV in milliseconds
    var respiratoryRate: Double?  // Breaths per minute
    var bodyTemperature: Double?  // Fahrenheit
    var exerciseMinutes: Double?  // Exercise time that day
    var mindfulMinutes: Double?  // Meditation time that day
    var caffeineIntake: Double?  // Milligrams
    var waterIntake: Double?  // Milliliters
    
    // Calculated metrics
    var quietScore: Int // 0-100, higher is better
    var totalDisturbances: Int
    var sleepQuality: String // "Excellent", "Good", "Fair", "Poor"
    
    // Relationships
    @Relationship(deleteRule: .cascade, inverse: \SoundEvent.session)
    var soundEvents: [SoundEvent]?
    
    init(startTime: Date = Date()) {
        self.id = UUID()
        self.startTime = startTime
        self.endTime = nil
        self.isActive = true
        self.isFavorite = false
        
        self.totalSnoreEvents = 0
        self.totalCoughEvents = 0
        self.totalSleepTalkEvents = 0
        self.totalOtherEvents = 0
        
        self.averageSnoreDuration = 0
        self.maxSnoreDuration = 0
        self.snoreSeverityIndex = 0
        
        self.quietScore = 100
        self.totalDisturbances = 0
        self.sleepQuality = "Excellent"
        
        self.soundEvents = []
    }
    
    func endSession() {
        self.endTime = Date()
        self.isActive = false
        calculateMetrics()
    }
    
    func calculateMetrics() {
        guard let events = soundEvents else { return }
        
        // Count events by type
        totalSnoreEvents = events.filter { $0.eventType == .snoring }.count
        totalCoughEvents = events.filter { $0.eventType == .coughing }.count
        totalSleepTalkEvents = events.filter { $0.eventType == .sleepTalking }.count
        totalOtherEvents = events.filter { $0.eventType == .other }.count
        
        totalDisturbances = events.count
        
        // Calculate snore metrics
        let snoreEvents = events.filter { $0.eventType == .snoring }
        if !snoreEvents.isEmpty {
            averageSnoreDuration = snoreEvents.map { $0.duration }.reduce(0, +) / Double(snoreEvents.count)
            maxSnoreDuration = snoreEvents.map { $0.duration }.max() ?? 0
            
            // Severity index based on frequency and duration
            let totalSnoreDuration = snoreEvents.map { $0.duration }.reduce(0, +)
            let sessionDuration = (endTime ?? Date()).timeIntervalSince(startTime) / 3600.0 // hours
            snoreSeverityIndex = min(100, (totalSnoreDuration / sessionDuration) * 10)
        }
        
        // Calculate quiet score (inverse of disturbances)
        let sessionHours = max(1, (endTime ?? Date()).timeIntervalSince(startTime) / 3600.0)
        let disturbancesPerHour = Double(totalDisturbances) / sessionHours
        quietScore = max(0, min(100, Int(100 - (disturbancesPerHour * 10))))
        
        // Determine sleep quality
        if quietScore >= 80 {
            sleepQuality = "Excellent"
        } else if quietScore >= 60 {
            sleepQuality = "Good"
        } else if quietScore >= 40 {
            sleepQuality = "Fair"
        } else {
            sleepQuality = "Poor"
        }
    }
    
    var duration: TimeInterval {
        guard let end = endTime else {
            return Date().timeIntervalSince(startTime)
        }
        return end.timeIntervalSince(startTime)
    }
    
    var formattedDuration: String {
        let hours = Int(duration) / 3600
        let minutes = (Int(duration) % 3600) / 60
        return "\(hours)h \(minutes)m"
    }
}

