//
//  ContentView.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 30/09/2025.
//

import SwiftUI
import SwiftData

struct ContentView: View {
    @Environment(\.modelContext) private var modelContext
    @Query private var preferences: [UserPreferences]
    @State private var showSplash = true

    var body: some View {
        ZStack {
            Group {
                if shouldShowOnboarding {
                    ModernOnboardingView {
                        completeOnboarding()
                    }
                } else {
                    MainTabView()
                }
            }

            if showSplash {
                SplashScreen(isActive: $showSplash)
                    .transition(.opacity)
                    .zIndex(1)
            }
        }
    }

    private var shouldShowOnboarding: Bool {
        if let prefs = preferences.first {
            return !prefs.hasCompletedOnboarding
        }
        return true
    }

    private func completeOnboarding() {
        if let prefs = preferences.first {
            prefs.hasCompletedOnboarding = true
        } else {
            let newPrefs = UserPreferences()
            newPrefs.hasCompletedOnboarding = true
            modelContext.insert(newPrefs)
        }
        try? modelContext.save()
    }
}

#Preview {
    ContentView()
        .modelContainer(for: SleepSession.self, inMemory: true)
}
