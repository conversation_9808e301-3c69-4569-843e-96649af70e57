//
//  HealthKitService.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 30/09/2025.
//

import Foundation
import HealthKit

@Observable
class HealthKitService {
    var isAuthorized = false
    private let healthStore = HKHealthStore()
    
    func requestAuthorization() async -> Bool {
        print("🏥 ========== REQUESTING HEALTHKIT AUTHORIZATION ==========")

        guard HKHealthStore.isHealthDataAvailable() else {
            print("   ❌ HealthKit not available on this device")
            return false
        }

        print("   📋 Requesting access to comprehensive health metrics...")

        // Comprehensive list of health metrics to read
        var typesToRead: Set<HKObjectType> = []

        // Sleep metrics
        if let sleepAnalysis = HKObjectType.categoryType(forIdentifier: .sleepAnalysis) {
            typesToRead.insert(sleepAnalysis)
            print("   ✅ Sleep Analysis")
        }

        // Heart metrics
        if let heartRate = HKObjectType.quantityType(forIdentifier: .heartRate) {
            typesToRead.insert(heartRate)
            print("   ✅ Heart Rate")
        }
        if let restingHeartRate = HKObjectType.quantityType(forIdentifier: .restingHeartRate) {
            typesToRead.insert(restingHeartRate)
            print("   ✅ Resting Heart Rate")
        }
        if let heartRateVariability = HKObjectType.quantityType(forIdentifier: .heartRateVariabilitySDNN) {
            typesToRead.insert(heartRateVariability)
            print("   ✅ Heart Rate Variability (HRV)")
        }

        // Respiratory metrics
        if let oxygenSaturation = HKObjectType.quantityType(forIdentifier: .oxygenSaturation) {
            typesToRead.insert(oxygenSaturation)
            print("   ✅ Oxygen Saturation")
        }
        if let respiratoryRate = HKObjectType.quantityType(forIdentifier: .respiratoryRate) {
            typesToRead.insert(respiratoryRate)
            print("   ✅ Respiratory Rate")
        }

        // Body metrics
        if let bodyTemperature = HKObjectType.quantityType(forIdentifier: .bodyTemperature) {
            typesToRead.insert(bodyTemperature)
            print("   ✅ Body Temperature")
        }

        // Activity metrics
        if let activeEnergy = HKObjectType.quantityType(forIdentifier: .activeEnergyBurned) {
            typesToRead.insert(activeEnergy)
            print("   ✅ Active Energy Burned")
        }
        if let exerciseTime = HKObjectType.quantityType(forIdentifier: .appleExerciseTime) {
            typesToRead.insert(exerciseTime)
            print("   ✅ Exercise Time")
        }

        // Mindfulness
        if let mindfulMinutes = HKObjectType.categoryType(forIdentifier: .mindfulSession) {
            typesToRead.insert(mindfulMinutes)
            print("   ✅ Mindful Minutes")
        }

        // Nutrition (if user logs)
        if let caffeine = HKObjectType.quantityType(forIdentifier: .dietaryCaffeine) {
            typesToRead.insert(caffeine)
            print("   ✅ Caffeine Intake")
        }
        if let water = HKObjectType.quantityType(forIdentifier: .dietaryWater) {
            typesToRead.insert(water)
            print("   ✅ Water Intake")
        }

        print("   📊 Total metrics requested: \(typesToRead.count)")

        do {
            try await healthStore.requestAuthorization(toShare: [], read: typesToRead)
            isAuthorized = true
            print("   ✅ HealthKit authorization granted")
            print("=========================================")
            return true
        } catch {
            print("   ❌ HealthKit authorization failed: \(error.localizedDescription)")
            print("=========================================")
            return false
        }
    }
    
    func fetchSleepData(for date: Date) async -> (deepSleep: Double, remSleep: Double, lightSleep: Double)? {
        guard isAuthorized else { return nil }
        
        let sleepType = HKObjectType.categoryType(forIdentifier: .sleepAnalysis)!
        
        let startOfDay = Calendar.current.startOfDay(for: date)
        let endOfDay = Calendar.current.date(byAdding: .day, value: 1, to: startOfDay)!
        
        let predicate = HKQuery.predicateForSamples(withStart: startOfDay, end: endOfDay, options: .strictStartDate)
        
        return await withCheckedContinuation { continuation in
            let query = HKSampleQuery(sampleType: sleepType, predicate: predicate, limit: HKObjectQueryNoLimit, sortDescriptors: nil) { _, samples, error in
                guard let samples = samples as? [HKCategorySample], error == nil else {
                    continuation.resume(returning: nil)
                    return
                }
                
                var deepSleep: Double = 0
                var remSleep: Double = 0
                var lightSleep: Double = 0
                
                for sample in samples {
                    let duration = sample.endDate.timeIntervalSince(sample.startDate) / 60.0 // minutes
                    
                    switch sample.value {
                    case HKCategoryValueSleepAnalysis.asleepDeep.rawValue:
                        deepSleep += duration
                    case HKCategoryValueSleepAnalysis.asleepREM.rawValue:
                        remSleep += duration
                    case HKCategoryValueSleepAnalysis.asleepCore.rawValue,
                         HKCategoryValueSleepAnalysis.asleepUnspecified.rawValue:
                        lightSleep += duration
                    default:
                        break
                    }
                }
                
                continuation.resume(returning: (deepSleep, remSleep, lightSleep))
            }
            
            healthStore.execute(query)
        }
    }
    
    func fetchAverageHeartRate(from startDate: Date, to endDate: Date) async -> Double? {
        guard isAuthorized else { return nil }
        
        let heartRateType = HKQuantityType.quantityType(forIdentifier: .heartRate)!
        let predicate = HKQuery.predicateForSamples(withStart: startDate, end: endDate, options: .strictStartDate)
        
        return await withCheckedContinuation { continuation in
            let query = HKStatisticsQuery(quantityType: heartRateType, quantitySamplePredicate: predicate, options: .discreteAverage) { _, statistics, error in
                guard let statistics = statistics, error == nil else {
                    continuation.resume(returning: nil)
                    return
                }
                
                let averageHeartRate = statistics.averageQuantity()?.doubleValue(for: HKUnit.count().unitDivided(by: .minute()))
                continuation.resume(returning: averageHeartRate)
            }
            
            healthStore.execute(query)
        }
    }
    
    func fetchOxygenSaturation(from startDate: Date, to endDate: Date) async -> Double? {
        guard isAuthorized else { return nil }
        
        let oxygenType = HKQuantityType.quantityType(forIdentifier: .oxygenSaturation)!
        let predicate = HKQuery.predicateForSamples(withStart: startDate, end: endDate, options: .strictStartDate)
        
        return await withCheckedContinuation { continuation in
            let query = HKStatisticsQuery(quantityType: oxygenType, quantitySamplePredicate: predicate, options: .discreteAverage) { _, statistics, error in
                guard let statistics = statistics, error == nil else {
                    continuation.resume(returning: nil)
                    return
                }
                
                let avgOxygen = statistics.averageQuantity()?.doubleValue(for: HKUnit.percent())
                continuation.resume(returning: avgOxygen)
            }
            
            healthStore.execute(query)
        }
    }

    // MARK: - Heart Rate Variability (HRV)

    func fetchHeartRateVariability(for date: Date) async -> Double? {
        guard isAuthorized else { return nil }

        guard let hrvType = HKObjectType.quantityType(forIdentifier: .heartRateVariabilitySDNN) else {
            return nil
        }

        let startOfDay = Calendar.current.startOfDay(for: date)
        let endOfDay = Calendar.current.date(byAdding: .day, value: 1, to: startOfDay)!
        let predicate = HKQuery.predicateForSamples(withStart: startOfDay, end: endOfDay, options: .strictStartDate)

        return await withCheckedContinuation { continuation in
            let query = HKStatisticsQuery(quantityType: hrvType, quantitySamplePredicate: predicate, options: .discreteAverage) { _, statistics, error in
                guard let statistics = statistics, error == nil else {
                    continuation.resume(returning: nil)
                    return
                }

                let avgHRV = statistics.averageQuantity()?.doubleValue(for: HKUnit.secondUnit(with: .milli))
                continuation.resume(returning: avgHRV)
            }

            healthStore.execute(query)
        }
    }

    // MARK: - Respiratory Rate

    func fetchRespiratoryRate(for date: Date) async -> Double? {
        guard isAuthorized else { return nil }

        guard let respiratoryType = HKObjectType.quantityType(forIdentifier: .respiratoryRate) else {
            return nil
        }

        let startOfDay = Calendar.current.startOfDay(for: date)
        let endOfDay = Calendar.current.date(byAdding: .day, value: 1, to: startOfDay)!
        let predicate = HKQuery.predicateForSamples(withStart: startOfDay, end: endOfDay, options: .strictStartDate)

        return await withCheckedContinuation { continuation in
            let query = HKStatisticsQuery(quantityType: respiratoryType, quantitySamplePredicate: predicate, options: .discreteAverage) { _, statistics, error in
                guard let statistics = statistics, error == nil else {
                    continuation.resume(returning: nil)
                    return
                }

                let avgRespRate = statistics.averageQuantity()?.doubleValue(for: HKUnit.count().unitDivided(by: .minute()))
                continuation.resume(returning: avgRespRate)
            }

            healthStore.execute(query)
        }
    }

    // MARK: - Body Temperature

    func fetchBodyTemperature(for date: Date) async -> Double? {
        guard isAuthorized else { return nil }

        guard let tempType = HKObjectType.quantityType(forIdentifier: .bodyTemperature) else {
            return nil
        }

        let startOfDay = Calendar.current.startOfDay(for: date)
        let endOfDay = Calendar.current.date(byAdding: .day, value: 1, to: startOfDay)!
        let predicate = HKQuery.predicateForSamples(withStart: startOfDay, end: endOfDay, options: .strictStartDate)

        return await withCheckedContinuation { continuation in
            let query = HKStatisticsQuery(quantityType: tempType, quantitySamplePredicate: predicate, options: .discreteAverage) { _, statistics, error in
                guard let statistics = statistics, error == nil else {
                    continuation.resume(returning: nil)
                    return
                }

                let avgTemp = statistics.averageQuantity()?.doubleValue(for: HKUnit.degreeFahrenheit())
                continuation.resume(returning: avgTemp)
            }

            healthStore.execute(query)
        }
    }

    // MARK: - Exercise Minutes

    func fetchExerciseMinutes(for date: Date) async -> Double? {
        guard isAuthorized else { return nil }

        guard let exerciseType = HKObjectType.quantityType(forIdentifier: .appleExerciseTime) else {
            return nil
        }

        let startOfDay = Calendar.current.startOfDay(for: date)
        let endOfDay = Calendar.current.date(byAdding: .day, value: 1, to: startOfDay)!
        let predicate = HKQuery.predicateForSamples(withStart: startOfDay, end: endOfDay, options: .strictStartDate)

        return await withCheckedContinuation { continuation in
            let query = HKStatisticsQuery(quantityType: exerciseType, quantitySamplePredicate: predicate, options: .cumulativeSum) { _, statistics, error in
                guard let statistics = statistics, error == nil else {
                    continuation.resume(returning: nil)
                    return
                }

                let totalMinutes = statistics.sumQuantity()?.doubleValue(for: .minute())
                continuation.resume(returning: totalMinutes)
            }

            healthStore.execute(query)
        }
    }

    // MARK: - Mindful Minutes

    func fetchMindfulMinutes(for date: Date) async -> Double? {
        guard isAuthorized else { return nil }

        guard let mindfulType = HKObjectType.categoryType(forIdentifier: .mindfulSession) else {
            return nil
        }

        let startOfDay = Calendar.current.startOfDay(for: date)
        let endOfDay = Calendar.current.date(byAdding: .day, value: 1, to: startOfDay)!
        let predicate = HKQuery.predicateForSamples(withStart: startOfDay, end: endOfDay, options: .strictStartDate)

        return await withCheckedContinuation { continuation in
            let query = HKSampleQuery(sampleType: mindfulType, predicate: predicate, limit: HKObjectQueryNoLimit, sortDescriptors: nil) { _, samples, error in
                guard let samples = samples, error == nil else {
                    continuation.resume(returning: nil)
                    return
                }

                let totalMinutes = samples.reduce(0.0) { total, sample in
                    total + sample.endDate.timeIntervalSince(sample.startDate) / 60.0
                }

                continuation.resume(returning: totalMinutes)
            }

            healthStore.execute(query)
        }
    }

    // MARK: - Caffeine Intake

    func fetchCaffeineIntake(for date: Date) async -> Double? {
        guard isAuthorized else { return nil }

        guard let caffeineType = HKObjectType.quantityType(forIdentifier: .dietaryCaffeine) else {
            return nil
        }

        let startOfDay = Calendar.current.startOfDay(for: date)
        let endOfDay = Calendar.current.date(byAdding: .day, value: 1, to: startOfDay)!
        let predicate = HKQuery.predicateForSamples(withStart: startOfDay, end: endOfDay, options: .strictStartDate)

        return await withCheckedContinuation { continuation in
            let query = HKStatisticsQuery(quantityType: caffeineType, quantitySamplePredicate: predicate, options: .cumulativeSum) { _, statistics, error in
                guard let statistics = statistics, error == nil else {
                    continuation.resume(returning: nil)
                    return
                }

                let totalCaffeine = statistics.sumQuantity()?.doubleValue(for: .gramUnit(with: .milli))
                continuation.resume(returning: totalCaffeine)
            }

            healthStore.execute(query)
        }
    }

    // MARK: - Water Intake

    func fetchWaterIntake(for date: Date) async -> Double? {
        guard isAuthorized else { return nil }

        guard let waterType = HKObjectType.quantityType(forIdentifier: .dietaryWater) else {
            return nil
        }

        let startOfDay = Calendar.current.startOfDay(for: date)
        let endOfDay = Calendar.current.date(byAdding: .day, value: 1, to: startOfDay)!
        let predicate = HKQuery.predicateForSamples(withStart: startOfDay, end: endOfDay, options: .strictStartDate)

        return await withCheckedContinuation { continuation in
            let query = HKStatisticsQuery(quantityType: waterType, quantitySamplePredicate: predicate, options: .cumulativeSum) { _, statistics, error in
                guard let statistics = statistics, error == nil else {
                    continuation.resume(returning: nil)
                    return
                }

                let totalWater = statistics.sumQuantity()?.doubleValue(for: .literUnit(with: .milli))
                continuation.resume(returning: totalWater)
            }

            healthStore.execute(query)
        }
    }

    // MARK: - Comprehensive Health Data

    /// Fetch all available health metrics for a given date
    func fetchComprehensiveHealthData(for date: Date) async -> ComprehensiveHealthData {
        print("🏥 Fetching comprehensive health data for \(date)...")

        let startOfDay = Calendar.current.startOfDay(for: date)
        let endOfDay = Calendar.current.date(byAdding: .day, value: 1, to: startOfDay)!

        async let sleepData = fetchSleepData(for: date)
        async let heartRate = fetchAverageHeartRate(from: startOfDay, to: endOfDay)
        async let oxygen = fetchOxygenSaturation(from: startOfDay, to: endOfDay)
        async let hrv = fetchHeartRateVariability(for: date)
        async let respiratoryRate = fetchRespiratoryRate(for: date)
        async let bodyTemp = fetchBodyTemperature(for: date)
        async let exercise = fetchExerciseMinutes(for: date)
        async let mindful = fetchMindfulMinutes(for: date)
        async let caffeine = fetchCaffeineIntake(for: date)
        async let water = fetchWaterIntake(for: date)

        let sleep = await sleepData

        let data = ComprehensiveHealthData(
            deepSleepMinutes: sleep?.deepSleep,
            remSleepMinutes: sleep?.remSleep,
            lightSleepMinutes: sleep?.lightSleep,
            averageHeartRate: await heartRate,
            oxygenSaturation: await oxygen,
            heartRateVariability: await hrv,
            respiratoryRate: await respiratoryRate,
            bodyTemperature: await bodyTemp,
            exerciseMinutes: await exercise,
            mindfulMinutes: await mindful,
            caffeineIntake: await caffeine,
            waterIntake: await water
        )

        print("✅ Health data fetched:")
        if let hrv = data.heartRateVariability {
            print("   💓 HRV: \(String(format: "%.1f", hrv)) ms")
        }
        if let respRate = data.respiratoryRate {
            print("   🫁 Respiratory Rate: \(String(format: "%.1f", respRate)) breaths/min")
        }
        if let temp = data.bodyTemperature {
            print("   🌡️ Body Temp: \(String(format: "%.1f", temp))°F")
        }
        if let exercise = data.exerciseMinutes {
            print("   🏃 Exercise: \(String(format: "%.0f", exercise)) min")
        }
        if let mindful = data.mindfulMinutes {
            print("   🧘 Mindful: \(String(format: "%.0f", mindful)) min")
        }
        if let caffeine = data.caffeineIntake {
            print("   ☕ Caffeine: \(String(format: "%.0f", caffeine)) mg")
        }

        return data
    }
}

// MARK: - Comprehensive Health Data Model

struct ComprehensiveHealthData {
    let deepSleepMinutes: Double?
    let remSleepMinutes: Double?
    let lightSleepMinutes: Double?
    let averageHeartRate: Double?
    let oxygenSaturation: Double?
    let heartRateVariability: Double?  // HRV in milliseconds
    let respiratoryRate: Double?  // Breaths per minute
    let bodyTemperature: Double?  // Fahrenheit
    let exerciseMinutes: Double?
    let mindfulMinutes: Double?
    let caffeineIntake: Double?  // Milligrams
    let waterIntake: Double?  // Milliliters

    var totalSleepMinutes: Double? {
        guard let deep = deepSleepMinutes,
              let rem = remSleepMinutes,
              let light = lightSleepMinutes else {
            return nil
        }
        return deep + rem + light
    }

    var sleepEfficiency: Double? {
        guard let total = totalSleepMinutes, total > 0 else { return nil }
        // Assuming 8 hours in bed
        return (total / 480.0) * 100.0
    }

    var hasAnyData: Bool {
        return deepSleepMinutes != nil ||
               remSleepMinutes != nil ||
               lightSleepMinutes != nil ||
               averageHeartRate != nil ||
               oxygenSaturation != nil ||
               heartRateVariability != nil ||
               respiratoryRate != nil ||
               bodyTemperature != nil ||
               exerciseMinutes != nil ||
               mindfulMinutes != nil ||
               caffeineIntake != nil ||
               waterIntake != nil
    }
}

