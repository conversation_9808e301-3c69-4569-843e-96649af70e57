//
//  HealthCorrelationService.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 01/10/2025.
//

import Foundation
import SwiftData

@Observable
class HealthCorrelationService {
    
    // MARK: - Correlation Analysis
    
    /// Analyze correlation between exercise and sleep quality
    func analyzeExerciseCorrelation(sessions: [SleepSession]) -> CorrelationInsight? {
        let sessionsWithExercise = sessions.filter { $0.exerciseMinutes != nil && $0.exerciseMinutes! > 0 }
        guard sessionsWithExercise.count >= 5 else { return nil }
        
        let withExercise = sessionsWithExercise.map { Double($0.quietScore) }
        let withoutExercise = sessions.filter { $0.exerciseMinutes == nil || $0.exerciseMinutes! == 0 }.map { Double($0.quietScore) }
        
        guard !withExercise.isEmpty && !withoutExercise.isEmpty else { return nil }
        
        let avgWithExercise = withExercise.reduce(0, +) / Double(withExercise.count)
        let avgWithoutExercise = withoutExercise.reduce(0, +) / Double(withoutExercise.count)
        let improvement = ((avgWithExercise - avgWithoutExercise) / avgWithoutExercise) * 100
        
        guard abs(improvement) > 5 else { return nil } // Only show if >5% difference
        
        let optimalMinutes = calculateOptimalExerciseMinutes(sessions: sessionsWithExercise)
        
        return CorrelationInsight(
            type: .exercise,
            correlation: improvement > 0 ? .positive : .negative,
            strength: abs(improvement),
            message: improvement > 0 
                ? "Your sleep quality improves by \(String(format: "%.0f", improvement))% when you exercise for \(optimalMinutes)+ minutes"
                : "Exercise close to bedtime may be affecting your sleep quality",
            recommendation: improvement > 0
                ? "Try to exercise for at least \(optimalMinutes) minutes, but finish 3-4 hours before bed"
                : "Consider exercising earlier in the day, at least 4 hours before bedtime"
        )
    }
    
    /// Analyze correlation between caffeine intake and sleep
    func analyzeCaffeineCorrelation(sessions: [SleepSession]) -> CorrelationInsight? {
        let sessionsWithCaffeine = sessions.filter { $0.caffeineIntake != nil && $0.caffeineIntake! > 0 }
        guard sessionsWithCaffeine.count >= 5 else { return nil }
        
        let highCaffeine = sessionsWithCaffeine.filter { $0.caffeineIntake! > 200 }
        let lowCaffeine = sessionsWithCaffeine.filter { $0.caffeineIntake! <= 200 }
        
        guard !highCaffeine.isEmpty && !lowCaffeine.isEmpty else { return nil }
        
        let avgHighCaffeine = highCaffeine.map { Double($0.quietScore) }.reduce(0, +) / Double(highCaffeine.count)
        let avgLowCaffeine = lowCaffeine.map { Double($0.quietScore) }.reduce(0, +) / Double(lowCaffeine.count)
        let impact = ((avgLowCaffeine - avgHighCaffeine) / avgHighCaffeine) * 100
        
        guard abs(impact) > 5 else { return nil }
        
        return CorrelationInsight(
            type: .caffeine,
            correlation: impact > 0 ? .negative : .positive,
            strength: abs(impact),
            message: impact > 0
                ? "High caffeine intake (>200mg) reduces your sleep quality by \(String(format: "%.0f", impact))%"
                : "Your sleep quality is not significantly affected by caffeine",
            recommendation: impact > 0
                ? "Try limiting caffeine to <200mg per day and avoid it after 2 PM"
                : "Your current caffeine intake seems fine, but monitor timing"
        )
    }
    
    /// Analyze correlation between HRV and sleep quality
    func analyzeHRVCorrelation(sessions: [SleepSession]) -> CorrelationInsight? {
        let sessionsWithHRV = sessions.filter { $0.heartRateVariability != nil }
        guard sessionsWithHRV.count >= 7 else { return nil }
        
        let avgHRV = sessionsWithHRV.map { $0.heartRateVariability! }.reduce(0, +) / Double(sessionsWithHRV.count)
        let highHRV = sessionsWithHRV.filter { $0.heartRateVariability! > avgHRV }
        let lowHRV = sessionsWithHRV.filter { $0.heartRateVariability! <= avgHRV }
        
        let avgQualityHighHRV = highHRV.map { Double($0.quietScore) }.reduce(0, +) / Double(highHRV.count)
        let avgQualityLowHRV = lowHRV.map { Double($0.quietScore) }.reduce(0, +) / Double(lowHRV.count)
        let correlation = ((avgQualityHighHRV - avgQualityLowHRV) / avgQualityLowHRV) * 100
        
        guard abs(correlation) > 5 else { return nil }
        
        return CorrelationInsight(
            type: .hrv,
            correlation: correlation > 0 ? .positive : .negative,
            strength: abs(correlation),
            message: "Higher HRV correlates with \(String(format: "%.0f", correlation))% better sleep quality",
            recommendation: "Focus on stress reduction, meditation, and recovery to improve HRV"
        )
    }
    
    /// Analyze correlation between mindfulness and sleep
    func analyzeMindfulnessCorrelation(sessions: [SleepSession]) -> CorrelationInsight? {
        let sessionsWithMindful = sessions.filter { $0.mindfulMinutes != nil && $0.mindfulMinutes! > 0 }
        guard sessionsWithMindful.count >= 5 else { return nil }
        
        let withMindfulness = sessionsWithMindful.map { Double($0.quietScore) }
        let withoutMindfulness = sessions.filter { $0.mindfulMinutes == nil || $0.mindfulMinutes! == 0 }.map { Double($0.quietScore) }
        
        guard !withMindfulness.isEmpty && !withoutMindfulness.isEmpty else { return nil }
        
        let avgWith = withMindfulness.reduce(0, +) / Double(withMindfulness.count)
        let avgWithout = withoutMindfulness.reduce(0, +) / Double(withoutMindfulness.count)
        let improvement = ((avgWith - avgWithout) / avgWithout) * 100
        
        guard abs(improvement) > 5 else { return nil }
        
        return CorrelationInsight(
            type: .mindfulness,
            correlation: improvement > 0 ? .positive : .negative,
            strength: abs(improvement),
            message: "Meditation improves your sleep quality by \(String(format: "%.0f", improvement))%",
            recommendation: "Try meditating for 10-15 minutes before bed for better sleep"
        )
    }
    
    /// Get all correlation insights
    func getAllCorrelations(sessions: [SleepSession]) -> [CorrelationInsight] {
        var insights: [CorrelationInsight] = []
        
        if let exercise = analyzeExerciseCorrelation(sessions: sessions) {
            insights.append(exercise)
        }
        if let caffeine = analyzeCaffeineCorrelation(sessions: sessions) {
            insights.append(caffeine)
        }
        if let hrv = analyzeHRVCorrelation(sessions: sessions) {
            insights.append(hrv)
        }
        if let mindfulness = analyzeMindfulnessCorrelation(sessions: sessions) {
            insights.append(mindfulness)
        }
        
        return insights.sorted { $0.strength > $1.strength }
    }
    
    // MARK: - Helper Methods
    
    private func calculateOptimalExerciseMinutes(sessions: [SleepSession]) -> Int {
        let grouped = Dictionary(grouping: sessions) { session -> Int in
            let minutes = session.exerciseMinutes ?? 0
            return Int(minutes / 15) * 15 // Group by 15-minute intervals
        }
        
        let bestGroup = grouped.max { a, b in
            let avgA = a.value.map { Double($0.quietScore) }.reduce(0, +) / Double(a.value.count)
            let avgB = b.value.map { Double($0.quietScore) }.reduce(0, +) / Double(b.value.count)
            return avgA < avgB
        }
        
        return bestGroup?.key ?? 30
    }
}

// MARK: - Models

struct CorrelationInsight: Identifiable {
    let id = UUID()
    let type: CorrelationType
    let correlation: CorrelationDirection
    let strength: Double // Percentage
    let message: String
    let recommendation: String
    
    var icon: String {
        switch type {
        case .exercise: return "figure.run"
        case .caffeine: return "cup.and.saucer.fill"
        case .hrv: return "heart.text.square.fill"
        case .mindfulness: return "brain.head.profile"
        case .hydration: return "drop.fill"
        case .temperature: return "thermometer"
        }
    }
    
    var color: String {
        switch correlation {
        case .positive: return "34C759" // Green
        case .negative: return "FF453A" // Red
        case .neutral: return "FFD60A" // Yellow
        }
    }
}

enum CorrelationType {
    case exercise
    case caffeine
    case hrv
    case mindfulness
    case hydration
    case temperature
}

enum CorrelationDirection {
    case positive
    case negative
    case neutral
}

