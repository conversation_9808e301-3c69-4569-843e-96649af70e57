//
//  ExportService.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 01/10/2025.
//

import Foundation
import PDFKit
import UIKit

class ExportService {
    
    // MARK: - PDF Export
    
    func generatePDFReport(sessions: [SleepSession]) -> URL? {
        let pdfMetaData = [
            kCGPDFContextCreator: "SleepSound Coach",
            kCGPDFContextAuthor: "SleepSound Coach App",
            kCGPDFContextTitle: "Sleep Report"
        ]
        
        let format = UIGraphicsPDFRendererFormat()
        format.documentInfo = pdfMetaData as [String: Any]
        
        let pageWidth = 8.5 * 72.0
        let pageHeight = 11 * 72.0
        let pageRect = CGRect(x: 0, y: 0, width: pageWidth, height: pageHeight)
        
        let renderer = UIGraphicsPDFRenderer(bounds: pageRect, format: format)
        
        let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent("SleepReport.pdf")
        
        do {
            try renderer.writePDF(to: tempURL) { context in
                context.beginPage()
                
                var yPosition: CGFloat = 50
                
                // Title
                let titleAttributes: [NSAttributedString.Key: Any] = [
                    .font: UIFont.boldSystemFont(ofSize: 24),
                    .foregroundColor: UIColor.black
                ]
                let title = "Sleep Report"
                title.draw(at: CGPoint(x: 50, y: yPosition), withAttributes: titleAttributes)
                yPosition += 40
                
                // Date range
                let dateFormatter = DateFormatter()
                dateFormatter.dateStyle = .medium
                
                if let firstSession = sessions.last, let lastSession = sessions.first {
                    let dateRange = "\(dateFormatter.string(from: firstSession.startTime)) - \(dateFormatter.string(from: lastSession.startTime))"
                    let dateAttributes: [NSAttributedString.Key: Any] = [
                        .font: UIFont.systemFont(ofSize: 12),
                        .foregroundColor: UIColor.gray
                    ]
                    dateRange.draw(at: CGPoint(x: 50, y: yPosition), withAttributes: dateAttributes)
                    yPosition += 30
                }
                
                // Summary statistics
                let avgScore = sessions.map { $0.quietScore }.reduce(0, +) / max(1, sessions.count)
                let totalSessions = sessions.count
                let totalSnores = sessions.map { $0.totalSnoreEvents }.reduce(0, +)
                
                let summaryAttributes: [NSAttributedString.Key: Any] = [
                    .font: UIFont.systemFont(ofSize: 14),
                    .foregroundColor: UIColor.black
                ]
                
                "Summary Statistics".draw(at: CGPoint(x: 50, y: yPosition), withAttributes: titleAttributes)
                yPosition += 30
                
                "Total Sessions: \(totalSessions)".draw(at: CGPoint(x: 50, y: yPosition), withAttributes: summaryAttributes)
                yPosition += 20
                
                "Average Quiet Score: \(avgScore)".draw(at: CGPoint(x: 50, y: yPosition), withAttributes: summaryAttributes)
                yPosition += 20
                
                "Total Snoring Events: \(totalSnores)".draw(at: CGPoint(x: 50, y: yPosition), withAttributes: summaryAttributes)
                yPosition += 40
                
                // Session details
                "Session Details".draw(at: CGPoint(x: 50, y: yPosition), withAttributes: titleAttributes)
                yPosition += 30
                
                for session in sessions.prefix(20) {
                    if yPosition > pageHeight - 100 {
                        context.beginPage()
                        yPosition = 50
                    }
                    
                    let sessionDate = dateFormatter.string(from: session.startTime)
                    let sessionInfo = "\(sessionDate) - Score: \(session.quietScore), Duration: \(String(format: "%.1f", session.duration / 3600))h"
                    sessionInfo.draw(at: CGPoint(x: 50, y: yPosition), withAttributes: summaryAttributes)
                    yPosition += 20
                }
            }
            
            return tempURL
        } catch {
            print("Failed to generate PDF: \(error)")
            return nil
        }
    }
    
    // MARK: - CSV Export
    
    func generateCSVReport(sessions: [SleepSession]) -> URL? {
        var csvString = "Date,Start Time,End Time,Duration (hours),Quiet Score,Sleep Quality,Total Snores,Total Coughs,Total Sleep Talk,Snore Severity,Heart Rate,Deep Sleep (min),REM Sleep (min),Oxygen Saturation\n"
        
        let dateFormatter = DateFormatter()
        dateFormatter.dateStyle = .short
        
        let timeFormatter = DateFormatter()
        timeFormatter.timeStyle = .short
        
        for session in sessions {
            let date = dateFormatter.string(from: session.startTime)
            let startTime = timeFormatter.string(from: session.startTime)
            let endTime = session.endTime != nil ? timeFormatter.string(from: session.endTime!) : "N/A"
            let duration = String(format: "%.2f", session.duration / 3600)
            let quietScore = "\(session.quietScore)"
            let quality = session.sleepQuality
            let snores = "\(session.totalSnoreEvents)"
            let coughs = "\(session.totalCoughEvents)"
            let sleepTalk = "\(session.totalSleepTalkEvents)"
            let severity = String(format: "%.1f", session.snoreSeverityIndex)
            let heartRate = session.averageHeartRate != nil ? String(format: "%.1f", session.averageHeartRate!) : "N/A"
            let deepSleep = session.deepSleepMinutes != nil ? String(format: "%.1f", session.deepSleepMinutes!) : "N/A"
            let remSleep = session.remSleepMinutes != nil ? String(format: "%.1f", session.remSleepMinutes!) : "N/A"
            let oxygen = session.oxygenSaturation != nil ? String(format: "%.1f", session.oxygenSaturation!) : "N/A"
            
            let row = "\(date),\(startTime),\(endTime),\(duration),\(quietScore),\(quality),\(snores),\(coughs),\(sleepTalk),\(severity),\(heartRate),\(deepSleep),\(remSleep),\(oxygen)\n"
            csvString.append(row)
        }
        
        let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent("SleepData.csv")
        
        do {
            try csvString.write(to: tempURL, atomically: true, encoding: .utf8)
            return tempURL
        } catch {
            print("Failed to generate CSV: \(error)")
            return nil
        }
    }
    
    // MARK: - Share
    
    func shareFile(url: URL, from viewController: UIViewController) {
        let activityVC = UIActivityViewController(activityItems: [url], applicationActivities: nil)
        viewController.present(activityVC, animated: true)
    }
}

