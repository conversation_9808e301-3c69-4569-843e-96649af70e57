//
//  NotificationService.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 01/10/2025.
//

import Foundation
import UserNotifications
import SwiftData

@Observable
class NotificationService {
    var isAuthorized = false
    var bedtimeReminderEnabled = false
    var bedtimeReminderTime: Date = Calendar.current.date(from: DateComponents(hour: 22, minute: 0)) ?? Date()
    var smartAlarmEnabled = false
    var smartAlarmTime: Date = Calendar.current.date(from: DateComponents(hour: 7, minute: 0)) ?? Date()
    var smartAlarmWindow: TimeInterval = 1800 // 30 minutes
    
    init() {
        checkAuthorizationStatus()
    }
    
    func requestAuthorization() async -> Bool {
        do {
            let granted = try await UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .sound, .badge])
            await MainActor.run {
                isAuthorized = granted
            }
            return granted
        } catch {
            print("Failed to request notification authorization: \(error)")
            return false
        }
    }
    
    func checkAuthorizationStatus() {
        UNUserNotificationCenter.current().getNotificationSettings { settings in
            DispatchQueue.main.async {
                self.isAuthorized = settings.authorizationStatus == .authorized
            }
        }
    }
    
    // MARK: - Bedtime Reminders
    
    func scheduleBedtimeReminder() {
        guard isAuthorized, bedtimeReminderEnabled else { return }
        
        // Cancel existing bedtime reminders
        UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: ["bedtime-reminder"])
        
        let calendar = Calendar.current
        let components = calendar.dateComponents([.hour, .minute], from: bedtimeReminderTime)
        
        let content = UNMutableNotificationContent()
        content.title = "Time for Bed 🌙"
        content.body = "It's time to wind down and prepare for a restful night's sleep."
        content.sound = .default
        content.categoryIdentifier = "bedtime"
        
        let trigger = UNCalendarNotificationTrigger(dateMatching: components, repeats: true)
        let request = UNNotificationRequest(identifier: "bedtime-reminder", content: content, trigger: trigger)
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("Failed to schedule bedtime reminder: \(error)")
            } else {
                print("✅ Bedtime reminder scheduled for \(components.hour ?? 0):\(components.minute ?? 0)")
            }
        }
    }
    
    func cancelBedtimeReminder() {
        UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: ["bedtime-reminder"])
    }
    
    // MARK: - Smart Alarm
    
    func scheduleSmartAlarm(session: SleepSession?) {
        guard isAuthorized, smartAlarmEnabled else { return }
        
        // Cancel existing smart alarm
        UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: ["smart-alarm"])
        
        let calendar = Calendar.current
        let alarmComponents = calendar.dateComponents([.hour, .minute], from: smartAlarmTime)
        
        // Calculate optimal wake time based on sleep phases
        let optimalWakeTime = calculateOptimalWakeTime(targetTime: smartAlarmTime, session: session)
        let optimalComponents = calendar.dateComponents([.hour, .minute], from: optimalWakeTime)
        
        let content = UNMutableNotificationContent()
        content.title = "Good Morning! ☀️"
        content.body = "Time to wake up feeling refreshed."
        content.sound = .default
        content.categoryIdentifier = "smart-alarm"
        
        let trigger = UNCalendarNotificationTrigger(dateMatching: optimalComponents, repeats: false)
        let request = UNNotificationRequest(identifier: "smart-alarm", content: content, trigger: trigger)
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("Failed to schedule smart alarm: \(error)")
            } else {
                print("✅ Smart alarm scheduled for \(optimalComponents.hour ?? 0):\(optimalComponents.minute ?? 0)")
            }
        }
    }
    
    func cancelSmartAlarm() {
        UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: ["smart-alarm"])
    }
    
    private func calculateOptimalWakeTime(targetTime: Date, session: SleepSession?) -> Date {
        // If no active session, return target time
        guard let session = session, session.isActive else {
            return targetTime
        }
        
        // Calculate sleep duration
        let sleepDuration = Date().timeIntervalSince(session.startTime)
        let sleepCycles = sleepDuration / 5400 // 90-minute sleep cycles
        
        // Try to wake at the end of a sleep cycle (light sleep)
        let calendar = Calendar.current
        let windowStart = calendar.date(byAdding: .second, value: -Int(smartAlarmWindow), to: targetTime) ?? targetTime
        
        // Find the nearest sleep cycle end within the window
        var optimalTime = targetTime
        var minDistance = smartAlarmWindow
        
        for i in 0..<10 {
            let cycleEnd = session.startTime.addingTimeInterval(Double(i) * 5400)
            if cycleEnd >= windowStart && cycleEnd <= targetTime {
                let distance = targetTime.timeIntervalSince(cycleEnd)
                if distance < minDistance {
                    minDistance = distance
                    optimalTime = cycleEnd
                }
            }
        }
        
        return optimalTime
    }
    
    // MARK: - Sleep Quality Notifications
    
    func sendSleepQualityNotification(score: Int, session: SleepSession) {
        guard isAuthorized else { return }
        
        let content = UNMutableNotificationContent()
        
        if score >= 80 {
            content.title = "Excellent Sleep! 🌟"
            content.body = "You had a great night with a Quiet Score of \(score). Keep it up!"
        } else if score >= 60 {
            content.title = "Good Sleep 😊"
            content.body = "Your Quiet Score was \(score). Not bad!"
        } else {
            content.title = "Sleep Report Available 📊"
            content.body = "Your Quiet Score was \(score). Check your report for insights."
        }
        
        content.sound = .default
        content.categoryIdentifier = "sleep-report"
        
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 1, repeats: false)
        let request = UNNotificationRequest(identifier: "sleep-quality-\(session.id)", content: content, trigger: trigger)
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("Failed to send sleep quality notification: \(error)")
            }
        }
    }
    
    // MARK: - Insight Notifications
    
    func sendInsightNotification(insight: InsightData) {
        guard isAuthorized else { return }
        
        let content = UNMutableNotificationContent()
        content.title = insight.title
        content.body = insight.message
        content.sound = .default
        content.categoryIdentifier = "insight"
        
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 1, repeats: false)
        let request = UNNotificationRequest(identifier: "insight-\(insight.id)", content: content, trigger: trigger)
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("Failed to send insight notification: \(error)")
            }
        }
    }
    
    // MARK: - Sleep Debt Notifications
    
    func sendSleepDebtWarning(debt: Double) {
        guard isAuthorized, debt > 5 else { return }
        
        let content = UNMutableNotificationContent()
        content.title = "Sleep Debt Alert ⚠️"
        content.body = "You have \(String(format: "%.1f", debt)) hours of sleep debt. Prioritize rest tonight!"
        content.sound = .default
        content.categoryIdentifier = "sleep-debt"
        
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 1, repeats: false)
        let request = UNNotificationRequest(identifier: "sleep-debt-warning", content: content, trigger: trigger)
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("Failed to send sleep debt warning: \(error)")
            }
        }
    }
}

