//
//  SubscriptionService.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 30/09/2025.
//

import Foundation
import StoreKit
import Combine

class SubscriptionService: ObservableObject {
    @Published var isPremium = false
    @Published var products: [Product] = []
    
    private let productIDs = ["com.sleepsoundcoach.premium.monthly", "com.sleepsoundcoach.premium.yearly"]
    
    init() {
        Task {
            await loadProducts()
            await checkSubscriptionStatus()
        }
    }
    
    func loadProducts() async {
        do {
            products = try await Product.products(for: productIDs)
        } catch {
            print("Failed to load products: \(error)")
        }
    }
    
    func purchase(_ product: Product) async -> Bool {
        do {
            let result = try await product.purchase()
            
            switch result {
            case .success(let verification):
                switch verification {
                case .verified(let transaction):
                    await transaction.finish()
                    await checkSubscriptionStatus()
                    return true
                case .unverified:
                    return false
                }
            case .userCancelled, .pending:
                return false
            @unknown default:
                return false
            }
        } catch {
            print("Purchase failed: \(error)")
            return false
        }
    }
    
    func checkSubscriptionStatus() async {
        var isActive = false
        
        for await result in Transaction.currentEntitlements {
            if case .verified(let transaction) = result {
                if productIDs.contains(transaction.productID) {
                    isActive = true
                    break
                }
            }
        }
        
        await MainActor.run {
            self.isPremium = isActive
        }
    }
    
    func restorePurchases() async {
        try? await AppStore.sync()
        await checkSubscriptionStatus()
    }
}

