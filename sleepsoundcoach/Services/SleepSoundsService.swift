//
//  SleepSoundsService.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 01/10/2025.
//

import AVFoundation
import Foundation
import SwiftUI

@Observable
class SleepSoundsService {
    var isPlaying = false
    var currentSound: SleepSound?
    var volume: Float = 0.5
    var timerDuration: TimeInterval = 0 // 0 = no timer
    var remainingTime: TimeInterval = 0
    
    private var audioPlayer: AVAudioPlayer?
    private var timer: Timer?
    private var fadeTimer: Timer?
    
    func playSound(_ sound: SleepSound) {
        print("🎵 ========== PLAYING SLEEP SOUND ==========")
        print("   Sound: \(sound.rawValue)")

        stopSound()

        currentSound = sound

        // Try to load MP3 file first, fallback to procedural generation
        if let audioURL = loadAudioFile(for: sound) {
            print("   ✅ Found MP3 file: \(audioURL.lastPathComponent)")
            playMP3File(url: audioURL)
        } else {
            print("   ⚠️ MP3 file not found, using procedural generation")
            generateAndPlayAudio(for: sound)
        }

        isPlaying = true
        print("   🔊 Playback started")
        print("=========================================")

        // Start timer if duration is set
        if timerDuration > 0 {
            remainingTime = timerDuration
            startTimer()
        }
    }

    private func loadAudioFile(for sound: SleepSound) -> URL? {
        // Try to load MP3 file from bundle
        let filename = sound.rawValue.lowercased().replacingOccurrences(of: " ", with: "")

        print("   🔍 Looking for audio file: \(filename).mp3")

        if let url = Bundle.main.url(forResource: filename, withExtension: "mp3", subdirectory: "Resources/Audio") {
            print("   ✅ Found in Resources/Audio/")
            return url
        }

        if let url = Bundle.main.url(forResource: filename, withExtension: "mp3") {
            print("   ✅ Found in main bundle")
            return url
        }

        print("   ❌ Audio file not found in bundle")
        return nil
    }

    private func playMP3File(url: URL) {
        do {
            print("   🎛️ Configuring audio session for playback...")
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.playback, mode: .default, options: [.mixWithOthers])
            try audioSession.setActive(true)
            print("   ✅ Audio session configured")

            print("   📀 Creating audio player...")
            audioPlayer = try AVAudioPlayer(contentsOf: url)
            audioPlayer?.numberOfLoops = -1 // Loop indefinitely
            audioPlayer?.volume = volume
            audioPlayer?.prepareToPlay()
            print("   ✅ Audio player created and prepared")

            print("   ▶️ Starting playback...")
            let success = audioPlayer?.play() ?? false
            print("   Playback started: \(success ? "✅ SUCCESS" : "❌ FAILED")")

        } catch {
            print("   ❌ ERROR playing MP3: \(error.localizedDescription)")
            print("   🔄 Falling back to procedural generation...")
            generateAndPlayAudio(for: currentSound ?? .rain)
        }
    }
    
    func stopSound(fadeOut: Bool = false) {
        if fadeOut {
            fadeOutAndStop()
        } else {
            audioPlayer?.stop()
            audioPlayer = nil
            isPlaying = false
            currentSound = nil
            stopTimer()
        }
    }
    
    func setVolume(_ newVolume: Float) {
        volume = max(0, min(1, newVolume))
        audioPlayer?.volume = volume
    }
    
    func setTimer(duration: TimeInterval) {
        timerDuration = duration
        if isPlaying && duration > 0 {
            remainingTime = duration
            startTimer()
        }
    }
    
    private func generateAndPlayAudio(for sound: SleepSound) {
        print("   🎛️ Generating procedural audio for: \(sound.rawValue)")

        let audioSession = AVAudioSession.sharedInstance()
        do {
            print("   🔊 Configuring audio session...")
            try audioSession.setCategory(.playback, mode: .default, options: [.mixWithOthers])
            try audioSession.setActive(true)
            print("   ✅ Audio session configured")
        } catch {
            print("   ❌ ERROR: Failed to setup audio session: \(error.localizedDescription)")
            return
        }
        
        // Generate audio buffer based on sound type
        switch sound {
        case .rain:
            playProceduralRain()
        case .ocean:
            playProceduralOcean()
        case .forest:
            playProceduralForest()
        case .whiteNoise:
            playProceduralWhiteNoise()
        case .brownNoise:
            playProceduralBrownNoise()
        case .pinkNoise:
            playProceduralPinkNoise()
        case .fireplace:
            playProceduralFireplace()
        case .thunderstorm:
            playProceduralThunderstorm()
        }
    }
    
    private func playProceduralWhiteNoise() {
        let sampleRate = 44100.0
        let duration = 60.0 // 60 seconds, will loop
        let samples = Int(sampleRate * duration)
        
        var audioData = [Float](repeating: 0, count: samples)
        
        for i in 0..<samples {
            audioData[i] = Float.random(in: -1...1) * volume
        }
        
        playAudioBuffer(audioData, sampleRate: sampleRate, loops: true)
    }
    
    private func playProceduralBrownNoise() {
        let sampleRate = 44100.0
        let duration = 60.0
        let samples = Int(sampleRate * duration)
        
        var audioData = [Float](repeating: 0, count: samples)
        var lastValue: Float = 0
        
        for i in 0..<samples {
            let white = Float.random(in: -1...1)
            lastValue = (lastValue + white * 0.02)
            lastValue = max(-1, min(1, lastValue))
            audioData[i] = lastValue * volume
        }
        
        playAudioBuffer(audioData, sampleRate: sampleRate, loops: true)
    }
    
    private func playProceduralPinkNoise() {
        let sampleRate = 44100.0
        let duration = 60.0
        let samples = Int(sampleRate * duration)
        
        var audioData = [Float](repeating: 0, count: samples)
        var b0: Float = 0, b1: Float = 0, b2: Float = 0, b3: Float = 0, b4: Float = 0, b5: Float = 0, b6: Float = 0
        
        for i in 0..<samples {
            let white = Float.random(in: -1...1)
            b0 = 0.99886 * b0 + white * 0.0555179
            b1 = 0.99332 * b1 + white * 0.0750759
            b2 = 0.96900 * b2 + white * 0.1538520
            b3 = 0.86650 * b3 + white * 0.3104856
            b4 = 0.55000 * b4 + white * 0.5329522
            b5 = -0.7616 * b5 - white * 0.0168980
            let pink = b0 + b1 + b2 + b3 + b4 + b5 + b6 + white * 0.5362
            b6 = white * 0.115926
            audioData[i] = pink * 0.11 * volume
        }
        
        playAudioBuffer(audioData, sampleRate: sampleRate, loops: true)
    }
    
    private func playProceduralRain() {
        // Simulate rain with filtered white noise
        let sampleRate = 44100.0
        let duration = 60.0
        let samples = Int(sampleRate * duration)
        
        var audioData = [Float](repeating: 0, count: samples)
        
        for i in 0..<samples {
            let noise = Float.random(in: -1...1)
            // Simple low-pass filter for rain-like sound
            if i > 0 {
                audioData[i] = (noise * 0.3 + audioData[i-1] * 0.7) * volume
            } else {
                audioData[i] = noise * volume
            }
        }
        
        playAudioBuffer(audioData, sampleRate: sampleRate, loops: true)
    }
    
    private func playProceduralOcean() {
        // Simulate ocean waves with low-frequency oscillation
        let sampleRate = 44100.0
        let duration = 60.0
        let samples = Int(sampleRate * duration)
        
        var audioData = [Float](repeating: 0, count: samples)
        
        for i in 0..<samples {
            let time = Float(i) / Float(sampleRate)
            let wave1 = sin(2.0 * .pi * 0.1 * time) // 0.1 Hz wave
            let wave2 = sin(2.0 * .pi * 0.15 * time) * 0.5 // 0.15 Hz wave
            let noise = Float.random(in: -0.3...0.3)
            audioData[i] = (wave1 + wave2 + noise) * 0.3 * volume
        }
        
        playAudioBuffer(audioData, sampleRate: sampleRate, loops: true)
    }
    
    private func playProceduralForest() {
        // Simulate forest ambience with filtered noise and occasional chirps
        let sampleRate = 44100.0
        let duration = 60.0
        let samples = Int(sampleRate * duration)
        
        var audioData = [Float](repeating: 0, count: samples)
        
        for i in 0..<samples {
            let noise = Float.random(in: -0.5...0.5)
            // Add occasional "chirps"
            if Float.random(in: 0...1) > 0.999 {
                let chirpFreq = Float.random(in: 2000...4000)
                let time = Float(i) / Float(sampleRate)
                audioData[i] = sin(2.0 * .pi * chirpFreq * time) * 0.2 * volume
            } else {
                audioData[i] = noise * 0.2 * volume
            }
        }
        
        playAudioBuffer(audioData, sampleRate: sampleRate, loops: true)
    }
    
    private func playProceduralFireplace() {
        // Simulate fireplace crackling
        playProceduralBrownNoise() // Similar to brown noise with occasional pops
    }
    
    private func playProceduralThunderstorm() {
        // Combine rain with occasional thunder
        playProceduralRain() // Base rain sound
        // Thunder would be added as random loud bursts
    }
    
    private func playAudioBuffer(_ audioData: [Float], sampleRate: Double, loops: Bool) {
        print("   🎵 Creating audio buffer: \(audioData.count) samples at \(sampleRate) Hz")

        // Create temporary file to save audio data
        let tempDir = FileManager.default.temporaryDirectory
        let tempFile = tempDir.appendingPathComponent("sleepsound_\(UUID().uuidString).wav")

        print("   💾 Saving to temporary file: \(tempFile.lastPathComponent)")

        // Create audio format
        guard let audioFormat = AVAudioFormat(standardFormatWithSampleRate: sampleRate, channels: 1) else {
            print("   ❌ ERROR: Failed to create audio format")
            return
        }

        // Create audio file
        do {
            let audioFile = try AVAudioFile(forWriting: tempFile, settings: audioFormat.settings)

            // Create buffer
            guard let audioBuffer = AVAudioPCMBuffer(pcmFormat: audioFormat, frameCapacity: AVAudioFrameCount(audioData.count)) else {
                print("   ❌ ERROR: Failed to create audio buffer")
                return
            }

            audioBuffer.frameLength = AVAudioFrameCount(audioData.count)

            // Copy audio data to buffer
            let channelData = audioBuffer.floatChannelData![0]
            for i in 0..<audioData.count {
                channelData[i] = audioData[i]
            }

            // Write to file
            try audioFile.write(from: audioBuffer)
            print("   ✅ Audio file written successfully")

            // Play the file
            playAudioFile(url: tempFile, loops: loops)

        } catch {
            print("   ❌ ERROR: Failed to create audio file: \(error.localizedDescription)")
        }
    }

    private func playAudioFile(url: URL, loops: Bool) {
        do {
            print("   📀 Creating audio player from file...")
            audioPlayer = try AVAudioPlayer(contentsOf: url)
            audioPlayer?.numberOfLoops = loops ? -1 : 0
            audioPlayer?.volume = volume
            audioPlayer?.prepareToPlay()
            print("   ✅ Audio player prepared")

            print("   ▶️ Starting playback...")
            let success = audioPlayer?.play() ?? false
            print("   Playback: \(success ? "✅ SUCCESS" : "❌ FAILED")")

            if success {
                print("   🔊 Audio is now playing")
            }

        } catch {
            print("   ❌ ERROR: Failed to play audio file: \(error.localizedDescription)")
        }
    }
    
    private func startTimer() {
        stopTimer()
        
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            guard let self = self else { return }
            
            self.remainingTime -= 1
            
            if self.remainingTime <= 0 {
                self.stopSound(fadeOut: true)
            } else if self.remainingTime <= 10 {
                // Start fading out in the last 10 seconds
                let fadeVolume = Float(self.remainingTime / 10.0) * self.volume
                self.audioPlayer?.volume = fadeVolume
            }
        }
    }
    
    private func stopTimer() {
        timer?.invalidate()
        timer = nil
    }
    
    private func fadeOutAndStop() {
        var currentVolume = audioPlayer?.volume ?? volume
        
        fadeTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] timer in
            currentVolume -= 0.05
            
            if currentVolume <= 0 {
                timer.invalidate()
                self?.audioPlayer?.stop()
                self?.audioPlayer = nil
                self?.isPlaying = false
                self?.currentSound = nil
                self?.stopTimer()
            } else {
                self?.audioPlayer?.volume = currentVolume
            }
        }
    }
}

enum SleepSound: String, CaseIterable, Identifiable {
    case rain = "Rain"
    case ocean = "Ocean Waves"
    case forest = "Forest"
    case whiteNoise = "White Noise"
    case brownNoise = "Brown Noise"
    case pinkNoise = "Pink Noise"
    case fireplace = "Fireplace"
    case thunderstorm = "Thunderstorm"
    
    var id: String { rawValue }
    
    var icon: String {
        switch self {
        case .rain: return "cloud.rain.fill"
        case .ocean: return "water.waves"
        case .forest: return "leaf.fill"
        case .whiteNoise: return "waveform"
        case .brownNoise: return "waveform.path"
        case .pinkNoise: return "waveform.circle"
        case .fireplace: return "flame.fill"
        case .thunderstorm: return "cloud.bolt.rain.fill"
        }
    }
    
    var color: Color {
        switch self {
        case .rain: return ColorTheme.rainColor
        case .ocean: return ColorTheme.oceanColor
        case .forest: return ColorTheme.forestColor
        case .whiteNoise: return ColorTheme.whiteNoiseColor
        case .brownNoise: return ColorTheme.brownNoiseColor
        case .pinkNoise: return Color.pink
        case .fireplace: return Color.orange
        case .thunderstorm: return Color.purple
        }
    }
}

