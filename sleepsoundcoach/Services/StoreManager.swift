//
//  StoreManager.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 01/10/2025.
//

import Foundation
import StoreKit

@Observable
class StoreManager {
    
    // MARK: - Properties
    
    var products: [Product] = []
    var purchasedProductIDs: Set<String> = []
    var isLoading = false
    var errorMessage: String?
    
    // Product IDs
    private let productIDs = [
        "com.sleepsoundcoach.premium.monthly",
        "com.sleepsoundcoach.premium.yearly"
    ]
    
    // MARK: - Computed Properties
    
    var isPremium: Bool {
        !purchasedProductIDs.isEmpty
    }
    
    var monthlyProduct: Product? {
        products.first { $0.id == "com.sleepsoundcoach.premium.monthly" }
    }
    
    var yearlyProduct: Product? {
        products.first { $0.id == "com.sleepsoundcoach.premium.yearly" }
    }
    
    // MARK: - Initialization
    
    init() {
        Task {
            await loadProducts()
            await updatePurchasedProducts()
        }
    }
    
    // MARK: - Product Loading
    
    func loadProducts() async {
        isLoading = true
        errorMessage = nil
        
        do {
            print("🛒 Loading products...")
            products = try await Product.products(for: productIDs)
            print("✅ Loaded \(products.count) products")
            
            for product in products {
                print("  - \(product.displayName): \(product.displayPrice)")
            }
        } catch {
            print("❌ Failed to load products: \(error)")
            errorMessage = "Failed to load products. Please try again."
        }
        
        isLoading = false
    }
    
    // MARK: - Purchase
    
    func purchase(_ product: Product) async throws -> Transaction? {
        print("🛒 Attempting to purchase: \(product.displayName)")
        
        let result = try await product.purchase()
        
        switch result {
        case .success(let verification):
            print("✅ Purchase successful")
            let transaction = try checkVerified(verification)
            
            // Update purchased products
            await updatePurchasedProducts()
            
            // Finish the transaction
            await transaction.finish()
            
            print("✅ Transaction finished")
            return transaction
            
        case .userCancelled:
            print("⚠️ User cancelled purchase")
            return nil
            
        case .pending:
            print("⏳ Purchase pending")
            return nil
            
        @unknown default:
            print("❓ Unknown purchase result")
            return nil
        }
    }
    
    // MARK: - Restore Purchases
    
    func restorePurchases() async {
        print("🔄 Restoring purchases...")
        
        do {
            try await AppStore.sync()
            await updatePurchasedProducts()
            print("✅ Purchases restored")
        } catch {
            print("❌ Failed to restore purchases: \(error)")
            errorMessage = "Failed to restore purchases. Please try again."
        }
    }
    
    // MARK: - Update Purchased Products
    
    func updatePurchasedProducts() async {
        print("🔍 Checking for purchased products...")
        
        var newPurchasedIDs: Set<String> = []
        
        for await result in Transaction.currentEntitlements {
            do {
                let transaction = try checkVerified(result)
                
                // Check if the transaction is still valid
                if transaction.revocationDate == nil {
                    newPurchasedIDs.insert(transaction.productID)
                    print("  ✅ Found active subscription: \(transaction.productID)")
                }
            } catch {
                print("  ❌ Failed to verify transaction: \(error)")
            }
        }
        
        purchasedProductIDs = newPurchasedIDs
        print("📊 Total active subscriptions: \(purchasedProductIDs.count)")
    }
    
    // MARK: - Verification
    
    private func checkVerified<T>(_ result: VerificationResult<T>) throws -> T {
        switch result {
        case .unverified:
            print("❌ Transaction verification failed")
            throw StoreError.failedVerification
        case .verified(let safe):
            return safe
        }
    }
    
    // MARK: - Feature Access

    func hasAccess(to feature: PremiumFeatureType) -> Bool {
        switch feature {
        case .unlimitedSoundDetection:
            return isPremium
        case .allSleepSounds:
            return isPremium
        case .advancedAnalytics:
            return isPremium
        case .aiInsights:
            return isPremium
        case .smartAlarm:
            return isPremium
        case .unlimitedExports:
            return isPremium
        case .audioPlayback:
            return isPremium
        case .customSensitivity:
            return isPremium
        }
    }
    
    // Free tier limits
    func canDetectMoreEvents(currentCount: Int) -> Bool {
        if isPremium {
            return true
        }
        return currentCount < 3 // Free tier: 3 events shown
    }
    
    func canAccessSleepSound(_ soundIndex: Int) -> Bool {
        if isPremium {
            return true
        }
        return soundIndex < 3 // Free tier: first 3 sounds (White Noise, Rain, Ocean)
    }
    
    func canExportThisMonth(exportsThisMonth: Int) -> Bool {
        if isPremium {
            return true
        }
        return exportsThisMonth < 1 // Free tier: 1 export per month
    }
    
    func canAccessHistory(daysAgo: Int) -> Bool {
        if isPremium {
            return true
        }
        return daysAgo <= 7 // Free tier: last 7 days
    }
}

// MARK: - Models

enum PremiumFeatureType {
    case unlimitedSoundDetection
    case allSleepSounds
    case advancedAnalytics
    case aiInsights
    case smartAlarm
    case unlimitedExports
    case audioPlayback
    case customSensitivity
}

enum StoreError: Error {
    case failedVerification
}

