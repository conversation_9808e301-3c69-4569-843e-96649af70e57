//
//  SoundAnalysisService.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 30/09/2025.
//

import Foundation
import AVFoundation
import SoundAnalysis
import Combine
import Accelerate

@Observable
class SoundAnalysisService: NSObject {
    var isRecording = false
    var currentWaveformLevel: Float = 0.0
    var detectedEvents: [SoundEvent] = []
    var totalEventsDetected: Int = 0
    var lastDetectionTime: Date?

    private var audioEngine: AVAudioEngine?
    private var inputNode: AVAudioInputNode?
    private var streamAnalyzer: SNAudioStreamAnalyzer?

    private var audioRecorder: AVAudioRecorder?
    private var recordingURL: URL?

    // Event detection state
    private var lastEventTime: Date?
    private var eventStartTime: Date?
    private var isInEvent = false
    private var currentEventType: SoundEventType?
    private var eventIntensitySum: Float = 0.0
    private var eventSampleCount: Int = 0

    // Audio analysis buffers
    private var audioBufferQueue: [AVAudioPCMBuffer] = []
    private let maxBufferQueueSize = 10

    // Frequency analysis
    private var fftSetup: vDSP_DFT_Setup?
    private let fftSize = 1024

    // Detection thresholds (adjustable for sensitivity)
    // LOWERED THRESHOLD FOR BETTER DETECTION - was 0.03, now 0.01
    var detectionThreshold: Float = 0.01  // Lower = more sensitive (range: 0.005 - 0.1)
    var minimumEventDuration: TimeInterval = 0.3  // Minimum duration to count as event (reduced from 0.5)

    // PERFORMANCE OPTIMIZATION: Battery-saving mode
    var batteryOptimizationEnabled = true
    private var analysisInterval: TimeInterval = 0.1 // Analyze every 100ms (can be increased to save battery)
    private var lastAnalysisTime: Date?

    override init() {
        super.init()
        setupFFT()
    }

    deinit {
        if let fftSetup = fftSetup {
            vDSP_DFT_DestroySetup(fftSetup)
        }
    }

    private func setupFFT() {
        fftSetup = vDSP_DFT_zop_CreateSetup(nil, vDSP_Length(fftSize), .FORWARD)
    }
    
    func requestMicrophonePermission() async -> Bool {
        await withCheckedContinuation { continuation in
            AVAudioApplication.requestRecordPermission { granted in
                continuation.resume(returning: granted)
            }
        }
    }
    
    func startRecording() throws {
        guard !isRecording else {
            print("⚠️ Already recording, ignoring start request")
            return
        }

        print("🎤 ========== STARTING SOUND ANALYSIS ==========")

        // Check microphone permission
        let permissionStatus = AVAudioSession.sharedInstance().recordPermission
        print("🔐 Microphone permission: \(permissionStatus == .granted ? "✅ GRANTED" : "❌ DENIED")")

        if permissionStatus != .granted {
            print("❌ ERROR: Microphone permission not granted!")
            throw NSError(domain: "SoundAnalysisService", code: -1, userInfo: [NSLocalizedDescriptionKey: "Microphone permission required"])
        }

        // Setup audio session for background recording
        let audioSession = AVAudioSession.sharedInstance()
        print("🔊 Configuring audio session...")
        try audioSession.setCategory(.record, mode: .measurement, options: [.mixWithOthers, .allowBluetooth])
        try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
        print("✅ Audio session configured: category=.record, mode=.measurement")

        // Setup audio engine
        print("🎛️ Creating audio engine...")
        audioEngine = AVAudioEngine()
        guard let audioEngine = audioEngine else {
            print("❌ CRITICAL ERROR: Failed to create audio engine")
            throw NSError(domain: "SoundAnalysisService", code: -2, userInfo: [NSLocalizedDescriptionKey: "Failed to create audio engine"])
        }
        print("✅ Audio engine created")

        inputNode = audioEngine.inputNode
        guard let inputNode = inputNode else {
            print("❌ CRITICAL ERROR: Failed to get input node")
            throw NSError(domain: "SoundAnalysisService", code: -3, userInfo: [NSLocalizedDescriptionKey: "Failed to get input node"])
        }
        print("✅ Input node obtained")

        let recordingFormat = inputNode.outputFormat(forBus: 0)
        print("🎵 Recording format:")
        print("   📊 Sample rate: \(recordingFormat.sampleRate) Hz")
        print("   📊 Channels: \(recordingFormat.channelCount)")
        print("   📊 Bits per channel: \(recordingFormat.streamDescription.pointee.mBitsPerChannel)")

        // Setup stream analyzer
        print("🔬 Creating stream analyzer...")
        streamAnalyzer = SNAudioStreamAnalyzer(format: recordingFormat)
        print("✅ Stream analyzer created")

        // Install tap for audio analysis with larger buffer for better detection
        print("🎧 Installing audio tap (buffer size: 4096)...")
        inputNode.installTap(onBus: 0, bufferSize: 4096, format: recordingFormat) { [weak self] buffer, time in
            self?.processAudioBuffer(buffer)
        }
        print("✅ Audio tap installed")

        // Start the audio engine
        print("▶️ Starting audio engine...")
        try audioEngine.start()
        isRecording = true
        print("✅ Audio engine started successfully")

        print("🔍 Sound detection ACTIVE")
        print("   🎚️ Detection threshold: \(detectionThreshold)")
        print("   🎚️ Minimum event duration: \(minimumEventDuration)s")
        print("========================================")
    }
    
    func stopRecording() {
        guard isRecording else { return }

        print("🛑 Stopping sound analysis recording...")

        audioEngine?.stop()
        inputNode?.removeTap(onBus: 0)

        audioEngine = nil
        inputNode = nil
        streamAnalyzer = nil

        isRecording = false
        currentWaveformLevel = 0.0

        // End any ongoing event
        if isInEvent {
            endCurrentEvent()
        }

        print("✅ Recording stopped. Total events detected: \(totalEventsDetected)")

        // Deactivate audio session
        try? AVAudioSession.sharedInstance().setActive(false, options: .notifyOthersOnDeactivation)
    }
    
    private var bufferCount = 0
    private var lastLogTime = Date()

    private func processAudioBuffer(_ buffer: AVAudioPCMBuffer) {
        bufferCount += 1

        // Log every 100 buffers (approximately every 10 seconds at 44.1kHz with 4096 buffer)
        if bufferCount % 100 == 0 {
            let elapsed = Date().timeIntervalSince(lastLogTime)
            print("📊 Processing audio: \(bufferCount) buffers processed (\(String(format: "%.1f", elapsed))s since last log)")
            lastLogTime = Date()
        }

        guard let channelData = buffer.floatChannelData else {
            if bufferCount % 100 == 0 {
                print("⚠️ No channel data in buffer")
            }
            return
        }

        let channelDataValue = channelData.pointee
        let frameLength = Int(buffer.frameLength)

        // Convert to array for processing
        var samples = [Float](repeating: 0, count: frameLength)
        for i in 0..<frameLength {
            samples[i] = channelDataValue[i]
        }

        // Calculate RMS for waveform visualization
        var rms: Float = 0
        vDSP_rmsqv(samples, 1, &rms, vDSP_Length(frameLength))

        // Calculate peak amplitude
        var peak: Float = 0
        vDSP_maxv(samples.map { abs($0) }, 1, &peak, vDSP_Length(frameLength))

        // Log audio levels periodically
        if bufferCount % 100 == 0 {
            print("🎚️ Audio levels: RMS=\(String(format: "%.4f", rms)), Peak=\(String(format: "%.4f", peak)), Threshold=\(String(format: "%.4f", detectionThreshold))")
        }

        // Update waveform level on main thread
        DispatchQueue.main.async { [weak self] in
            self?.currentWaveformLevel = rms
        }

        // Perform frequency analysis
        let frequencyData = performFFT(samples: samples)

        // Analyze for sound events with both time and frequency domain data
        analyzeForSoundEvents(rms: rms, peak: peak, frequencyData: frequencyData, buffer: buffer)
    }

    private func performFFT(samples: [Float]) -> [Float] {
        guard samples.count >= fftSize else { return [] }

        var realPart = [Float](repeating: 0, count: fftSize)
        var imagPart = [Float](repeating: 0, count: fftSize)

        // Copy samples to real part
        for i in 0..<min(fftSize, samples.count) {
            realPart[i] = samples[i]
        }

        // Perform FFT using vDSP
        realPart.withUnsafeMutableBufferPointer { realPtr in
            imagPart.withUnsafeMutableBufferPointer { imagPtr in
                let splitComplex = DSPSplitComplex(realp: realPtr.baseAddress!, imagp: imagPtr.baseAddress!)

                if let fftSetup = fftSetup {
                    vDSP_DFT_Execute(fftSetup, splitComplex.realp, splitComplex.imagp, splitComplex.realp, splitComplex.imagp)
                }
            }
        }

        // Calculate magnitude spectrum
        var magnitudes = [Float](repeating: 0, count: fftSize / 2)
        for i in 0..<fftSize / 2 {
            magnitudes[i] = sqrt(realPart[i] * realPart[i] + imagPart[i] * imagPart[i])
        }

        return magnitudes
    }
    
    private var soundDetectionCount = 0

    private func analyzeForSoundEvents(rms: Float, peak: Float, frequencyData: [Float], buffer: AVAudioPCMBuffer) {
        // Dynamic threshold based on ambient noise
        let threshold = detectionThreshold

        let exceedsRMS = rms > threshold
        let exceedsPeak = peak > threshold * 2

        if exceedsRMS || exceedsPeak {
            soundDetectionCount += 1

            if !isInEvent {
                // Start of a new event
                isInEvent = true
                eventStartTime = Date()
                eventIntensitySum = rms
                eventSampleCount = 1

                // Classify the sound type based on characteristics
                currentEventType = classifySound(rms: rms, peak: peak, frequencyData: frequencyData)

                print("🔔 ========== SOUND EVENT STARTED ==========")
                print("   Type: \(currentEventType?.rawValue.uppercased() ?? "UNKNOWN")")
                print("   RMS: \(String(format: "%.4f", rms)) (threshold: \(String(format: "%.4f", threshold)))")
                print("   Peak: \(String(format: "%.4f", peak))")
                print("   Exceeded RMS: \(exceedsRMS), Exceeded Peak: \(exceedsPeak)")
                print("   Time: \(Date())")
                print("==========================================")
            } else {
                // Continue tracking event intensity
                eventIntensitySum += rms
                eventSampleCount += 1

                // Log every 10 samples during event
                if eventSampleCount % 10 == 0 {
                    let avgIntensity = eventIntensitySum / Float(eventSampleCount)
                    let duration = Date().timeIntervalSince(eventStartTime ?? Date())
                    print("   📊 Event ongoing: duration=\(String(format: "%.1f", duration))s, avg intensity=\(String(format: "%.4f", avgIntensity))")
                }
            }
        } else {
            if isInEvent {
                // End of event
                endCurrentEvent()
            }
        }
    }
    
    private func classifySound(rms: Float, peak: Float, frequencyData: [Float]) -> SoundEventType {
        // Advanced classification using frequency analysis
        // This is a heuristic-based approach that analyzes frequency characteristics

        guard !frequencyData.isEmpty else {
            return classifyByAmplitude(rms: rms, peak: peak)
        }

        // Analyze frequency bands
        let lowFreqEnergy = calculateBandEnergy(frequencyData: frequencyData, startBin: 5, endBin: 50)    // 50-500 Hz
        let midFreqEnergy = calculateBandEnergy(frequencyData: frequencyData, startBin: 50, endBin: 150)  // 500-1500 Hz
        let highFreqEnergy = calculateBandEnergy(frequencyData: frequencyData, startBin: 150, endBin: 300) // 1500-3000 Hz

        let totalEnergy = lowFreqEnergy + midFreqEnergy + highFreqEnergy
        guard totalEnergy > 0 else { return .other }

        let lowRatio = lowFreqEnergy / totalEnergy
        let midRatio = midFreqEnergy / totalEnergy
        let highRatio = highFreqEnergy / totalEnergy

        // ADJUSTED THRESHOLDS FOR BETTER DETECTION

        // Snoring: Dominant low frequencies (50-300 Hz), rhythmic
        // Lowered RMS threshold from 0.04 to 0.015
        if lowRatio > 0.55 && rms > 0.015 {
            print("🌙 Classified as SNORING - Low freq ratio: \(String(format: "%.2f", lowRatio)), RMS: \(String(format: "%.4f", rms))")
            return .snoring
        }

        // Coughing: Sharp onset, broad spectrum with emphasis on mid-high frequencies
        // Lowered peak threshold from 0.2 to 0.1
        if peak > 0.1 && midRatio > 0.25 && highRatio > 0.15 {
            print("😷 Classified as COUGHING - Peak: \(String(format: "%.4f", peak)), Mid: \(String(format: "%.2f", midRatio)), High: \(String(format: "%.2f", highRatio))")
            return .coughing
        }

        // Sleep talking: Mid frequencies dominant (300-2000 Hz), moderate intensity
        // Lowered RMS threshold from 0.03 to 0.012
        if midRatio > 0.45 && rms > 0.012 && rms < 0.2 {
            print("💬 Classified as SLEEP TALKING - Mid freq ratio: \(String(format: "%.2f", midRatio)), RMS: \(String(format: "%.4f", rms))")
            return .sleepTalking
        }

        // Fallback to amplitude-based classification
        return classifyByAmplitude(rms: rms, peak: peak)
    }

    private func classifyByAmplitude(rms: Float, peak: Float) -> SoundEventType {
        // Fallback classification based on amplitude characteristics
        if peak > 0.25 {
            return .coughing
        } else if rms > 0.08 {
            return .snoring
        } else if rms > 0.04 {
            return .sleepTalking
        } else {
            return .other
        }
    }

    private func calculateBandEnergy(frequencyData: [Float], startBin: Int, endBin: Int) -> Float {
        let start = max(0, startBin)
        let end = min(frequencyData.count, endBin)

        guard start < end else { return 0 }

        var energy: Float = 0
        for i in start..<end {
            energy += frequencyData[i] * frequencyData[i]
        }

        return sqrt(energy / Float(end - start))
    }
    
    private func endCurrentEvent() {
        guard let startTime = eventStartTime,
              let eventType = currentEventType else {
            print("⚠️ Ending event but missing start time or event type")
            isInEvent = false
            eventIntensitySum = 0
            eventSampleCount = 0
            return
        }

        let duration = Date().timeIntervalSince(startTime)

        print("🏁 ========== SOUND EVENT ENDED ==========")
        print("   Type: \(eventType.rawValue.uppercased())")
        print("   Duration: \(String(format: "%.2f", duration))s (min: \(String(format: "%.2f", minimumEventDuration))s)")
        print("   Samples: \(eventSampleCount)")

        // Only record events longer than minimum duration
        if duration >= minimumEventDuration {
            // Calculate average intensity for the event
            let avgIntensity = eventSampleCount > 0 ? Double(eventIntensitySum / Float(eventSampleCount)) : Double(currentWaveformLevel)

            // Calculate confidence based on duration and intensity
            let confidence = min(0.95, 0.5 + (duration / 10.0) + Double(avgIntensity) * 2)

            print("   Avg Intensity: \(String(format: "%.4f", avgIntensity))")
            print("   Confidence: \(String(format: "%.0f", confidence * 100))%")
            print("   ✅ RECORDING EVENT")

            let event = SoundEvent(
                timestamp: startTime,
                duration: duration,
                intensity: avgIntensity,
                eventType: eventType,
                confidence: confidence
            )
            event.determineSeverity()

            DispatchQueue.main.async { [weak self] in
                self?.detectedEvents.append(event)
                self?.totalEventsDetected += 1
                self?.lastDetectionTime = Date()

                print("   📝 Event saved to array. Total events: \(self?.totalEventsDetected ?? 0)")
                print("=========================================")

                // Play subtle feedback sound
                SoundEffectsManager.shared.playSoundDetectedFeedback(type: eventType)
            }
        } else {
            print("   ⏭️ SKIPPING - Event too short")
            print("=========================================")
        }

        isInEvent = false
        eventStartTime = nil
        currentEventType = nil
        eventIntensitySum = 0
        eventSampleCount = 0
    }
    
    func clearEvents() {
        detectedEvents.removeAll()
        totalEventsDetected = 0
        lastDetectionTime = nil
    }

    // MARK: - Sensitivity Control

    func setSensitivity(_ level: SensitivityLevel) {
        switch level {
        case .low:
            detectionThreshold = 0.08
            minimumEventDuration = 1.5
        case .medium:
            detectionThreshold = 0.04
            minimumEventDuration = 0.8
        case .high:
            detectionThreshold = 0.02
            minimumEventDuration = 0.5
        }
        print("🎚️ Sensitivity set to \(level.rawValue) - Threshold: \(detectionThreshold)")
    }

    // MARK: - Test Sound Generation (for debugging)

    func simulateSnoring() {
        let event = SoundEvent(
            timestamp: Date(),
            duration: 3.5,
            intensity: 0.12,
            eventType: .snoring,
            confidence: 0.85
        )
        event.determineSeverity()
        detectedEvents.append(event)
        totalEventsDetected += 1
        print("🧪 Simulated snoring event")
    }

    func simulateCoughing() {
        let event = SoundEvent(
            timestamp: Date(),
            duration: 1.2,
            intensity: 0.35,
            eventType: .coughing,
            confidence: 0.90
        )
        event.determineSeverity()
        detectedEvents.append(event)
        totalEventsDetected += 1
        print("🧪 Simulated coughing event")
    }

    func simulateSleepTalking() {
        let event = SoundEvent(
            timestamp: Date(),
            duration: 2.8,
            intensity: 0.08,
            eventType: .sleepTalking,
            confidence: 0.75
        )
        event.determineSeverity()
        detectedEvents.append(event)
        totalEventsDetected += 1
        print("🧪 Simulated sleep talking event")
    }
}

enum SensitivityLevel: String, CaseIterable {
    case low = "Low"
    case medium = "Medium"
    case high = "High"
}

