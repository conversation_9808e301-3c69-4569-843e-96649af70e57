//
//  AudioPlaybackService.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 01/10/2025.
//

import AVFoundation
import Foundation
import Combine

class AudioPlaybackService: ObservableObject {
    @Published var isPlaying = false
    @Published var currentEvent: SoundEvent?
    @Published var playbackProgress: Double = 0.0
    @Published var duration: TimeInterval = 0
    
    private var audioPlayer: AVAudioPlayer?
    private var progressTimer: Timer?
    
    func playEvent(_ event: SoundEvent) {
        stopPlayback()
        
        currentEvent = event
        
        // Generate audio for the event
        // In a real app, you would have recorded the actual audio
        // For now, we'll generate representative audio based on event type
        generateAndPlayAudio(for: event)
        
        isPlaying = true
        startProgressTimer()
    }
    
    func stopPlayback() {
        audioPlayer?.stop()
        audioPlayer = nil
        isPlaying = false
        currentEvent = nil
        playbackProgress = 0.0
        stopProgressTimer()
    }
    
    func togglePlayPause() {
        if isPlaying {
            audioPlayer?.pause()
            isPlaying = false
            stopProgressTimer()
        } else {
            audioPlayer?.play()
            isPlaying = true
            startProgressTimer()
        }
    }
    
    func seek(to progress: Double) {
        guard let player = audioPlayer else { return }
        let time = progress * player.duration
        player.currentTime = time
        playbackProgress = progress
    }
    
    private func generateAndPlayAudio(for event: SoundEvent) {
        let sampleRate = 44100.0
        let samples = Int(sampleRate * event.duration)
        
        var audioData = [Float](repeating: 0, count: samples)
        
        switch event.eventType {
        case .snoring:
            // Generate low-frequency rumbling sound
            for i in 0..<samples {
                let time = Float(i) / Float(sampleRate)
                let freq = Float.random(in: 80...150)
                audioData[i] = sin(2.0 * .pi * freq * time) * Float(event.intensity) * 0.5
            }
            
        case .coughing:
            // Generate sharp burst sound
            for i in 0..<samples {
                let envelope = Float(i) / Float(samples)
                let decay = exp(-envelope * 5)
                audioData[i] = Float.random(in: -1...1) * decay * Float(event.intensity)
            }
            
        case .sleepTalking:
            // Generate speech-like modulated sound
            for i in 0..<samples {
                let time = Float(i) / Float(sampleRate)
                let carrier = sin(2.0 * .pi * 200 * time)
                let modulator = sin(2.0 * .pi * 5 * time)
                audioData[i] = carrier * (1 + modulator * 0.5) * Float(event.intensity) * 0.3
            }
            
        case .other:
            // Generate generic noise
            for i in 0..<samples {
                audioData[i] = Float.random(in: -1...1) * Float(event.intensity) * 0.2
            }
        }
        
        playAudioBuffer(audioData, sampleRate: sampleRate)
    }
    
    private func playAudioBuffer(_ audioData: [Float], sampleRate: Double) {
        let audioFormat = AVAudioFormat(standardFormatWithSampleRate: sampleRate, channels: 1)!
        let audioBuffer = AVAudioPCMBuffer(pcmFormat: audioFormat, frameCapacity: AVAudioFrameCount(audioData.count))!
        audioBuffer.frameLength = AVAudioFrameCount(audioData.count)
        
        let channelData = audioBuffer.floatChannelData![0]
        for i in 0..<audioData.count {
            channelData[i] = audioData[i]
        }
        
        // Create temporary file to play with AVAudioPlayer
        let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent("playback.wav")
        
        do {
            let audioFile = try AVAudioFile(forWriting: tempURL, settings: audioFormat.settings)
            try audioFile.write(from: audioBuffer)
            
            audioPlayer = try AVAudioPlayer(contentsOf: tempURL)
            audioPlayer?.prepareToPlay()
            audioPlayer?.play()
            
            duration = audioPlayer?.duration ?? 0
            
        } catch {
            print("Failed to create audio playback: \(error)")
        }
    }
    
    private func startProgressTimer() {
        stopProgressTimer()
        
        progressTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] _ in
            guard let self = self, let player = self.audioPlayer else { return }
            
            if player.isPlaying {
                self.playbackProgress = player.currentTime / player.duration
            } else {
                self.stopPlayback()
            }
        }
    }
    
    private func stopProgressTimer() {
        progressTimer?.invalidate()
        progressTimer = nil
    }
}

