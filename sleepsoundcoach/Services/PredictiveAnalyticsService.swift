//
//  PredictiveAnalyticsService.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 01/10/2025.
//

import Foundation
import SwiftData

@Observable
class PredictiveAnalyticsService {
    
    // MARK: - Sleep Quality Prediction
    
    /// Predict tonight's sleep quality based on today's activities
    func predictTonightsSleepQuality(
        todayExercise: Double?,
        todayCaffeine: Double?,
        todayHRV: Double?,
        todayMindfulness: Double?,
        historicalSessions: [SleepSession]
    ) -> SleepPrediction {
        
        guard historicalSessions.count >= 7 else {
            return SleepPrediction(
                predictedScore: 75,
                confidence: 0.3,
                factors: [],
                recommendation: "Not enough data yet. Keep tracking for better predictions!"
            )
        }
        
        var baseScore = calculateAverageScore(sessions: historicalSessions)
        var factors: [PredictionFactor] = []
        var confidence = 0.5
        
        // Exercise impact
        if let exercise = todayExercise {
            let exerciseImpact = calculateExerciseImpact(minutes: exercise, sessions: historicalSessions)
            baseScore += exerciseImpact.scoreChange
            factors.append(exerciseImpact)
            confidence += 0.1
        }
        
        // Caffeine impact
        if let caffeine = todayCaffeine {
            let caffeineImpact = calculateCaffeineImpact(mg: caffeine, sessions: historicalSessions)
            baseScore += caffeineImpact.scoreChange
            factors.append(caffeineImpact)
            confidence += 0.1
        }
        
        // HRV impact (stress indicator)
        if let hrv = todayHRV {
            let hrvImpact = calculateHRVImpact(hrv: hrv, sessions: historicalSessions)
            baseScore += hrvImpact.scoreChange
            factors.append(hrvImpact)
            confidence += 0.15
        }
        
        // Mindfulness impact
        if let mindfulness = todayMindfulness {
            let mindfulnessImpact = calculateMindfulnessImpact(minutes: mindfulness, sessions: historicalSessions)
            baseScore += mindfulnessImpact.scoreChange
            factors.append(mindfulnessImpact)
            confidence += 0.1
        }
        
        // Day of week pattern
        let dayOfWeekImpact = calculateDayOfWeekImpact(sessions: historicalSessions)
        baseScore += dayOfWeekImpact.scoreChange
        factors.append(dayOfWeekImpact)
        confidence += 0.05
        
        let finalScore = max(0, min(100, baseScore))
        let recommendation = generateRecommendation(score: finalScore, factors: factors)
        
        return SleepPrediction(
            predictedScore: finalScore,
            confidence: min(0.95, confidence),
            factors: factors.sorted { abs($0.scoreChange) > abs($1.scoreChange) },
            recommendation: recommendation
        )
    }
    
    // MARK: - Optimal Bedtime Calculator
    
    /// Calculate optimal bedtime based on historical data
    func calculateOptimalBedtime(
        targetWakeTime: Date,
        historicalSessions: [SleepSession]
    ) -> OptimalBedtime {
        
        guard historicalSessions.count >= 14 else {
            // Default recommendation: 8 hours before wake time
            let bedtime = Calendar.current.date(byAdding: .hour, value: -8, to: targetWakeTime)!
            return OptimalBedtime(
                recommendedBedtime: bedtime,
                confidence: 0.3,
                reasoning: "Based on standard 8-hour sleep recommendation. Track more nights for personalized timing.",
                sleepCycles: 5
            )
        }
        
        // Analyze best performing sessions
        let topSessions = historicalSessions
            .sorted { $0.quietScore > $1.quietScore }
            .prefix(10)
        
        // Calculate average duration of best sessions
        let avgDuration = topSessions.map { $0.duration }.reduce(0, +) / Double(topSessions.count)
        
        // Calculate average bedtime of best sessions
        let avgBedtimeHour = topSessions.map { session -> Double in
            let components = Calendar.current.dateComponents([.hour, .minute], from: session.startTime)
            return Double(components.hour ?? 22) + Double(components.minute ?? 0) / 60.0
        }.reduce(0, +) / Double(topSessions.count)
        
        // Calculate optimal bedtime
        let hoursBeforeWake = avgDuration / 3600.0
        let bedtime = Calendar.current.date(byAdding: .hour, value: -Int(hoursBeforeWake), to: targetWakeTime)!
        
        // Calculate sleep cycles (90 minutes each)
        let cycles = Int(hoursBeforeWake / 1.5)
        
        let reasoning = """
        Based on your best \(topSessions.count) nights:
        • Average sleep duration: \(String(format: "%.1f", hoursBeforeWake)) hours
        • Optimal bedtime window: \(formatTime(avgBedtimeHour)) - \(formatTime(avgBedtimeHour + 0.5))
        • This allows for \(cycles) complete sleep cycles
        """
        
        return OptimalBedtime(
            recommendedBedtime: bedtime,
            confidence: 0.8,
            reasoning: reasoning,
            sleepCycles: cycles
        )
    }
    
    // MARK: - Sleep Debt Tracking
    
    /// Calculate current sleep debt
    func calculateSleepDebt(sessions: [SleepSession]) -> SleepDebt {
        let last7Days = sessions.prefix(7)
        guard !last7Days.isEmpty else {
            return SleepDebt(totalDebt: 0, dailyDeficit: [], recommendation: "Start tracking to monitor sleep debt")
        }
        
        let optimalSleep: Double = 8 * 3600 // 8 hours in seconds
        var totalDebt: Double = 0
        var dailyDeficit: [(date: Date, deficit: Double)] = []
        
        for session in last7Days {
            let deficit = optimalSleep - session.duration
            if deficit > 0 {
                totalDebt += deficit
                dailyDeficit.append((session.startTime, deficit))
            }
        }
        
        let debtHours = totalDebt / 3600.0
        let recommendation = generateSleepDebtRecommendation(debtHours: debtHours)
        
        return SleepDebt(
            totalDebt: debtHours,
            dailyDeficit: dailyDeficit,
            recommendation: recommendation
        )
    }
    
    // MARK: - Anomaly Detection
    
    /// Detect unusual patterns in health metrics
    func detectAnomalies(session: SleepSession, historicalSessions: [SleepSession]) -> [HealthAnomaly] {
        guard historicalSessions.count >= 7 else { return [] }
        
        var anomalies: [HealthAnomaly] = []
        
        // HRV anomaly
        if let hrv = session.heartRateVariability {
            let avgHRV = historicalSessions.compactMap { $0.heartRateVariability }.reduce(0, +) / Double(historicalSessions.compactMap { $0.heartRateVariability }.count)
            if hrv < avgHRV * 0.7 {
                anomalies.append(HealthAnomaly(
                    type: .lowHRV,
                    severity: .high,
                    message: "Your HRV is \(String(format: "%.0f", ((avgHRV - hrv) / avgHRV) * 100))% lower than average",
                    recommendation: "This may indicate high stress or poor recovery. Consider rest and stress management."
                ))
            }
        }
        
        // Heart rate anomaly
        if let hr = session.averageHeartRate {
            let avgHR = historicalSessions.compactMap { $0.averageHeartRate }.reduce(0, +) / Double(historicalSessions.compactMap { $0.averageHeartRate }.count)
            if hr > avgHR * 1.15 {
                anomalies.append(HealthAnomaly(
                    type: .elevatedHeartRate,
                    severity: .medium,
                    message: "Your heart rate was \(String(format: "%.0f", hr - avgHR)) bpm higher than average",
                    recommendation: "Elevated heart rate during sleep may indicate stress, illness, or poor sleep environment."
                ))
            }
        }
        
        // Respiratory rate anomaly
        if let respRate = session.respiratoryRate {
            if respRate > 20 || respRate < 12 {
                anomalies.append(HealthAnomaly(
                    type: .abnormalBreathing,
                    severity: .high,
                    message: "Your respiratory rate was \(String(format: "%.1f", respRate)) breaths/min (normal: 12-20)",
                    recommendation: "Abnormal breathing patterns may indicate sleep apnea or other issues. Consider consulting a doctor."
                ))
            }
        }
        
        // Oxygen saturation anomaly
        if let oxygen = session.oxygenSaturation {
            if oxygen < 95 {
                anomalies.append(HealthAnomaly(
                    type: .lowOxygen,
                    severity: .high,
                    message: "Your oxygen saturation was \(String(format: "%.1f", oxygen))% (normal: >95%)",
                    recommendation: "Low oxygen levels during sleep require medical attention. Please consult a doctor."
                ))
            }
        }
        
        return anomalies
    }
    
    // MARK: - Helper Methods
    
    private func calculateAverageScore(sessions: [SleepSession]) -> Double {
        sessions.map { Double($0.quietScore) }.reduce(0, +) / Double(sessions.count)
    }
    
    private func calculateExerciseImpact(minutes: Double, sessions: [SleepSession]) -> PredictionFactor {
        let withExercise = sessions.filter { ($0.exerciseMinutes ?? 0) >= 30 }
        let withoutExercise = sessions.filter { ($0.exerciseMinutes ?? 0) < 30 }

        guard !withExercise.isEmpty && !withoutExercise.isEmpty else {
            return PredictionFactor(name: "Exercise", scoreChange: 0, description: "Insufficient data")
        }

        let avgWith = withExercise.map { Double($0.quietScore) }.reduce(0, +) / Double(withExercise.count)
        let avgWithout = withoutExercise.map { Double($0.quietScore) }.reduce(0, +) / Double(withoutExercise.count)
        let impact = avgWith - avgWithout

        return PredictionFactor(
            name: "Exercise",
            scoreChange: minutes >= 30 ? impact : -impact / 2.0,
            description: minutes >= 30 ? "Good exercise today" : "Limited exercise today"
        )
    }
    
    private func calculateCaffeineImpact(mg: Double, sessions: [SleepSession]) -> PredictionFactor {
        let impact = mg > 200 ? -5.0 : 0.0
        return PredictionFactor(
            name: "Caffeine",
            scoreChange: impact,
            description: mg > 200 ? "High caffeine intake" : "Moderate caffeine"
        )
    }
    
    private func calculateHRVImpact(hrv: Double, sessions: [SleepSession]) -> PredictionFactor {
        let avgHRV = sessions.compactMap { $0.heartRateVariability }.reduce(0, +) / Double(max(1, sessions.compactMap { $0.heartRateVariability }.count))
        let percentDiff = ((hrv - avgHRV) / avgHRV) * 100
        let impact = percentDiff * 0.2 // 20% HRV change = 4 point score change
        
        return PredictionFactor(
            name: "HRV (Stress)",
            scoreChange: impact,
            description: hrv > avgHRV ? "Good recovery" : "Elevated stress"
        )
    }
    
    private func calculateMindfulnessImpact(minutes: Double, sessions: [SleepSession]) -> PredictionFactor {
        let impact = minutes > 10 ? 3.0 : 0.0
        return PredictionFactor(
            name: "Mindfulness",
            scoreChange: impact,
            description: minutes > 10 ? "Meditated today" : "No meditation"
        )
    }
    
    private func calculateDayOfWeekImpact(sessions: [SleepSession]) -> PredictionFactor {
        let today = Calendar.current.component(.weekday, from: Date())
        let isWeekend = today == 1 || today == 7
        
        let weekendSessions = sessions.filter {
            let day = Calendar.current.component(.weekday, from: $0.startTime)
            return day == 1 || day == 7
        }
        let weekdaySessions = sessions.filter {
            let day = Calendar.current.component(.weekday, from: $0.startTime)
            return day != 1 && day != 7
        }
        
        guard !weekendSessions.isEmpty && !weekdaySessions.isEmpty else {
            return PredictionFactor(name: "Day of Week", scoreChange: 0, description: "No pattern detected")
        }

        let avgWeekend = weekendSessions.map { Double($0.quietScore) }.reduce(0, +) / Double(weekendSessions.count)
        let avgWeekday = weekdaySessions.map { Double($0.quietScore) }.reduce(0, +) / Double(weekdaySessions.count)
        let impact = isWeekend ? (avgWeekend - avgWeekday) : 0.0
        
        return PredictionFactor(
            name: "Day of Week",
            scoreChange: impact,
            description: isWeekend ? "Weekend sleep pattern" : "Weekday sleep pattern"
        )
    }
    
    private func generateRecommendation(score: Double, factors: [PredictionFactor]) -> String {
        if score >= 85 {
            return "Great! You're set up for excellent sleep tonight. Keep up your healthy habits!"
        } else if score >= 70 {
            return "Good sleep expected. \(factors.first(where: { $0.scoreChange < 0 })?.description ?? "Maintain your routine")"
        } else {
            let negativeFactors = factors.filter { $0.scoreChange < 0 }.map { $0.name }.joined(separator: ", ")
            return "Sleep quality may be affected by: \(negativeFactors). Consider adjusting these factors."
        }
    }
    
    private func generateSleepDebtRecommendation(debtHours: Double) -> String {
        if debtHours == 0 {
            return "Excellent! You have no sleep debt. Keep maintaining your sleep schedule."
        } else if debtHours < 3 {
            return "Minor sleep debt of \(String(format: "%.1f", debtHours)) hours. Try going to bed 30 minutes earlier tonight."
        } else if debtHours < 7 {
            return "Moderate sleep debt of \(String(format: "%.1f", debtHours)) hours. Plan for 1-2 recovery nights with extra sleep."
        } else {
            return "Significant sleep debt of \(String(format: "%.1f", debtHours)) hours. Prioritize sleep this week and consider naps if possible."
        }
    }
    
    private func formatTime(_ hour: Double) -> String {
        let h = Int(hour)
        let m = Int((hour - Double(h)) * 60)
        let period = h >= 12 ? "PM" : "AM"
        let displayHour = h > 12 ? h - 12 : (h == 0 ? 12 : h)
        return String(format: "%d:%02d %@", displayHour, m, period)
    }
}

// MARK: - Models

struct SleepPrediction {
    let predictedScore: Double
    let confidence: Double
    let factors: [PredictionFactor]
    let recommendation: String
    
    var qualityLevel: String {
        if predictedScore >= 85 { return "Excellent" }
        else if predictedScore >= 70 { return "Good" }
        else if predictedScore >= 55 { return "Fair" }
        else { return "Poor" }
    }
}

struct PredictionFactor {
    let name: String
    let scoreChange: Double
    let description: String
    
    var isPositive: Bool { scoreChange > 0 }
}

struct OptimalBedtime {
    let recommendedBedtime: Date
    let confidence: Double
    let reasoning: String
    let sleepCycles: Int
}

struct SleepDebt {
    let totalDebt: Double // Hours
    let dailyDeficit: [(date: Date, deficit: Double)]
    let recommendation: String
    
    var severity: String {
        if totalDebt == 0 { return "None" }
        else if totalDebt < 3 { return "Minor" }
        else if totalDebt < 7 { return "Moderate" }
        else { return "Significant" }
    }
}

struct HealthAnomaly: Identifiable {
    let id = UUID()
    let type: AnomalyType
    let severity: AnomalySeverity
    let message: String
    let recommendation: String
    
    var icon: String {
        switch type {
        case .lowHRV: return "heart.text.square"
        case .elevatedHeartRate: return "heart.fill"
        case .abnormalBreathing: return "lungs.fill"
        case .lowOxygen: return "o.circle.fill"
        }
    }
    
    var color: String {
        switch severity {
        case .low: return "FFD60A"
        case .medium: return "FF9F0A"
        case .high: return "FF453A"
        }
    }
}

enum AnomalyType {
    case lowHRV
    case elevatedHeartRate
    case abnormalBreathing
    case lowOxygen
}

enum AnomalySeverity {
    case low
    case medium
    case high
}

