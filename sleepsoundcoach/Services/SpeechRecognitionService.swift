//
//  SpeechRecognitionService.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 30/09/2025.
//

import Foundation
import Speech
import AVFoundation

@Observable
class SpeechRecognitionService {
    var isAuthorized = false
    
    func requestAuthorization() async -> <PERSON>ol {
        await withCheckedContinuation { continuation in
            SFSpeechRecognizer.requestAuthorization { status in
                continuation.resume(returning: status == .authorized)
            }
        }
    }
    
    func transcribeAudio(url: URL) async -> String? {
        guard isAuthorized else { return nil }
        
        let recognizer = SFSpeechRecognizer()
        guard recognizer?.isAvailable == true else { return nil }
        
        let request = SFSpeechURLRecognitionRequest(url: url)
        
        return await withCheckedContinuation { continuation in
            recognizer?.recognitionTask(with: request) { result, error in
                if let result = result, result.isFinal {
                    continuation.resume(returning: result.bestTranscription.formattedString)
                } else if error != nil {
                    continuation.resume(returning: nil)
                }
            }
        }
    }
}

