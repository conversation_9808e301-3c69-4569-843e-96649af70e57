//
//  sleepsoundcoachApp.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 30/09/2025.
//

import SwiftUI
import SwiftData

@main
struct sleepsoundcoachApp: App {
    // MIGRATION MANAGER: Handles schema changes gracefully
    var sharedModelContainer: ModelContainer = {
        print("🚀 SleepSound Coach: Initializing...")

        // Backup database before attempting migration
        if SwiftDataMigrationManager.isMigrationNeeded() {
            print("⚠️ Migration needed - backing up database...")
            _ = SwiftDataMigrationManager.backupDatabase()
        }

        // Use migration manager to create container with fallback strategies
        return SwiftDataMigrationManager.createModelContainer()
    }()

    // NEW: Store Manager for in-app purchases
    @State private var storeManager = StoreManager()

    var body: some Scene {
        WindowGroup {
            ContentView()
                .preferredColorScheme(.dark) // Lock app to dark mode for nighttime use
                .environment(storeManager) // NEW: Make StoreManager available throughout the app
        }
        .modelContainer(sharedModelContainer)
    }
}
