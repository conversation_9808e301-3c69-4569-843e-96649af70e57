//
//  ImageHelpers.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 01/10/2025.
//

import SwiftUI

/// Helper extension for safe image loading with fallbacks
extension Image {
    /// Creates an image with a fallback to a system icon if the named image doesn't exist
    static func safeLoad(_ name: String, fallbackIcon: String = "photo") -> some View {
        Group {
            if UIImage(named: name) != nil {
                Image(name)
                    .resizable()
            } else {
                // Fallback to system icon with gradient background
                ZStack {
                    Rectangle()
                        .fill(
                            LinearGradient(
                                colors: [
                                    ColorTheme.accent.opacity(0.3),
                                    ColorTheme.secondary.opacity(0.3)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                    
                    Image(systemName: fallbackIcon)
                        .font(.system(size: 60))
                        .foregroundStyle(
                            LinearGradient(
                                colors: [ColorTheme.accent, ColorTheme.secondary],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                }
            }
        }
    }
}

/// View modifier for photo backgrounds with gradient overlays
struct PhotoBackground: ViewModifier {
    let imageName: String
    let blur: CGFloat
    let overlayOpacity: Double
    
    func body(content: Content) -> some View {
        ZStack {
            if UIImage(named: imageName) != nil {
                Image(imageName)
                    .resizable()
                    .scaledToFill()
                    .ignoresSafeArea()
                    .blur(radius: blur)
                
                LinearGradient(
                    colors: [
                        Color.black.opacity(overlayOpacity),
                        Color.black.opacity(overlayOpacity * 0.7),
                        Color.black.opacity(overlayOpacity)
                    ],
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()
            } else {
                AnimatedBackground(isTracking: false)
            }
            
            content
        }
    }
}

extension View {
    /// Adds a photo background with gradient overlay, or falls back to animated background
    func photoBackground(_ imageName: String, blur: CGFloat = 10, overlayOpacity: Double = 0.8) -> some View {
        modifier(PhotoBackground(imageName: imageName, blur: blur, overlayOpacity: overlayOpacity))
    }
}

/// Safe image view that shows a placeholder if image doesn't exist
struct SafeImage: View {
    let name: String
    let fallbackIcon: String
    
    init(_ name: String, fallbackIcon: String = "photo") {
        self.name = name
        self.fallbackIcon = fallbackIcon
    }
    
    var body: some View {
        if UIImage(named: name) != nil {
            Image(name)
                .resizable()
        } else {
            ZStack {
                Rectangle()
                    .fill(Color.gray.opacity(0.2))
                
                Image(systemName: fallbackIcon)
                    .font(.largeTitle)
                    .foregroundColor(.gray.opacity(0.5))
            }
        }
    }
}

