//
//  HapticManager.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 30/09/2025.
//

import UIKit

class HapticManager {
    static let shared = HapticManager()
    
    private init() {}
    
    func impact(style: UIImpactFeedbackGenerator.FeedbackStyle) {
        let generator = UIImpactFeedbackGenerator(style: style)
        generator.impactOccurred()
    }
    
    func notification(type: UINotificationFeedbackGenerator.FeedbackType) {
        let generator = UINotificationFeedbackGenerator()
        generator.notificationOccurred(type)
    }
    
    func selection() {
        let generator = UISelectionFeedbackGenerator()
        generator.selectionChanged()
    }
    
    // Custom patterns
    func successPattern() {
        impact(style: .light)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.impact(style: .medium)
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            self.impact(style: .heavy)
        }
    }
    
    func errorPattern() {
        impact(style: .heavy)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.impact(style: .heavy)
        }
    }
    
    func gentlePulse() {
        impact(style: .light)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.impact(style: .light)
        }
    }
}

