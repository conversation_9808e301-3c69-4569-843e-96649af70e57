//
//  ColorTheme.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 01/10/2025.
//

import SwiftUI

struct ColorTheme {
    // MARK: - Primary Colors (Refined for Calming Nighttime Use)

    static let primary = Color(hex: "0A1128")  // Deep navy blue (refined)
    static let secondary = Color(hex: "2D1B4E")  // Soft purple (refined)
    static let accent = Color(hex: "64B5F6")  // Gentle sky blue (refined)

    // MARK: - Background Colors

    static let backgroundGradientStart = Color(hex: "050A1F")  // Very dark blue (refined)
    static let backgroundGradientEnd = Color(hex: "1A2332")  // Slightly lighter navy (refined)
    static let cardBackground = Color.white.opacity(0.08)
    static let cardBackgroundElevated = Color.white.opacity(0.12)

    // MARK: - Text Colors (Optimized for Dark Mode Readability)

    static let primaryText = Color.white
    static let secondaryText = Color.white.opacity(0.75)
    static let tertiaryText = Color.white.opacity(0.5)
    static let quaternaryText = Color.white.opacity(0.3)
    
    // MARK: - Gradient Colors (Calming Nighttime Palette)

    static let moonGradient = [Color.yellow.opacity(0.8), Color.orange.opacity(0.6)]
    static let waveformGradient = [Color(hex: "64B5F6"), Color(hex: "2D1B4E")]  // Refined

    static func scoreGradient(score: Int) -> [Color] {
        if score >= 80 {
            return [Color(hex: "66BB6A").opacity(0.8), Color(hex: "64B5F6").opacity(0.6)]  // Soft green + sky blue
        } else if score >= 60 {
            return [Color(hex: "64B5F6").opacity(0.8), Color(hex: "2D1B4E").opacity(0.6)]  // Sky blue + purple
        } else if score >= 40 {
            return [Color(hex: "FFA726").opacity(0.8), Color(hex: "FFD60A").opacity(0.6)]  // Warm amber + yellow
        } else {
            return [Color(hex: "EF5350").opacity(0.8), Color(hex: "FFA726").opacity(0.6)]  // Soft coral + amber
        }
    }

    // MARK: - Particle Colors

    static let particleColors = [Color.blue.opacity(0.3), Color.purple.opacity(0.3), Color.pink.opacity(0.2)]

    // MARK: - Chart Colors

    static let chartLineColor = Color(hex: "64B5F6")  // Refined
    static let chartAreaColor = Color(hex: "64B5F6").opacity(0.3)  // Refined
    static let chartSecondaryColor = Color(hex: "2D1B4E")  // Refined

    // MARK: - Event Type Colors

    static func eventColor(for type: SoundEventType) -> Color {
        switch type {
        case .snoring:
            return Color(hex: "64B5F6")  // Refined
        case .coughing:
            return Color(hex: "FFA726")  // Refined
        case .sleepTalking:
            return Color(hex: "2D1B4E")  // Refined
        case .other:
            return Color.gray
        }
    }

    // MARK: - Status Colors (Refined for Better Visibility)

    static let successColor = Color(hex: "66BB6A")  // Soft green
    static let warningColor = Color(hex: "FFA726")  // Warm amber
    static let errorColor = Color(hex: "EF5350")  // Soft coral
    static let infoColor = Color(hex: "42A5F5")  // Sky blue

    // MARK: - Sleep Sounds Colors

    static let rainColor = Color(hex: "64B5F6")  // Refined
    static let oceanColor = Color(hex: "00C7BE")
    static let forestColor = Color(hex: "66BB6A")  // Refined
    static let whiteNoiseColor = Color.gray
    static let brownNoiseColor = Color(hex: "8B7355")

    // MARK: - Semantic Colors for Intelligence Features

    static let correlationPositive = Color(hex: "66BB6A")  // Soft green
    static let correlationNegative = Color(hex: "EF5350")  // Soft coral
    static let correlationNeutral = Color(hex: "FFA726")  // Warm amber

    static let predictionExcellent = Color(hex: "66BB6A")  // Soft green
    static let predictionGood = Color(hex: "64B5F6")  // Sky blue
    static let predictionFair = Color(hex: "FFA726")  // Warm amber
    static let predictionPoor = Color(hex: "EF5350")  // Soft coral

    static let anomalyLow = Color(hex: "FFA726")  // Warm amber
    static let anomalyMedium = Color(hex: "FF9800")  // Orange
    static let anomalyHigh = Color(hex: "EF5350")  // Soft coral
}

// MARK: - Color Extension for Hex

extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (255, 0, 0, 0)
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}

// MARK: - View Extension for Dark Mode Styling

extension View {
    func themedBackground() -> some View {
        self.background(
            LinearGradient(
                colors: [
                    ColorTheme.backgroundGradientStart,
                    ColorTheme.backgroundGradientEnd
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
    }

    func themedCard(elevated: Bool = false) -> some View {
        self
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(elevated ? ColorTheme.cardBackgroundElevated : ColorTheme.cardBackground)
            )
    }

    func themedText(style: TextStyle = .primary) -> some View {
        self.foregroundColor(
            style == .primary ? ColorTheme.primaryText :
            style == .secondary ? ColorTheme.secondaryText :
            style == .tertiary ? ColorTheme.tertiaryText :
            ColorTheme.quaternaryText
        )
    }
}

enum TextStyle {
    case primary
    case secondary
    case tertiary
    case quaternary
}

