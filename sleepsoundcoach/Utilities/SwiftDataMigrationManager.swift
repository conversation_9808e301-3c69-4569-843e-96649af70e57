//
//  SwiftDataMigrationManager.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 01/10/2025.
//

import Foundation
import SwiftData

/// Manages SwiftData schema migrations and provides recovery strategies
class SwiftDataMigrationManager {
    
    /// Attempts to create a ModelContainer with automatic migration and fallback strategies
    static func createModelContainer() -> ModelContainer {
        print("🔄 SwiftDataMigrationManager: Starting ModelContainer creation...")
        
        // Define the current schema
        let schema = Schema([
            SleepSession.self,
            SoundEvent.self,
            UserPreferences.self,
            UserProfile.self,
            JournalEntry.self,
            InsightData.self
        ])
        
        // Strategy 1: Try normal creation with migration
        if let container = tryNormalCreation(schema: schema) {
            return container
        }
        
        // Strategy 2: Try deleting and recreating the database
        if let container = tryDatabaseReset(schema: schema) {
            return container
        }
        
        // Strategy 3: Use in-memory storage (temporary)
        if let container = tryInMemoryStorage(schema: schema) {
            return container
        }
        
        // Strategy 4: Minimal schema (last resort)
        if let container = tryMinimalSchema() {
            return container
        }
        
        // If all strategies fail, provide helpful error
        fatalError("""
        ❌ CRITICAL: All ModelContainer creation strategies failed
        
        SOLUTION:
        1. Delete the app completely
        2. Reinstall from Xcode
        3. If issue persists, check Xcode console for detailed errors
        
        This is a schema migration issue that requires a clean install.
        """)
    }
    
    // MARK: - Strategy 1: Normal Creation
    
    private static func tryNormalCreation(schema: Schema) -> ModelContainer? {
        print("📋 Strategy 1: Attempting normal creation with automatic migration...")
        
        let configuration = ModelConfiguration(
            schema: schema,
            isStoredInMemoryOnly: false,
            allowsSave: true
        )
        
        do {
            let container = try ModelContainer(for: schema, configurations: [configuration])
            print("✅ Strategy 1 SUCCESS: ModelContainer created with automatic migration")
            return container
        } catch {
            print("❌ Strategy 1 FAILED: \(error.localizedDescription)")
            return nil
        }
    }
    
    // MARK: - Strategy 2: Database Reset
    
    private static func tryDatabaseReset(schema: Schema) -> ModelContainer? {
        print("📋 Strategy 2: Attempting database reset...")
        
        // Get the default store URL
        guard let storeURL = getDefaultStoreURL() else {
            print("❌ Strategy 2 FAILED: Could not get store URL")
            return nil
        }
        
        print("📁 Store location: \(storeURL.path)")
        
        // Check if database exists
        if FileManager.default.fileExists(atPath: storeURL.path) {
            print("🗑️ Deleting existing database...")
            
            do {
                // Delete the database file
                try FileManager.default.removeItem(at: storeURL)
                print("✅ Database deleted successfully")
                
                // Also delete related files (WAL, SHM)
                let walURL = storeURL.deletingPathExtension().appendingPathExtension("sqlite-wal")
                let shmURL = storeURL.deletingPathExtension().appendingPathExtension("sqlite-shm")
                
                try? FileManager.default.removeItem(at: walURL)
                try? FileManager.default.removeItem(at: shmURL)
                
            } catch {
                print("⚠️ Could not delete database: \(error.localizedDescription)")
            }
        }
        
        // Try creating fresh container
        let configuration = ModelConfiguration(
            schema: schema,
            isStoredInMemoryOnly: false,
            allowsSave: true
        )
        
        do {
            let container = try ModelContainer(for: schema, configurations: [configuration])
            print("✅ Strategy 2 SUCCESS: Fresh database created")
            print("⚠️ NOTE: Previous data was lost due to migration failure")
            return container
        } catch {
            print("❌ Strategy 2 FAILED: \(error.localizedDescription)")
            return nil
        }
    }
    
    // MARK: - Strategy 3: In-Memory Storage
    
    private static func tryInMemoryStorage(schema: Schema) -> ModelContainer? {
        print("📋 Strategy 3: Attempting in-memory storage (temporary)...")
        
        let configuration = ModelConfiguration(
            schema: schema,
            isStoredInMemoryOnly: true
        )
        
        do {
            let container = try ModelContainer(for: schema, configurations: [configuration])
            print("✅ Strategy 3 SUCCESS: In-memory container created")
            print("⚠️ WARNING: Data will NOT persist between app launches")
            return container
        } catch {
            print("❌ Strategy 3 FAILED: \(error.localizedDescription)")
            return nil
        }
    }
    
    // MARK: - Strategy 4: Minimal Schema
    
    private static func tryMinimalSchema() -> ModelContainer? {
        print("📋 Strategy 4: Attempting minimal schema (last resort)...")
        
        let minimalSchema = Schema([
            SleepSession.self,
            SoundEvent.self
        ])
        
        let configuration = ModelConfiguration(
            schema: minimalSchema,
            isStoredInMemoryOnly: false
        )
        
        do {
            let container = try ModelContainer(for: minimalSchema, configurations: [configuration])
            print("✅ Strategy 4 SUCCESS: Minimal schema container created")
            print("⚠️ WARNING: Some features will be disabled (UserProfile, Preferences, etc.)")
            return container
        } catch {
            print("❌ Strategy 4 FAILED: \(error.localizedDescription)")
            return nil
        }
    }
    
    // MARK: - Helper Methods
    
    private static func getDefaultStoreURL() -> URL? {
        guard let appSupport = FileManager.default.urls(
            for: .applicationSupportDirectory,
            in: .userDomainMask
        ).first else {
            return nil
        }
        
        let bundleID = Bundle.main.bundleIdentifier ?? "com.sleepsoundcoach"
        let storeURL = appSupport
            .appendingPathComponent(bundleID)
            .appendingPathComponent("default.store")
        
        return storeURL
    }
    
    /// Checks if migration is needed by comparing schema versions
    static func isMigrationNeeded() -> Bool {
        // Check if UserProfile exists in current database
        // This is a simplified check - in production, use proper versioning
        guard let storeURL = getDefaultStoreURL() else {
            return false
        }
        
        return FileManager.default.fileExists(atPath: storeURL.path)
    }
    
    /// Backs up the current database before migration
    static func backupDatabase() -> Bool {
        guard let storeURL = getDefaultStoreURL() else {
            print("❌ Backup failed: Could not get store URL")
            return false
        }
        
        guard FileManager.default.fileExists(atPath: storeURL.path) else {
            print("ℹ️ No database to backup")
            return true
        }
        
        let backupURL = storeURL.deletingLastPathComponent()
            .appendingPathComponent("default.store.backup")
        
        do {
            // Remove old backup if exists
            if FileManager.default.fileExists(atPath: backupURL.path) {
                try FileManager.default.removeItem(at: backupURL)
            }
            
            // Create new backup
            try FileManager.default.copyItem(at: storeURL, to: backupURL)
            print("✅ Database backed up to: \(backupURL.path)")
            return true
        } catch {
            print("❌ Backup failed: \(error.localizedDescription)")
            return false
        }
    }
    
    /// Restores database from backup
    static func restoreFromBackup() -> Bool {
        guard let storeURL = getDefaultStoreURL() else {
            print("❌ Restore failed: Could not get store URL")
            return false
        }
        
        let backupURL = storeURL.deletingLastPathComponent()
            .appendingPathComponent("default.store.backup")
        
        guard FileManager.default.fileExists(atPath: backupURL.path) else {
            print("❌ Restore failed: No backup found")
            return false
        }
        
        do {
            // Remove current database
            if FileManager.default.fileExists(atPath: storeURL.path) {
                try FileManager.default.removeItem(at: storeURL)
            }
            
            // Restore from backup
            try FileManager.default.copyItem(at: backupURL, to: storeURL)
            print("✅ Database restored from backup")
            return true
        } catch {
            print("❌ Restore failed: \(error.localizedDescription)")
            return false
        }
    }
}

// MARK: - Migration Error Types

enum MigrationError: Error, LocalizedError {
    case schemaIncompatible
    case databaseCorrupted
    case backupFailed
    case restoreFailed
    
    var errorDescription: String? {
        switch self {
        case .schemaIncompatible:
            return "The database schema is incompatible with the current app version"
        case .databaseCorrupted:
            return "The database file is corrupted"
        case .backupFailed:
            return "Failed to create database backup"
        case .restoreFailed:
            return "Failed to restore database from backup"
        }
    }
}

