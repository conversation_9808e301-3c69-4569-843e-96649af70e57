//
//  SoundEffectsManager.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 30/09/2025.
//

import AVFoundation
import UIKit

class SoundEffectsManager {
    static let shared = SoundEffectsManager()
    
    private var audioPlayers: [String: AVAudioPlayer] = [:]
    private var ambientPlayer: AVAudioPlayer?
    
    private init() {
        setupAudioSession()
    }
    
    private func setupAudioSession() {
        do {
            try AVAudioSession.sharedInstance().setCategory(.ambient, mode: .default)
            try AVAudioSession.sharedInstance().setActive(true)
        } catch {
            print("Failed to setup audio session: \(error)")
        }
    }
    
    // Play system sounds using system sound IDs
    func playSystemSound(_ soundID: SystemSoundID) {
        AudioServicesPlaySystemSound(soundID)
    }
    
    // Predefined sound effects
    func playSuccess() {
        AudioServicesPlaySystemSound(1054) // Success sound
        HapticManager.shared.notification(type: .success)
    }
    
    func playError() {
        AudioServicesPlaySystemSound(1053) // Error sound
        HapticManager.shared.notification(type: .error)
    }
    
    func playTap() {
        AudioServicesPlaySystemSound(1104) // Tap sound
        HapticManager.shared.impact(style: .light)
    }
    
    func playNotification() {
        AudioServicesPlaySystemSound(1315) // Notification sound
        HapticManager.shared.notification(type: .success)
    }
    
    // Generate tones programmatically
    func playTone(frequency: Float, duration: TimeInterval) {
        let sampleRate = 44100.0
        let amplitude: Float = 0.3
        let samples = Int(sampleRate * duration)
        
        var audioData = [Float](repeating: 0, count: samples)
        
        for i in 0..<samples {
            let time = Float(i) / Float(sampleRate)
            audioData[i] = amplitude * sin(2.0 * .pi * frequency * time)
        }
        
        // Create audio buffer
        let audioFormat = AVAudioFormat(standardFormatWithSampleRate: sampleRate, channels: 1)!
        let audioBuffer = AVAudioPCMBuffer(pcmFormat: audioFormat, frameCapacity: AVAudioFrameCount(samples))!
        audioBuffer.frameLength = AVAudioFrameCount(samples)
        
        let channelData = audioBuffer.floatChannelData![0]
        for i in 0..<samples {
            channelData[i] = audioData[i]
        }
        
        // Play the buffer
        let audioEngine = AVAudioEngine()
        let playerNode = AVAudioPlayerNode()
        
        audioEngine.attach(playerNode)
        audioEngine.connect(playerNode, to: audioEngine.mainMixerNode, format: audioFormat)
        
        do {
            try audioEngine.start()
            playerNode.play()
            playerNode.scheduleBuffer(audioBuffer, at: nil, options: .interrupts, completionHandler: nil)
        } catch {
            print("Failed to play tone: \(error)")
        }
    }
    
    // Ambient sounds for relaxation
    func playAmbientSound(type: AmbientSoundType, volume: Float = 0.3) {
        // In a real app, you would load actual audio files
        // For now, we'll use system sounds or generate tones
        
        switch type {
        case .rain:
            // Generate white noise for rain effect
            playWhiteNoise(duration: 60, volume: volume)
        case .ocean:
            // Generate low frequency waves
            playOceanWaves(volume: volume)
        case .forest:
            // Generate nature sounds
            playForestAmbience(volume: volume)
        case .silence:
            stopAmbientSound()
        }
    }
    
    func stopAmbientSound() {
        ambientPlayer?.stop()
        ambientPlayer = nil
    }
    
    private func playWhiteNoise(duration: TimeInterval, volume: Float) {
        // Generate white noise
        let sampleRate = 44100.0
        let samples = Int(sampleRate * duration)
        
        var audioData = [Float](repeating: 0, count: samples)
        
        for i in 0..<samples {
            audioData[i] = Float.random(in: -volume...volume)
        }
        
        // This would need proper implementation with AVAudioEngine
        // For now, just a placeholder
    }
    
    private func playOceanWaves(volume: Float) {
        // Generate low frequency oscillating sound
        // Placeholder for actual implementation
    }
    
    private func playForestAmbience(volume: Float) {
        // Generate nature-like sounds
        // Placeholder for actual implementation
    }
    
    // Breathing exercise sounds
    func playBreathingCue(isInhale: Bool) {
        if isInhale {
            // Rising tone for inhale
            playTone(frequency: 440, duration: 0.3)
        } else {
            // Falling tone for exhale
            playTone(frequency: 330, duration: 0.3)
        }
        
        HapticManager.shared.gentlePulse()
    }
    
    // Sleep tracking sounds
    func playTrackingStartSound() {
        playTone(frequency: 523.25, duration: 0.2) // C5
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.15) {
            self.playTone(frequency: 659.25, duration: 0.2) // E5
        }
        HapticManager.shared.successPattern()
    }
    
    func playTrackingStopSound() {
        playTone(frequency: 659.25, duration: 0.2) // E5
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.15) {
            self.playTone(frequency: 523.25, duration: 0.2) // C5
        }
        HapticManager.shared.notification(type: .success)
    }
    
    // Sound event detection feedback
    func playSoundDetectedFeedback(type: SoundEventType) {
        switch type {
        case .snoring:
            playTone(frequency: 220, duration: 0.1)
        case .coughing:
            playTone(frequency: 440, duration: 0.1)
        case .sleepTalking:
            playTone(frequency: 330, duration: 0.1)
        case .other:
            playTone(frequency: 550, duration: 0.1)
        }
    }
}

enum AmbientSoundType {
    case rain
    case ocean
    case forest
    case silence
}

// Extension for sound event types
extension SoundEffectsManager {
    func playEventSound(for eventType: SoundEventType) {
        switch eventType {
        case .snoring:
            playTone(frequency: 200, duration: 0.5)
        case .coughing:
            playTone(frequency: 400, duration: 0.3)
        case .sleepTalking:
            playTone(frequency: 300, duration: 0.4)
        case .other:
            playTone(frequency: 350, duration: 0.3)
        }
    }
}

