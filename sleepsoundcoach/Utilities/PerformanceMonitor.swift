//
//  PerformanceMonitor.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 01/10/2025.
//

import Foundation
import UIKit

@Observable
class PerformanceMonitor {
    static let shared = PerformanceMonitor()
    
    // MARK: - Properties
    
    var cpuUsage: Double = 0.0
    var memoryUsage: Double = 0.0
    var batteryLevel: Float = 0.0
    var batteryState: UIDevice.BatteryState = .unknown
    
    private var timer: Timer?
    private var isMonitoring = false
    
    // MARK: - Initialization
    
    private init() {
        setupBatteryMonitoring()
    }
    
    // MARK: - Monitoring Control
    
    func startMonitoring() {
        guard !isMonitoring else { return }
        
        isMonitoring = true
        print("📊 Performance monitoring started")
        
        // Update every 5 seconds
        timer = Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) { [weak self] _ in
            self?.updateMetrics()
        }
        
        // Initial update
        updateMetrics()
    }
    
    func stopMonitoring() {
        guard isMonitoring else { return }
        
        isMonitoring = false
        timer?.invalidate()
        timer = nil
        print("📊 Performance monitoring stopped")
    }
    
    // MARK: - Metrics Update
    
    private func updateMetrics() {
        cpuUsage = getCPUUsage()
        memoryUsage = getMemoryUsage()
        batteryLevel = UIDevice.current.batteryLevel
        batteryState = UIDevice.current.batteryState
        
        // Log if metrics are concerning
        if memoryUsage > 100 {
            print("⚠️ High memory usage: \(String(format: "%.1f", memoryUsage)) MB")
        }
        
        if cpuUsage > 50 {
            print("⚠️ High CPU usage: \(String(format: "%.1f", cpuUsage))%")
        }
    }
    
    // MARK: - CPU Usage
    
    private func getCPUUsage() -> Double {
        var totalUsageOfCPU: Double = 0.0
        var threadsList = UnsafeMutablePointer<thread_act_t>(bitPattern: 0)
        var threadsCount = mach_msg_type_number_t(0)
        let threadsResult = withUnsafeMutablePointer(to: &threadsList) {
            return $0.withMemoryRebound(to: thread_act_array_t?.self, capacity: 1) {
                task_threads(mach_task_self_, $0, &threadsCount)
            }
        }
        
        if threadsResult == KERN_SUCCESS, let threadsList = threadsList {
            for index in 0..<threadsCount {
                var threadInfo = thread_basic_info()
                var threadInfoCount = mach_msg_type_number_t(THREAD_INFO_MAX)
                let infoResult = withUnsafeMutablePointer(to: &threadInfo) {
                    $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                        thread_info(threadsList[Int(index)], thread_flavor_t(THREAD_BASIC_INFO), $0, &threadInfoCount)
                    }
                }
                
                guard infoResult == KERN_SUCCESS else {
                    break
                }
                
                let threadBasicInfo = threadInfo as thread_basic_info
                if threadBasicInfo.flags & TH_FLAGS_IDLE == 0 {
                    totalUsageOfCPU = (totalUsageOfCPU + (Double(threadBasicInfo.cpu_usage) / Double(TH_USAGE_SCALE) * 100.0))
                }
            }
        }
        
        vm_deallocate(mach_task_self_, vm_address_t(UInt(bitPattern: threadsList)), vm_size_t(Int(threadsCount) * MemoryLayout<thread_t>.stride))
        return totalUsageOfCPU
    }
    
    // MARK: - Memory Usage
    
    private func getMemoryUsage() -> Double {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            return Double(info.resident_size) / 1024.0 / 1024.0 // Convert to MB
        }
        
        return 0.0
    }
    
    // MARK: - Battery Monitoring
    
    private func setupBatteryMonitoring() {
        UIDevice.current.isBatteryMonitoringEnabled = true
        
        NotificationCenter.default.addObserver(
            forName: UIDevice.batteryLevelDidChangeNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.batteryLevel = UIDevice.current.batteryLevel
        }
        
        NotificationCenter.default.addObserver(
            forName: UIDevice.batteryStateDidChangeNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.batteryState = UIDevice.current.batteryState
        }
    }
    
    // MARK: - Formatted Metrics
    
    var formattedCPUUsage: String {
        String(format: "%.1f%%", cpuUsage)
    }
    
    var formattedMemoryUsage: String {
        String(format: "%.1f MB", memoryUsage)
    }
    
    var formattedBatteryLevel: String {
        if batteryLevel < 0 {
            return "Unknown"
        }
        return String(format: "%.0f%%", batteryLevel * 100)
    }
    
    var batteryStateDescription: String {
        switch batteryState {
        case .charging:
            return "Charging"
        case .full:
            return "Full"
        case .unplugged:
            return "Unplugged"
        case .unknown:
            return "Unknown"
        @unknown default:
            return "Unknown"
        }
    }
    
    // MARK: - Performance Report
    
    func generateReport() -> String {
        """
        📊 Performance Report
        
        CPU Usage: \(formattedCPUUsage)
        Memory Usage: \(formattedMemoryUsage)
        Battery Level: \(formattedBatteryLevel)
        Battery State: \(batteryStateDescription)
        
        Status: \(getPerformanceStatus())
        """
    }
    
    private func getPerformanceStatus() -> String {
        if memoryUsage > 150 {
            return "⚠️ High memory usage detected"
        } else if cpuUsage > 70 {
            return "⚠️ High CPU usage detected"
        } else if memoryUsage > 100 || cpuUsage > 50 {
            return "⚡ Moderate resource usage"
        } else {
            return "✅ Performance is good"
        }
    }
}

// MARK: - Performance Optimization Tips

extension PerformanceMonitor {
    func getOptimizationTips() -> [String] {
        var tips: [String] = []
        
        if memoryUsage > 100 {
            tips.append("Consider reducing the number of cached audio samples")
            tips.append("Limit the number of sessions loaded in memory")
        }
        
        if cpuUsage > 50 {
            tips.append("Reduce FFT analysis frequency during low activity")
            tips.append("Optimize audio processing algorithms")
        }
        
        if batteryLevel < 0.2 && batteryState == .unplugged {
            tips.append("Consider reducing background processing")
            tips.append("Lower audio analysis frequency to save battery")
        }
        
        if tips.isEmpty {
            tips.append("Performance is optimal - no changes needed")
        }
        
        return tips
    }
}

