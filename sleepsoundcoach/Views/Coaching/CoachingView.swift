//
//  CoachingView.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 30/09/2025.
//

import SwiftUI
import AVFoundation

struct CoachingView: View {
    @State private var selectedTab = 0
    @State private var showingBreathingExercise = false
    
    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(spacing: 25) {
                    // Header
                    headerSection
                    
                    // Daily Tip
                    dailyTipCard
                    
                    // Breathing Exercise
                    breathingExerciseCard
                    
                    // Sleep Hygiene Tips
                    sleepHygieneTips
                    
                    // Motivational Quote
                    motivationalQuote
                }
                .padding()
            }
            .background(AnimatedBackground(isTracking: false))
            .navigationTitle("Sleep Coach")
            .sheet(isPresented: $showingBreathingExercise) {
                BreathingExerciseView()
            }
        }
    }
    
    private var headerSection: some View {
        VStack(spacing: 10) {
            Image(systemName: "figure.mind.and.body")
                .font(.system(size: 60))
                .foregroundStyle(
                    LinearGradient(
                        colors: [.blue, .purple],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
            
            Text("Your Sleep Coach")
                .font(.system(size: 32, weight: .bold))
                .foregroundColor(.white)
            
            Text("Better rest today, stronger tomorrow")
                .font(.subheadline)
                .foregroundColor(.white.opacity(0.7))
        }
        .padding()
    }
    
    private var dailyTipCard: some View {
        VStack(alignment: .leading, spacing: 15) {
            HStack {
                Image(systemName: "lightbulb.fill")
                    .foregroundStyle(.yellow)
                    .font(.title2)
                
                Text("Daily Tip")
                    .font(.headline)
                    .foregroundColor(.white)
            }
            
            Text(getDailyTip())
                .font(.body)
                .foregroundColor(.white.opacity(0.9))
                .fixedSize(horizontal: false, vertical: true)
        }
        .padding()
        .frame(maxWidth: .infinity, alignment: .leading)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
        )
    }
    
    private var breathingExerciseCard: some View {
        Button(action: { showingBreathingExercise = true }) {
            HStack(spacing: 15) {
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [.blue.opacity(0.3), .purple.opacity(0.3)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 60, height: 60)
                    
                    Image(systemName: "wind")
                        .font(.title2)
                        .foregroundStyle(.white)
                }
                
                VStack(alignment: .leading, spacing: 5) {
                    Text("Guided Breathing")
                        .font(.headline)
                        .foregroundColor(.white)
                    
                    Text("5-minute relaxation exercise")
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.7))
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .foregroundColor(.white.opacity(0.5))
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(.ultraThinMaterial)
            )
        }
    }
    
    private var sleepHygieneTips: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Sleep Hygiene Tips")
                .font(.headline)
                .foregroundColor(.white)
            
            SleepTipRowWithPhoto(
                photo: "coaching-photo-exercise",
                icon: "figure.run",
                title: "Exercise Earlier",
                description: "Get 10-15 minutes of sunlight within an hour of waking",
                color: .green
            )

            SleepTipRowWithPhoto(
                photo: "coaching-photo-caffeine",
                icon: "cup.and.saucer.fill",
                title: "Limit Caffeine",
                description: "Avoid caffeine 6 hours before bedtime",
                color: .brown
            )

            SleepTipRowWithPhoto(
                photo: "coaching-photo-routine",
                icon: "moon.zzz.fill",
                title: "Bedtime Routine",
                description: "Create a relaxing routine to signal your body it's time to sleep",
                color: .purple
            )

            SleepTipRowWithPhoto(
                photo: "lifestyle-photo-meditation",
                icon: "figure.mind.and.body",
                title: "Meditation",
                description: "Practice mindfulness to reduce stress and improve sleep quality",
                color: .cyan
            )
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
        )
    }
    
    private var motivationalQuote: some View {
        VStack(spacing: 15) {
            Image(systemName: "quote.opening")
                .font(.title)
                .foregroundStyle(.blue)
            
            Text(getMotivationalQuote())
                .font(.title3)
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
                .italic()
            
            Image(systemName: "quote.closing")
                .font(.title)
                .foregroundStyle(.blue)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
        )
    }
    
    private func getDailyTip() -> String {
        let tips = [
            "Keep your bedroom temperature between 60-67°F (15-19°C) for optimal sleep.",
            "Avoid screens 1 hour before bedtime to reduce blue light exposure.",
            "Try sleeping on your side to reduce snoring.",
            "Use blackout curtains to create a dark sleeping environment.",
            "Practice a relaxing bedtime routine to signal your body it's time to sleep.",
            "Exercise regularly, but not within 3 hours of bedtime.",
            "Keep a notepad by your bed to jot down worries and clear your mind."
        ]
        
        let dayOfYear = Calendar.current.ordinality(of: .day, in: .year, for: Date()) ?? 0
        return tips[dayOfYear % tips.count]
    }
    
    private func getMotivationalQuote() -> String {
        let quotes = [
            "Sleep is the best meditation.",
            "A good laugh and a long sleep are the best cures.",
            "Sleep is the golden chain that ties health and our bodies together.",
            "The best bridge between despair and hope is a good night's sleep.",
            "Sleep is that golden chain that ties health and our bodies together.",
            "Each night, when I go to sleep, I die. And the next morning, when I wake up, I am reborn."
        ]
        
        let dayOfYear = Calendar.current.ordinality(of: .day, in: .year, for: Date()) ?? 0
        return quotes[dayOfYear % quotes.count]
    }
}

struct SleepTipRow: View {
    let icon: String
    let title: String
    let description: String
    let color: Color

    var body: some View {
        HStack(alignment: .top, spacing: 15) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundStyle(color)
                .frame(width: 30)

            VStack(alignment: .leading, spacing: 5) {
                Text(title)
                    .font(.subheadline.bold())
                    .foregroundColor(.white)

                Text(description)
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
                    .fixedSize(horizontal: false, vertical: true)
            }
        }
    }
}

struct SleepTipRowWithPhoto: View {
    let photo: String
    let icon: String
    let title: String
    let description: String
    let color: Color

    var body: some View {
        ZStack(alignment: .leading) {
            // Background photo (with fallback)
            if UIImage(named: photo) != nil {
                Image(photo)
                    .resizable()
                    .scaledToFill()
                    .frame(height: 100)
                    .clipped()
                    .cornerRadius(15)
                    .blur(radius: 2)

                // Gradient overlay
                LinearGradient(
                    colors: [
                        Color.black.opacity(0.7),
                        Color.black.opacity(0.5)
                    ],
                    startPoint: .leading,
                    endPoint: .trailing
                )
                .cornerRadius(15)
            } else {
                // Fallback gradient background
                LinearGradient(
                    colors: [
                        color.opacity(0.3),
                        color.opacity(0.1)
                    ],
                    startPoint: .leading,
                    endPoint: .trailing
                )
                .frame(height: 100)
                .cornerRadius(15)
            }

            HStack(alignment: .center, spacing: 15) {
                // Icon
                ZStack {
                    Circle()
                        .fill(color.opacity(0.3))
                        .frame(width: 50, height: 50)

                    Image(systemName: icon)
                        .font(.title3)
                        .foregroundStyle(color)
                }
                .shadow(color: .black.opacity(0.5), radius: 4, x: 0, y: 2)

                VStack(alignment: .leading, spacing: 5) {
                    Text(title)
                        .font(.subheadline.bold())
                        .foregroundColor(.white)
                        .shadow(color: .black.opacity(0.5), radius: 2, x: 0, y: 1)

                    Text(description)
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.95))
                        .shadow(color: .black.opacity(0.5), radius: 2, x: 0, y: 1)
                        .fixedSize(horizontal: false, vertical: true)
                }

                Spacer()
            }
            .padding()
        }
        .frame(height: 100)
    }
}

struct BreathingExerciseView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var isBreathing = false
    @State private var breathPhase: BreathPhase = .inhale
    @State private var scale: CGFloat = 1.0
    @State private var cycleCount = 0
    
    let synthesizer = AVSpeechSynthesizer()
    
    enum BreathPhase {
        case inhale, hold, exhale, rest
        
        var instruction: String {
            switch self {
            case .inhale: return "Breathe In"
            case .hold: return "Hold"
            case .exhale: return "Breathe Out"
            case .rest: return "Rest"
            }
        }
        
        var duration: Double {
            switch self {
            case .inhale: return 4.0
            case .hold: return 4.0
            case .exhale: return 6.0
            case .rest: return 2.0
            }
        }
    }
    
    var body: some View {
        ZStack {
            AnimatedBackground(isTracking: false)

            FloatingParticles()
                .opacity(0.3)

            VStack(spacing: 50) {
                // Close button
                HStack {
                    Spacer()
                    Button(action: { dismiss() }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.title)
                            .foregroundColor(.white.opacity(0.7))
                    }
                }
                .padding()
                
                Spacer()
                
                // Breathing circle with enhanced animations
                ZStack {
                    // Outer pulsing rings
                    ForEach(0..<3, id: \.self) { index in
                        Circle()
                            .stroke(
                                LinearGradient(
                                    colors: [.blue.opacity(0.4), .purple.opacity(0.4)],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 2
                            )
                            .frame(width: 200, height: 200)
                            .scaleEffect(scale + CGFloat(index) * 0.2)
                            .opacity(1.0 - Double(index) * 0.3)
                    }

                    // Main breathing circle
                    Circle()
                        .fill(
                            RadialGradient(
                                colors: [
                                    .blue.opacity(0.7),
                                    .purple.opacity(0.6),
                                    .pink.opacity(0.4)
                                ],
                                center: .center,
                                startRadius: 50,
                                endRadius: 150
                            )
                        )
                        .frame(width: 200, height: 200)
                        .scaleEffect(scale)
                        .blur(radius: 8)
                        .glow(color: .blue, radius: 20)

                    // Inner circle
                    Circle()
                        .stroke(lineWidth: 4)
                        .fill(
                            AngularGradient(
                                colors: [.white, .blue, .purple, .white],
                                center: .center
                            )
                        )
                        .frame(width: 200, height: 200)
                        .scaleEffect(scale)
                        .glow(color: .white, radius: 5)
                }
                
                // Instruction text
                VStack(spacing: 10) {
                    Text(breathPhase.instruction)
                        .font(.system(size: 36, weight: .bold))
                        .foregroundColor(.white)
                    
                    if isBreathing {
                        Text("Cycle \(cycleCount + 1) of 5")
                            .font(.subheadline)
                            .foregroundColor(.white.opacity(0.7))
                    }
                }
                
                Spacer()
                
                // Start/Stop button
                Button(action: toggleBreathing) {
                    Text(isBreathing ? "Stop" : "Start Exercise")
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 15)
                                .fill(
                                    LinearGradient(
                                        colors: isBreathing ? [.red, .orange] : [.blue, .purple],
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    )
                                )
                        )
                }
                .padding(.horizontal, 40)
                .padding(.bottom, 50)
            }
        }
    }
    
    private func toggleBreathing() {
        if isBreathing {
            stopBreathing()
        } else {
            startBreathing()
        }
    }
    
    private func startBreathing() {
        isBreathing = true
        cycleCount = 0
        performBreathCycle()
    }
    
    private func stopBreathing() {
        isBreathing = false
        scale = 1.0
        breathPhase = .inhale
        synthesizer.stopSpeaking(at: .immediate)
    }
    
    private func performBreathCycle() {
        guard isBreathing else { return }

        // Inhale
        breathPhase = .inhale
        speak(breathPhase.instruction)
        SoundEffectsManager.shared.playBreathingCue(isInhale: true)
        withAnimation(.easeInOut(duration: breathPhase.duration)) {
            scale = 1.5
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + breathPhase.duration) {
            guard self.isBreathing else { return }

            // Hold
            self.breathPhase = .hold
            self.speak(self.breathPhase.instruction)
            HapticManager.shared.impact(style: .light)

            DispatchQueue.main.asyncAfter(deadline: .now() + self.breathPhase.duration) {
                guard self.isBreathing else { return }

                // Exhale
                self.breathPhase = .exhale
                self.speak(self.breathPhase.instruction)
                SoundEffectsManager.shared.playBreathingCue(isInhale: false)
                withAnimation(.easeInOut(duration: self.breathPhase.duration)) {
                    self.scale = 1.0
                }
                
                DispatchQueue.main.asyncAfter(deadline: .now() + self.breathPhase.duration) {
                    guard self.isBreathing else { return }
                    
                    // Rest
                    self.breathPhase = .rest
                    
                    DispatchQueue.main.asyncAfter(deadline: .now() + self.breathPhase.duration) {
                        self.cycleCount += 1
                        
                        if self.cycleCount < 5 {
                            self.performBreathCycle()
                        } else {
                            self.speak("Exercise complete. Well done!")
                            self.stopBreathing()
                        }
                    }
                }
            }
        }
    }
    
    private func speak(_ text: String) {
        let utterance = AVSpeechUtterance(string: text)
        utterance.voice = AVSpeechSynthesisVoice(language: "en-US")
        utterance.rate = 0.5
        synthesizer.speak(utterance)
    }
}

