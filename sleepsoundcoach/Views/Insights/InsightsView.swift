//
//  InsightsView.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 30/09/2025.
//

import SwiftUI
import SwiftData
import Charts

struct InsightsView: View {
    @Environment(\.modelContext) private var modelContext
    @Query(sort: \SleepSession.startTime, order: .reverse) private var sessions: [SleepSession]
    @Query private var journalEntries: [JournalEntry]

    @State private var selectedPeriod: TimePeriod = .week
    @State private var showingJournalSheet = false
    @State private var insightsService = InsightsService()
    @State private var isRefreshing = false
    @State private var correlationService = HealthCorrelationService()
    @State private var predictiveService = PredictiveAnalyticsService()
    
    enum TimePeriod: String, CaseIterable {
        case week = "Week"
        case month = "Month"
        case all = "All Time"
    }
    
    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(spacing: 25) {
                    // Period selector
                    periodSelector

                    // Key metrics
                    keyMetrics

                    // Quiet Score Trend
                    quietScoreTrend

                    // Event Distribution
                    eventDistribution

                    // AI Insights
                    aiInsights

                    // Correlation Insights (NEW)
                    correlationInsights

                    // Sleep Prediction (NEW)
                    sleepPrediction

                    // Pattern Detection
                    patternInsights

                    // Journal Section
                    journalSection
                }
                .padding()
            }
            .refreshable {
                await refreshInsights()
            }
            .background(AnimatedBackground(isTracking: false))
            .navigationTitle("Insights")
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button {
                        showingJournalSheet = true
                    } label: {
                        Image(systemName: "plus.circle.fill")
                            .foregroundColor(.white)
                    }
                }
            }
            .sheet(isPresented: $showingJournalSheet) {
                JournalEntrySheet()
            }
        }
    }

    private func refreshInsights() async {
        isRefreshing = true
        // Regenerate insights
        insightsService.cachedInsights = insightsService.generateWeeklyInsights(sessions: Array(filteredSessions))
        try? await Task.sleep(nanoseconds: 500_000_000)
        isRefreshing = false
    }
    
    private var periodSelector: some View {
        Picker("Period", selection: $selectedPeriod) {
            ForEach(TimePeriod.allCases, id: \.self) { period in
                Text(period.rawValue).tag(period)
            }
        }
        .pickerStyle(.segmented)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 15)
                .fill(.ultraThinMaterial)
        )
    }
    
    private var filteredSessions: [SleepSession] {
        let calendar = Calendar.current
        let now = Date()
        
        switch selectedPeriod {
        case .week:
            let weekAgo = calendar.date(byAdding: .day, value: -7, to: now)!
            return sessions.filter { $0.startTime >= weekAgo }
        case .month:
            let monthAgo = calendar.date(byAdding: .month, value: -1, to: now)!
            return sessions.filter { $0.startTime >= monthAgo }
        case .all:
            return Array(sessions)
        }
    }
    
    private var keyMetrics: some View {
        VStack(spacing: 15) {
            Text("Key Metrics")
                .font(.headline)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            HStack(spacing: 15) {
                MetricCard(
                    title: "Avg Quiet Score",
                    value: String(format: "%.0f", averageQuietScore),
                    icon: "moon.stars.fill",
                    color: .blue
                )
                
                MetricCard(
                    title: "Total Nights",
                    value: "\(filteredSessions.count)",
                    icon: "calendar",
                    color: .purple
                )
            }
            
            HStack(spacing: 15) {
                MetricCard(
                    title: "Avg Sleep",
                    value: averageSleepDuration,
                    icon: "bed.double.fill",
                    color: .green
                )
                
                MetricCard(
                    title: "Best Score",
                    value: "\(bestQuietScore)",
                    icon: "star.fill",
                    color: .yellow
                )
            }
        }
    }
    
    private var quietScoreTrend: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("Quiet Score Trend")
                .font(.headline)
                .foregroundColor(.white)
            
            if !filteredSessions.isEmpty {
                Chart(filteredSessions.reversed()) { session in
                    LineMark(
                        x: .value("Date", session.startTime),
                        y: .value("Score", session.quietScore)
                    )
                    .foregroundStyle(
                        LinearGradient(
                            colors: [.blue, .purple],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .interpolationMethod(.catmullRom)
                    
                    AreaMark(
                        x: .value("Date", session.startTime),
                        y: .value("Score", session.quietScore)
                    )
                    .foregroundStyle(
                        LinearGradient(
                            colors: [.blue.opacity(0.3), .purple.opacity(0.3)],
                            startPoint: .top,
                            endPoint: .bottom
                        )
                    )
                    .interpolationMethod(.catmullRom)
                }
                .frame(height: 200)
                .chartXAxis {
                    AxisMarks(values: .automatic) { _ in
                        AxisValueLabel(format: .dateTime.month().day())
                            .foregroundStyle(.white)
                    }
                }
                .chartYAxis {
                    AxisMarks { _ in
                        AxisValueLabel()
                            .foregroundStyle(.white)
                    }
                }
                .chartYScale(domain: 0...100)
            } else {
                Text("No data available for this period")
                    .foregroundColor(.white.opacity(0.6))
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
        )
    }
    
    private var eventDistribution: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("Event Distribution")
                .font(.headline)
                .foregroundColor(.white)
            
            if !filteredSessions.isEmpty {
                let totalSnores = filteredSessions.map { $0.totalSnoreEvents }.reduce(0, +)
                let totalCoughs = filteredSessions.map { $0.totalCoughEvents }.reduce(0, +)
                let totalTalks = filteredSessions.map { $0.totalSleepTalkEvents }.reduce(0, +)
                
                Chart {
                    BarMark(
                        x: .value("Type", "Snoring"),
                        y: .value("Count", totalSnores)
                    )
                    .foregroundStyle(.blue)
                    
                    BarMark(
                        x: .value("Type", "Coughing"),
                        y: .value("Count", totalCoughs)
                    )
                    .foregroundStyle(.orange)
                    
                    BarMark(
                        x: .value("Type", "Sleep Talking"),
                        y: .value("Count", totalTalks)
                    )
                    .foregroundStyle(.purple)
                }
                .frame(height: 200)
                .chartXAxis {
                    AxisMarks { _ in
                        AxisValueLabel()
                            .foregroundStyle(.white)
                    }
                }
                .chartYAxis {
                    AxisMarks { _ in
                        AxisValueLabel()
                            .foregroundStyle(.white)
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
        )
    }
    
    private var aiInsights: some View {
        VStack(alignment: .leading, spacing: 15) {
            HStack {
                Image(systemName: "sparkles")
                    .foregroundStyle(.yellow)
                Text("AI Insights")
                    .font(.headline)
                    .foregroundColor(.white)
            }

            let insights = insightsService.generateWeeklyInsights(sessions: filteredSessions)

            if insights.isEmpty {
                if UIImage(named: "empty-state-photo-data-waiting") != nil {
                    ZStack {
                        Image("empty-state-photo-data-waiting")
                            .resizable()
                            .scaledToFill()
                            .frame(height: 150)
                            .clipped()
                            .cornerRadius(15)
                            .blur(radius: 3)

                        LinearGradient(
                            colors: [
                                Color.black.opacity(0.6),
                                Color.black.opacity(0.4)
                            ],
                            startPoint: .top,
                            endPoint: .bottom
                        )
                        .cornerRadius(15)

                        Text("Keep tracking to unlock personalized insights!")
                            .foregroundColor(.white)
                            .fontWeight(.medium)
                            .shadow(color: .black.opacity(0.5), radius: 2, x: 0, y: 1)
                            .padding()
                    }
                    .frame(height: 150)
                } else {
                    Text("Keep tracking to unlock personalized insights!")
                        .foregroundColor(.white.opacity(0.7))
                        .padding()
                }
            } else {
                ForEach(insights, id: \.id) { insight in
                    InsightCard(insight: insight)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
        )
    }

    private var patternInsights: some View {
        VStack(alignment: .leading, spacing: 15) {
            HStack {
                Image(systemName: "brain.head.profile")
                    .foregroundStyle(ColorTheme.accent)
                Text("Pattern Detection")
                    .font(.headline)
                    .foregroundColor(.white)
            }

            if filteredSessions.count >= 7 {
                if let weekendPattern = insightsService.detectWeekendPattern(sessions: filteredSessions) {
                    InsightCard(insight: weekendPattern)
                }

                if let timePattern = insightsService.detectTimePattern(sessions: filteredSessions) {
                    InsightCard(insight: timePattern)
                }

                let recommendations = insightsService.generatePersonalizedRecommendations(sessions: filteredSessions, journalEntries: journalEntries)
                if !recommendations.isEmpty {
                    VStack(alignment: .leading, spacing: 10) {
                        Text("Personalized Recommendations")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.white.opacity(0.8))

                        ForEach(recommendations, id: \.self) { recommendation in
                            HStack(alignment: .top, spacing: 10) {
                                Image(systemName: "checkmark.circle.fill")
                                    .foregroundColor(ColorTheme.successColor)
                                    .font(.caption)

                                Text(recommendation)
                                    .font(.caption)
                                    .foregroundColor(.white.opacity(0.7))
                            }
                        }
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.white.opacity(0.05))
                    )
                }
            } else {
                Text("Need at least 7 days of data for pattern detection")
                    .foregroundColor(.white.opacity(0.6))
                    .font(.subheadline)
                    .padding()
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
        )
    }
    
    // NEW: Correlation Insights Section
    private var correlationInsights: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Health Correlations")
                .font(.headline)
                .foregroundColor(.white)

            let correlations = correlationService.getAllCorrelations(sessions: Array(filteredSessions))

            if !correlations.isEmpty {
                ForEach(correlations) { insight in
                    CorrelationInsightCard(insight: insight)
                }
            } else {
                VStack(spacing: 10) {
                    Image(systemName: "chart.line.uptrend.xyaxis")
                        .font(.largeTitle)
                        .foregroundStyle(ColorTheme.accent)

                    Text("Track for 7+ days with health data to see correlations")
                        .foregroundColor(.white.opacity(0.7))
                        .multilineTextAlignment(.center)
                }
                .frame(maxWidth: .infinity)
                .padding()
            }
        }
    }

    // NEW: Sleep Prediction Section
    private var sleepPrediction: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Tonight's Prediction")
                .font(.headline)
                .foregroundColor(.white)

            if filteredSessions.count >= 7 {
                // Get today's health data (mock for now - would fetch from HealthKit)
                let prediction = predictiveService.predictTonightsSleepQuality(
                    todayExercise: nil,
                    todayCaffeine: nil,
                    todayHRV: nil,
                    todayMindfulness: nil,
                    historicalSessions: Array(filteredSessions)
                )

                SleepPredictionCard(prediction: prediction)
            } else {
                VStack(spacing: 10) {
                    Image(systemName: "crystal.ball")
                        .font(.largeTitle)
                        .foregroundStyle(ColorTheme.accent)

                    Text("Track for 7+ days to get sleep predictions")
                        .foregroundColor(.white.opacity(0.7))
                        .multilineTextAlignment(.center)
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(.ultraThinMaterial)
                )
            }
        }
    }

    private var journalSection: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Sleep Journal")
                .font(.headline)
                .foregroundColor(.white)

            if journalEntries.isEmpty {
                VStack(spacing: 10) {
                    Image(systemName: "book.fill")
                        .font(.largeTitle)
                        .foregroundStyle(.blue)

                    Text("Start journaling to track your mood and energy")
                        .foregroundColor(.white.opacity(0.7))
                        .multilineTextAlignment(.center)
                }
                .frame(maxWidth: .infinity)
                .padding()
            } else {
                ForEach(journalEntries.prefix(3)) { entry in
                    JournalEntryRow(entry: entry)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
        )
    }

    // Computed properties
    private var averageQuietScore: Double {
        guard !filteredSessions.isEmpty else { return 0 }
        return Double(filteredSessions.map { $0.quietScore }.reduce(0, +)) / Double(filteredSessions.count)
    }
    
    private var bestQuietScore: Int {
        filteredSessions.map { $0.quietScore }.max() ?? 0
    }
    
    private var averageSleepDuration: String {
        guard !filteredSessions.isEmpty else { return "0h" }
        let avgSeconds = filteredSessions.map { $0.duration }.reduce(0, +) / Double(filteredSessions.count)
        let hours = Int(avgSeconds) / 3600
        let minutes = (Int(avgSeconds) % 3600) / 60
        return "\(hours)h \(minutes)m"
    }
}

struct MetricCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 10) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundStyle(color)
            
            Text(value)
                .font(.title2.bold())
                .foregroundColor(.white)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.white.opacity(0.7))
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 15)
                .fill(.ultraThinMaterial)
        )
    }
}

struct InsightCard: View {
    let insight: InsightData
    
    var body: some View {
        HStack(spacing: 15) {
            Image(systemName: insight.isPositive ? "arrow.up.circle.fill" : "arrow.down.circle.fill")
                .font(.title)
                .foregroundStyle(insight.isPositive ? .green : .orange)
            
            VStack(alignment: .leading, spacing: 5) {
                Text(insight.title)
                    .font(.subheadline.bold())
                    .foregroundColor(.white)
                
                Text(insight.message)
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.8))
            }
            
            Spacer()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.white.opacity(0.1))
        )
    }
}

struct JournalEntryRow: View {
    let entry: JournalEntry
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(entry.date, style: .date)
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.6))
                
                Spacer()
                
                Text(entry.moodEmoji)
                    .font(.title3)
            }
            
            Text(entry.content)
                .font(.subheadline)
                .foregroundColor(.white)
                .lineLimit(2)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.white.opacity(0.1))
        )
    }
}

struct JournalEntrySheet: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext
    
    @State private var content = ""
    @State private var mood = "Okay"
    @State private var energyLevel = 3
    
    let moods = ["Great", "Good", "Okay", "Poor", "Terrible"]
    
    var body: some View {
        NavigationStack {
            Form {
                Section("How are you feeling?") {
                    Picker("Mood", selection: $mood) {
                        ForEach(moods, id: \.self) { mood in
                            Text(mood).tag(mood)
                        }
                    }
                    .pickerStyle(.segmented)
                    
                    VStack(alignment: .leading) {
                        Text("Energy Level: \(energyLevel)")
                        Slider(value: Binding(
                            get: { Double(energyLevel) },
                            set: { energyLevel = Int($0) }
                        ), in: 1...5, step: 1)
                    }
                }
                
                Section("Notes") {
                    TextEditor(text: $content)
                        .frame(minHeight: 150)
                }
            }
            .navigationTitle("New Journal Entry")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        saveEntry()
                    }
                }
            }
        }
    }
    
    private func saveEntry() {
        let entry = JournalEntry(content: content, mood: mood, energyLevel: energyLevel)
        
        // Analyze sentiment
        let insightsService = InsightsService()
        entry.sentimentScore = insightsService.analyzeSentiment(text: content)
        
        modelContext.insert(entry)
        try? modelContext.save()
        
        dismiss()
    }
}

