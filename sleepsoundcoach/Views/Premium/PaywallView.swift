//
//  PaywallView.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 01/10/2025.
//

import SwiftUI
import StoreKit

struct PaywallView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var storeManager = StoreManager()
    @State private var selectedProduct: Product?
    @State private var isPurchasing = false
    @State private var showError = false
    
    var body: some View {
        ZStack {
            // Background - use photo if available, otherwise animated background
            if UIImage(named: "premium-photo-lifestyle") != nil {
                Image("premium-photo-lifestyle")
                    .resizable()
                    .scaledToFill()
                    .ignoresSafeArea()
                    .blur(radius: 12)

                // Gradient overlay
                LinearGradient(
                    colors: [
                        Color.black.opacity(0.85),
                        Color.black.opacity(0.7),
                        Color.black.opacity(0.85)
                    ],
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()
            } else {
                AnimatedBackground(isTracking: false)
            }

            ScrollView {
                VStack(spacing: 30) {
                    // Header with photo (or fallback)
                    ZStack {
                        if UIImage(named: "premium-photo-analytics") != nil {
                            Image("premium-photo-analytics")
                                .resizable()
                                .scaledToFill()
                                .frame(height: 250)
                                .clipped()
                                .cornerRadius(24)

                            LinearGradient(
                                colors: [
                                    Color.black.opacity(0.6),
                                    Color.black.opacity(0.3),
                                    Color.black.opacity(0.6)
                                ],
                                startPoint: .top,
                                endPoint: .bottom
                            )
                            .cornerRadius(24)
                        } else {
                            // Fallback gradient
                            LinearGradient(
                                colors: [
                                    ColorTheme.accent.opacity(0.4),
                                    ColorTheme.secondary.opacity(0.4)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                            .frame(height: 250)
                            .cornerRadius(24)
                        }

                        VStack(spacing: 16) {
                            Image(systemName: "moon.stars.fill")
                                .font(.system(size: 70))
                                .foregroundStyle(
                                    LinearGradient(
                                        colors: [ColorTheme.accent, ColorTheme.secondary],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                                .shadow(color: .black.opacity(0.5), radius: 10, x: 0, y: 5)

                            Text("Unlock Premium")
                                .font(.system(size: 36, weight: .bold, design: .rounded))
                                .foregroundColor(.white)
                                .shadow(color: .black.opacity(0.5), radius: 4, x: 0, y: 2)

                            Text("Get the most advanced sleep insights")
                                .font(.title3)
                                .foregroundColor(.white.opacity(0.95))
                                .shadow(color: .black.opacity(0.5), radius: 2, x: 0, y: 1)
                                .multilineTextAlignment(.center)
                        }
                    }
                    .frame(height: 250)
                    .padding(.top, 40)
                    
                    // Features
                    VStack(spacing: 16) {
                        PremiumFeatureRow(
                            icon: "waveform",
                            title: "Unlimited Sound Detection",
                            description: "See all detected events, not just 3 per night"
                        )

                        PremiumFeatureRow(
                            icon: "speaker.wave.3.fill",
                            title: "All 8 Sleep Sounds",
                            description: "Access the complete sound library"
                        )

                        PremiumFeatureRow(
                            icon: "chart.line.uptrend.xyaxis",
                            title: "Advanced Analytics",
                            description: "30 & 90-day trends, correlations, predictions"
                        )

                        PremiumFeatureRow(
                            icon: "brain.head.profile",
                            title: "AI-Powered Insights",
                            description: "Personalized recommendations and anomaly detection"
                        )

                        PremiumFeatureRow(
                            icon: "alarm.fill",
                            title: "Smart Alarm",
                            description: "Wake during optimal sleep phase"
                        )

                        PremiumFeatureRow(
                            icon: "play.circle.fill",
                            title: "Audio Playback",
                            description: "Listen to recorded sleep events"
                        )

                        PremiumFeatureRow(
                            icon: "square.and.arrow.up",
                            title: "Unlimited Exports",
                            description: "PDF & CSV reports anytime"
                        )

                        PremiumFeatureRow(
                            icon: "slider.horizontal.3",
                            title: "Custom Sensitivity",
                            description: "Adjust detection sensitivity to your needs"
                        )
                    }
                    .padding(20)
                    .background(.ultraThinMaterial)
                    .cornerRadius(24)
                    
                    // Pricing
                    if storeManager.isLoading {
                        ProgressView()
                            .tint(.white)
                            .scaleEffect(1.5)
                            .padding(40)
                    } else {
                        VStack(spacing: 16) {
                            if let yearly = storeManager.yearlyProduct {
                                PricingCard(
                                    product: yearly,
                                    isSelected: selectedProduct?.id == yearly.id,
                                    badge: "BEST VALUE",
                                    savings: "Save 33%"
                                ) {
                                    selectedProduct = yearly
                                }
                            }
                            
                            if let monthly = storeManager.monthlyProduct {
                                PricingCard(
                                    product: monthly,
                                    isSelected: selectedProduct?.id == monthly.id,
                                    badge: nil,
                                    savings: nil
                                ) {
                                    selectedProduct = monthly
                                }
                            }
                        }
                    }
                    
                    // Subscribe button
                    Button(action: {
                        Task {
                            await purchaseSelected()
                        }
                    }) {
                        HStack {
                            if isPurchasing {
                                ProgressView()
                                    .tint(.white)
                            } else {
                                Text("Start 7-Day Free Trial")
                                    .font(.headline)
                                    .foregroundColor(.white)
                            }
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(
                            LinearGradient(
                                colors: [ColorTheme.accent, ColorTheme.secondary],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .cornerRadius(16)
                    }
                    .disabled(selectedProduct == nil || isPurchasing)
                    .opacity(selectedProduct == nil ? 0.5 : 1.0)
                    
                    // Fine print
                    VStack(spacing: 8) {
                        Text("7-day free trial • Cancel anytime")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.7))
                        
                        Text("Subscription automatically renews unless cancelled")
                            .font(.caption2)
                            .foregroundColor(.white.opacity(0.5))
                            .multilineTextAlignment(.center)
                    }
                    
                    // Restore purchases
                    Button(action: {
                        Task {
                            await storeManager.restorePurchases()
                        }
                    }) {
                        Text("Restore Purchases")
                            .font(.subheadline)
                            .foregroundColor(.white.opacity(0.8))
                    }
                    .padding(.bottom, 20)
                }
                .padding()
            }
            
            // Close button
            VStack {
                HStack {
                    Spacer()
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.title)
                            .foregroundColor(.white.opacity(0.8))
                            .padding()
                    }
                }
                Spacer()
            }
        }
        .alert("Error", isPresented: $showError) {
            Button("OK", role: .cancel) { }
        } message: {
            Text(storeManager.errorMessage ?? "An error occurred")
        }
    }
    
    private func purchaseSelected() async {
        guard let product = selectedProduct else { return }
        
        isPurchasing = true
        
        do {
            let transaction = try await storeManager.purchase(product)
            if transaction != nil {
                // Purchase successful
                dismiss()
            }
        } catch {
            showError = true
        }
        
        isPurchasing = false
    }
}

// MARK: - Premium Feature Row

struct PremiumFeatureRow: View {
    let icon: String
    let title: String
    let description: String

    var body: some View {
        HStack(alignment: .top, spacing: 16) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(ColorTheme.accent)
                .frame(width: 32)

            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                    .foregroundColor(.white)

                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.7))
            }

            Spacer()
        }
    }
}

// MARK: - Pricing Card

struct PricingCard: View {
    let product: Product
    let isSelected: Bool
    let badge: String?
    let savings: String?
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 12) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(product.displayName)
                            .font(.headline)
                            .foregroundColor(.white)
                        
                        if let savings = savings {
                            Text(savings)
                                .font(.caption)
                                .foregroundColor(ColorTheme.successColor)
                        }
                    }
                    
                    Spacer()
                    
                    if let badge = badge {
                        Text(badge)
                            .font(.caption.bold())
                            .foregroundColor(.white)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(ColorTheme.successColor)
                            .cornerRadius(8)
                    }
                }
                
                HStack(alignment: .firstTextBaseline) {
                    Text(product.displayPrice)
                        .font(.system(size: 32, weight: .bold, design: .rounded))
                        .foregroundColor(.white)
                    
                    Text(pricePerMonth)
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.7))
                    
                    Spacer()
                    
                    if isSelected {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.title2)
                            .foregroundColor(ColorTheme.successColor)
                    }
                }
            }
            .padding(20)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(isSelected ? ColorTheme.accent.opacity(0.2) : Color.white.opacity(0.1))
            )
            .overlay(
                RoundedRectangle(cornerRadius: 20)
                    .stroke(isSelected ? ColorTheme.accent : Color.clear, lineWidth: 2)
            )
        }
    }
    
    private var pricePerMonth: String {
        if product.id.contains("yearly") {
            let price = product.price
            let monthly = price / 12
            return "/ \(monthly.formatted(.currency(code: product.priceFormatStyle.currencyCode))) per month"
        }
        return "/ month"
    }
}

// MARK: - Preview

#Preview {
    PaywallView()
}

