//
//  PremiumFeatureGate.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 01/10/2025.
//

import SwiftUI

struct PremiumFeatureGate: View {
    let feature: PremiumFeatureType
    let storeManager: StoreManager
    @State private var showPaywall = false
    
    var body: some View {
        VStack(spacing: 16) {
            // Icon
            Image(systemName: featureIcon)
                .font(.system(size: 50))
                .foregroundStyle(
                    LinearGradient(
                        colors: [ColorTheme.accent, ColorTheme.secondary],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
            
            // Title
            Text(featureTitle)
                .font(.title2.bold())
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
            
            // Description
            Text(featureDescription)
                .font(.subheadline)
                .foregroundColor(.white.opacity(0.8))
                .multilineTextAlignment(.center)
            
            // Upgrade button
            Button(action: {
                showPaywall = true
            }) {
                HStack {
                    Image(systemName: "crown.fill")
                    Text("Upgrade to Premium")
                }
                .font(.headline)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding()
                .background(
                    LinearGradient(
                        colors: [ColorTheme.accent, ColorTheme.secondary],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(16)
            }
        }
        .padding(30)
        .background(.ultraThinMaterial)
        .cornerRadius(24)
        .sheet(isPresented: $showPaywall) {
            PaywallView()
        }
    }
    
    private var featureIcon: String {
        switch feature {
        case .unlimitedSoundDetection: return "waveform"
        case .allSleepSounds: return "speaker.wave.3.fill"
        case .advancedAnalytics: return "chart.line.uptrend.xyaxis"
        case .aiInsights: return "brain.head.profile"
        case .smartAlarm: return "alarm.fill"
        case .unlimitedExports: return "square.and.arrow.up"
        case .audioPlayback: return "play.circle.fill"
        case .customSensitivity: return "slider.horizontal.3"
        }
    }
    
    private var featureTitle: String {
        switch feature {
        case .unlimitedSoundDetection: return "Unlimited Sound Detection"
        case .allSleepSounds: return "All Sleep Sounds"
        case .advancedAnalytics: return "Advanced Analytics"
        case .aiInsights: return "AI-Powered Insights"
        case .smartAlarm: return "Smart Alarm"
        case .unlimitedExports: return "Unlimited Exports"
        case .audioPlayback: return "Audio Playback"
        case .customSensitivity: return "Custom Sensitivity"
        }
    }
    
    private var featureDescription: String {
        switch feature {
        case .unlimitedSoundDetection:
            return "See all detected events throughout the night, not just the first 3"
        case .allSleepSounds:
            return "Access all 8 ambient sleep sounds to help you fall asleep"
        case .advancedAnalytics:
            return "View 30 & 90-day trends, correlations, and predictions"
        case .aiInsights:
            return "Get personalized recommendations and anomaly detection"
        case .smartAlarm:
            return "Wake up during your optimal sleep phase for better mornings"
        case .unlimitedExports:
            return "Export your sleep data as PDF or CSV anytime"
        case .audioPlayback:
            return "Listen to recordings of your sleep events"
        case .customSensitivity:
            return "Adjust detection sensitivity to match your needs"
        }
    }
}

// MARK: - Inline Upgrade Prompt

struct InlineUpgradePrompt: View {
    let message: String
    @State private var showPaywall = false
    
    var body: some View {
        Button(action: {
            showPaywall = true
        }) {
            HStack(spacing: 12) {
                Image(systemName: "crown.fill")
                    .font(.title3)
                    .foregroundColor(ColorTheme.warningColor)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("Premium Feature")
                        .font(.subheadline.bold())
                        .foregroundColor(.white)
                    
                    Text(message)
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.6))
            }
            .padding()
            .background(
                LinearGradient(
                    colors: [ColorTheme.warningColor.opacity(0.2), ColorTheme.accent.opacity(0.2)],
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(ColorTheme.warningColor.opacity(0.5), lineWidth: 1)
            )
        }
        .sheet(isPresented: $showPaywall) {
            PaywallView()
        }
    }
}

// MARK: - Preview

#Preview {
    ZStack {
        ColorTheme.backgroundGradientStart.ignoresSafeArea()
        
        VStack(spacing: 20) {
            PremiumFeatureGate(
                feature: .advancedAnalytics,
                storeManager: StoreManager()
            )
            
            InlineUpgradePrompt(
                message: "Upgrade to see all detected events"
            )
        }
        .padding()
    }
}

