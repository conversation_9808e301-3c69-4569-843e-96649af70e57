//
//  OnboardingView.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 30/09/2025.
//

import SwiftUI
import SwiftData

struct OnboardingView: View {
    @State private var currentPage = 0
    @Binding var isOnboardingComplete: Bool
    
    var body: some View {
        ZStack {
            // Animated Background
            AnimatedBackground(isTracking: false)

            FloatingParticles()
                .opacity(0.4)

            TabView(selection: $currentPage) {
                WelcomePageView()
                    .tag(0)
                
                FeaturesPageView()
                    .tag(1)
                
                PrivacyPageView()
                    .tag(2)
                
                PermissionsPageView(isOnboardingComplete: $isOnboardingComplete)
                    .tag(3)
            }
            .tabViewStyle(.page(indexDisplayMode: .always))
            .indexViewStyle(.page(backgroundDisplayMode: .always))
        }
    }
}

struct WelcomePageView: View {
    var body: some View {
        VStack(spacing: 30) {
            Spacer()
            
            // App icon/logo
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [.blue.opacity(0.3), .purple.opacity(0.3)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 120, height: 120)
                
                Image(systemName: "moon.stars.fill")
                    .font(.system(size: 60))
                    .foregroundStyle(.white)
            }
            
            Text("Welcome to")
                .font(.title2)
                .foregroundColor(.white.opacity(0.8))
            
            Text("SleepSound Coach")
                .font(.system(size: 42, weight: .bold, design: .rounded))
                .foregroundStyle(
                    LinearGradient(
                        colors: [.blue, .purple],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
            
            Text("Your personal AI-powered sleep companion")
                .font(.title3)
                .foregroundColor(.white.opacity(0.7))
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)
            
            Spacer()
            
            Text("Swipe to continue")
                .font(.subheadline)
                .foregroundColor(.white.opacity(0.5))
                .padding(.bottom, 50)
        }
    }
}

struct FeaturesPageView: View {
    var body: some View {
        VStack(spacing: 40) {
            Text("What We Do")
                .font(.system(size: 36, weight: .bold))
                .foregroundColor(.white)
                .padding(.top, 60)
            
            VStack(spacing: 30) {
                FeatureRow(
                    icon: "waveform",
                    title: "Smart Sound Tracking",
                    description: "Detect snoring, coughing, and sleep talking with AI"
                )
                
                FeatureRow(
                    icon: "chart.line.uptrend.xyaxis",
                    title: "Detailed Insights",
                    description: "Understand your sleep patterns with beautiful charts"
                )
                
                FeatureRow(
                    icon: "heart.text.square.fill",
                    title: "HealthKit Integration",
                    description: "Correlate sounds with heart rate and sleep stages"
                )
                
                FeatureRow(
                    icon: "sparkles",
                    title: "AI Coaching",
                    description: "Get personalized tips to improve your sleep quality"
                )
            }
            .padding(.horizontal, 30)
            
            Spacer()
        }
    }
}

struct FeatureRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 20) {
            Image(systemName: icon)
                .font(.system(size: 30))
                .foregroundStyle(.blue)
                .frame(width: 50)
            
            VStack(alignment: .leading, spacing: 5) {
                Text(title)
                    .font(.headline)
                    .foregroundColor(.white)
                
                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.7))
            }
            
            Spacer()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 15)
                .fill(.white.opacity(0.1))
        )
    }
}

struct PrivacyPageView: View {
    var body: some View {
        VStack(spacing: 30) {
            Image(systemName: "lock.shield.fill")
                .font(.system(size: 80))
                .foregroundStyle(.green)
                .padding(.top, 60)
            
            Text("Privacy First")
                .font(.system(size: 36, weight: .bold))
                .foregroundColor(.white)
            
            VStack(spacing: 25) {
                PrivacyPoint(
                    icon: "iphone",
                    text: "All analysis happens on your device"
                )
                
                PrivacyPoint(
                    icon: "eye.slash.fill",
                    text: "We never see or store your audio"
                )
                
                PrivacyPoint(
                    icon: "hand.raised.fill",
                    text: "No data shared with third parties"
                )
                
                PrivacyPoint(
                    icon: "checkmark.seal.fill",
                    text: "You control all your data"
                )
            }
            .padding(.horizontal, 40)
            
            Spacer()
        }
    }
}

struct PrivacyPoint: View {
    let icon: String
    let text: String
    
    var body: some View {
        HStack(spacing: 15) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundStyle(.green)
                .frame(width: 40)
            
            Text(text)
                .font(.body)
                .foregroundColor(.white)
            
            Spacer()
        }
    }
}

struct PermissionsPageView: View {
    @Binding var isOnboardingComplete: Bool
    @State private var microphoneGranted = false
    @State private var healthKitGranted = false
    @State private var speechGranted = false
    
    @Environment(\.modelContext) private var modelContext
    
    var body: some View {
        VStack(spacing: 30) {
            Text("Permissions")
                .font(.system(size: 36, weight: .bold))
                .foregroundColor(.white)
                .padding(.top, 60)
            
            Text("We need a few permissions to provide the best experience")
                .font(.body)
                .foregroundColor(.white.opacity(0.7))
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)
            
            VStack(spacing: 20) {
                PermissionCard(
                    icon: "mic.fill",
                    title: "Microphone",
                    description: "To detect sleep sounds",
                    isGranted: microphoneGranted,
                    action: requestMicrophone
                )
                
                PermissionCard(
                    icon: "heart.fill",
                    title: "HealthKit",
                    description: "To correlate with sleep data",
                    isGranted: healthKitGranted,
                    action: requestHealthKit
                )
                
                PermissionCard(
                    icon: "text.bubble.fill",
                    title: "Speech Recognition",
                    description: "To transcribe sleep talking",
                    isGranted: speechGranted,
                    action: requestSpeech
                )
            }
            .padding(.horizontal, 30)
            
            Spacer()
            
            Button(action: completeOnboarding) {
                Text("Get Started")
                    .font(.headline)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        LinearGradient(
                            colors: [.blue, .purple],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(15)
            }
            .padding(.horizontal, 30)
            .padding(.bottom, 50)
        }
    }
    
    private func requestMicrophone() {
        HapticManager.shared.impact(style: .medium)
        Task {
            let service = SoundAnalysisService()
            microphoneGranted = await service.requestMicrophonePermission()
            if microphoneGranted {
                HapticManager.shared.notification(type: .success)
            }
        }
    }

    private func requestHealthKit() {
        HapticManager.shared.impact(style: .medium)
        Task {
            let service = HealthKitService()
            healthKitGranted = await service.requestAuthorization()
            if healthKitGranted {
                HapticManager.shared.notification(type: .success)
            }
        }
    }

    private func requestSpeech() {
        HapticManager.shared.impact(style: .medium)
        Task {
            let service = SpeechRecognitionService()
            speechGranted = await service.requestAuthorization()
            if speechGranted {
                HapticManager.shared.notification(type: .success)
            }
        }
    }

    private func completeOnboarding() {
        HapticManager.shared.successPattern()
        let preferences = UserPreferences()
        preferences.hasCompletedOnboarding = true
        preferences.hasGrantedMicrophonePermission = microphoneGranted
        preferences.hasGrantedHealthKitPermission = healthKitGranted

        modelContext.insert(preferences)
        try? modelContext.save()

        isOnboardingComplete = true
    }
}

struct PermissionCard: View {
    let icon: String
    let title: String
    let description: String
    let isGranted: Bool
    let action: () -> Void
    
    var body: some View {
        HStack(spacing: 15) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundStyle(isGranted ? .green : .blue)
                .frame(width: 40)
            
            VStack(alignment: .leading, spacing: 3) {
                Text(title)
                    .font(.headline)
                    .foregroundColor(.white)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
            }
            
            Spacer()
            
            if isGranted {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundStyle(.green)
                    .font(.title2)
            } else {
                Button("Allow") {
                    action()
                }
                .font(.subheadline)
                .foregroundColor(.blue)
                .padding(.horizontal, 15)
                .padding(.vertical, 8)
                .background(.white.opacity(0.2))
                .cornerRadius(8)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 15)
                .fill(.white.opacity(0.1))
        )
    }
}

