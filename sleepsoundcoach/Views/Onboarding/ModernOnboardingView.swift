//
//  ModernOnboardingView.swift
//  sleepsoundcoach
//
//  Modern, minimal onboarding with Apple-like aesthetic
//

import SwiftUI
import SwiftData

struct ModernOnboardingView: View {
    @Environment(\.modelContext) private var modelContext
    @State private var currentPage = 0
    @State private var showProfileSetup = false
    @State private var onboardingComplete = false
    
    var onComplete: () -> Void
    
    var body: some View {
        ZStack {
            if showProfileSetup {
                ProfileSetupView {
                    onboardingComplete = true
                    onComplete()
                }
                .transition(.move(edge: .trailing))
            } else {
                TabView(selection: $currentPage) {
                    WelcomeView()
                        .tag(0)
                    
                    FeaturesView()
                        .tag(1)
                    
                    PermissionsView()
                        .tag(2)
                    
                    GetStartedView {
                        withAnimation(.spring(response: 0.5)) {
                            showProfileSetup = true
                        }
                    }
                    .tag(3)
                }
                .tabViewStyle(.page)
                .indexViewStyle(.page(backgroundDisplayMode: .always))
                .transition(.move(edge: .leading))
            }
        }
        .preferredColorScheme(.dark)
    }
}

// MARK: - Welcome View

struct WelcomeView: View {
    @State private var animateIcon = false
    @State private var animateText = false

    var body: some View {
        ZStack {
            // Clean gradient background
            LinearGradient(
                colors: [
                    Color(hex: "0A1128"),
                    Color(hex: "1A1F3A"),
                    Color(hex: "2D1B4E")
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            // Subtle animated particles
            FloatingParticles()
                .opacity(0.15)

            VStack(spacing: 0) {
                Spacer()
                
                // App icon with elegant animation
                ZStack {
                    // Glow effect
                    Circle()
                        .fill(
                            RadialGradient(
                                colors: [
                                    ColorTheme.accent.opacity(0.3),
                                    ColorTheme.accent.opacity(0.1),
                                    Color.clear
                                ],
                                center: .center,
                                startRadius: 50,
                                endRadius: 120
                            )
                        )
                        .frame(width: 240, height: 240)
                        .blur(radius: 20)
                        .scaleEffect(animateIcon ? 1.2 : 0.8)
                    
                    // Icon
                    Image(systemName: "moon.stars.fill")
                        .font(.system(size: 100, weight: .light))
                        .foregroundStyle(
                            LinearGradient(
                                colors: [ColorTheme.accent, Color(hex: "90CAF9")],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .scaleEffect(animateIcon ? 1.0 : 0.5)
                        .rotationEffect(.degrees(animateIcon ? 0 : -180))
                }
                .padding(.bottom, 60)

                // Title and subtitle
                VStack(spacing: 12) {
                    Text("SleepSound Coach")
                        .font(.system(size: 38, weight: .bold, design: .rounded))
                        .foregroundColor(.white)
                        .opacity(animateText ? 1.0 : 0.0)
                        .offset(y: animateText ? 0 : 20)

                    Text("AI-Powered Sleep Intelligence")
                        .font(.system(size: 17, weight: .medium))
                        .foregroundColor(.white.opacity(0.7))
                        .opacity(animateText ? 1.0 : 0.0)
                        .offset(y: animateText ? 0 : 20)
                }
                .padding(.horizontal, 40)
                
                Spacer()
                
                // Feature highlights
                VStack(spacing: 16) {
                    FeatureHighlight(icon: "waveform", text: "Advanced Sound Detection")
                    FeatureHighlight(icon: "heart.text.square", text: "12 Health Metrics")
                    FeatureHighlight(icon: "brain.head.profile", text: "AI Insights & Predictions")
                }
                .opacity(animateText ? 1.0 : 0.0)
                .offset(y: animateText ? 0 : 30)
                .padding(.bottom, 80)
            }
        }
        .onAppear {
            withAnimation(.spring(response: 0.8, dampingFraction: 0.7)) {
                animateIcon = true
            }

            withAnimation(.easeOut(duration: 0.8).delay(0.3)) {
                animateText = true
            }
        }
    }
}

struct FeatureHighlight: View {
    let icon: String
    let text: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(ColorTheme.accent)
                .frame(width: 24)
            
            Text(text)
                .font(.system(size: 15, weight: .medium))
                .foregroundColor(.white.opacity(0.9))
            
            Spacer()
        }
        .padding(.horizontal, 50)
    }
}

// MARK: - Features View

struct FeaturesView: View {
    @State private var animateCards = false
    
    var body: some View {
        ZStack {
            LinearGradient(
                colors: [
                    Color(hex: "0A1128"),
                    Color(hex: "1A1F3A"),
                    Color(hex: "2D1B4E")
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            VStack(spacing: 30) {
                VStack(spacing: 8) {
                    Text("Powerful Features")
                        .font(.system(size: 32, weight: .bold))
                        .foregroundColor(.white)
                    
                    Text("Everything you need for better sleep")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white.opacity(0.7))
                }
                .padding(.top, 80)
                
                ScrollView(showsIndicators: false) {
                    VStack(spacing: 20) {
                        ModernFeatureCard(
                            icon: "waveform.path.ecg",
                            title: "Sound Detection",
                            description: "Detect snoring, coughing, and sleep talking with advanced AI",
                            color: ColorTheme.accent,
                            delay: 0.0
                        )
                        
                        ModernFeatureCard(
                            icon: "heart.text.square.fill",
                            title: "Health Integration",
                            description: "Track 12 health metrics from HealthKit for complete insights",
                            color: Color(hex: "FF6B9D"),
                            delay: 0.1
                        )
                        
                        ModernFeatureCard(
                            icon: "brain.head.profile",
                            title: "AI Predictions",
                            description: "Get personalized sleep predictions and recommendations",
                            color: Color(hex: "C77DFF"),
                            delay: 0.2
                        )
                        
                        ModernFeatureCard(
                            icon: "chart.xyaxis.line",
                            title: "Trend Analysis",
                            description: "Visualize patterns and correlations over time",
                            color: Color(hex: "4ECDC4"),
                            delay: 0.3
                        )
                    }
                    .padding(.horizontal, 24)
                }
                .padding(.bottom, 60)
            }
        }
    }
}

struct ModernFeatureCard: View {
    let icon: String
    let title: String
    let description: String
    let color: Color
    let delay: Double
    
    @State private var animate = false
    
    var body: some View {
        HStack(alignment: .top, spacing: 16) {
            ZStack {
                RoundedRectangle(cornerRadius: 12)
                    .fill(color.opacity(0.15))
                    .frame(width: 56, height: 56)
                
                Image(systemName: icon)
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(color)
            }
            
            VStack(alignment: .leading, spacing: 6) {
                Text(title)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                
                Text(description)
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(.white.opacity(0.7))
                    .fixedSize(horizontal: false, vertical: true)
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
        )
        .opacity(animate ? 1.0 : 0.0)
        .offset(y: animate ? 0 : 30)
        .onAppear {
            withAnimation(.easeOut(duration: 0.6).delay(delay)) {
                animate = true
            }
        }
    }
}

// MARK: - Permissions View

struct PermissionsView: View {
    @State private var animateCards = false
    @State private var microphoneGranted = false
    @State private var healthKitGranted = false
    @State private var notificationsGranted = false

    var body: some View {
        ZStack {
            LinearGradient(
                colors: [
                    Color(hex: "0A1128"),
                    Color(hex: "1A1F3A"),
                    Color(hex: "2D1B4E")
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()

            VStack(spacing: 30) {
                VStack(spacing: 8) {
                    Image(systemName: "checkmark.shield.fill")
                        .font(.system(size: 60))
                        .foregroundStyle(
                            LinearGradient(
                                colors: [ColorTheme.accent, Color(hex: "90CAF9")],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .padding(.bottom, 10)

                    Text("Permissions")
                        .font(.system(size: 32, weight: .bold))
                        .foregroundColor(.white)

                    Text("We need a few permissions to work our magic")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white.opacity(0.7))
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 40)
                }
                .padding(.top, 60)

                VStack(spacing: 16) {
                    ModernPermissionCard(
                        icon: "mic.fill",
                        title: "Microphone",
                        description: "Detect sleep sounds like snoring and coughing",
                        isGranted: $microphoneGranted,
                        color: ColorTheme.accent,
                        action: requestMicrophone
                    )

                    ModernPermissionCard(
                        icon: "heart.fill",
                        title: "HealthKit",
                        description: "Access health data for comprehensive insights",
                        isGranted: $healthKitGranted,
                        color: Color(hex: "FF6B9D"),
                        action: requestHealthKit
                    )

                    ModernPermissionCard(
                        icon: "bell.fill",
                        title: "Notifications",
                        description: "Get morning reports and sleep reminders",
                        isGranted: $notificationsGranted,
                        color: Color(hex: "C77DFF"),
                        action: requestNotifications
                    )
                }
                .padding(.horizontal, 24)

                Spacer()

                Text("You can change these later in Settings")
                    .font(.system(size: 13, weight: .medium))
                    .foregroundColor(.white.opacity(0.5))
                    .padding(.bottom, 60)
            }
        }
    }

    private func requestMicrophone() {
        HapticManager.shared.impact(style: .medium)
        Task {
            let service = SoundAnalysisService()
            microphoneGranted = await service.requestMicrophonePermission()
            if microphoneGranted {
                HapticManager.shared.notification(type: .success)
            }
        }
    }

    private func requestHealthKit() {
        HapticManager.shared.impact(style: .medium)
        Task {
            let service = HealthKitService()
            healthKitGranted = await service.requestAuthorization()
            if healthKitGranted {
                HapticManager.shared.notification(type: .success)
            }
        }
    }

    private func requestNotifications() {
        HapticManager.shared.impact(style: .medium)
        Task {
            let service = NotificationService()
            notificationsGranted = await service.requestAuthorization()
            if notificationsGranted {
                HapticManager.shared.notification(type: .success)
            }
        }
    }
}

struct ModernPermissionCard: View {
    let icon: String
    let title: String
    let description: String
    @Binding var isGranted: Bool
    let color: Color
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(alignment: .center, spacing: 16) {
                ZStack {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(isGranted ? Color.green.opacity(0.15) : color.opacity(0.15))
                        .frame(width: 56, height: 56)

                    Image(systemName: isGranted ? "checkmark.circle.fill" : icon)
                        .font(.system(size: 24, weight: .medium))
                        .foregroundColor(isGranted ? .green : color)
                }

                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.system(size: 17, weight: .semibold))
                        .foregroundColor(.white)

                    Text(description)
                        .font(.system(size: 14, weight: .regular))
                        .foregroundColor(.white.opacity(0.7))
                        .fixedSize(horizontal: false, vertical: true)
                }

                Spacer()

                if !isGranted {
                    Image(systemName: "chevron.right")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(.white.opacity(0.3))
                }
            }
            .padding(20)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(.ultraThinMaterial)
            )
        }
        .disabled(isGranted)
    }
}

// MARK: - Get Started View

struct GetStartedView: View {
    let onContinue: () -> Void
    @State private var animate = false

    var body: some View {
        ZStack {
            LinearGradient(
                colors: [
                    Color(hex: "0A1128"),
                    Color(hex: "1A1F3A"),
                    Color(hex: "2D1B4E")
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()

            FloatingParticles()
                .opacity(0.15)

            VStack(spacing: 40) {
                Spacer()

                ZStack {
                    // Animated rings
                    ForEach(0..<3, id: \.self) { index in
                        Circle()
                            .stroke(
                                LinearGradient(
                                    colors: [ColorTheme.accent.opacity(0.3), Color.clear],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 2
                            )
                            .frame(width: 200 + CGFloat(index * 40), height: 200 + CGFloat(index * 40))
                            .scaleEffect(animate ? 1.2 : 1.0)
                            .opacity(animate ? 0.0 : 0.6)
                            .animation(
                                .easeOut(duration: 2.0)
                                .repeatForever(autoreverses: false)
                                .delay(Double(index) * 0.3),
                                value: animate
                            )
                    }

                    Image(systemName: "sparkles")
                        .font(.system(size: 80, weight: .light))
                        .foregroundStyle(
                            LinearGradient(
                                colors: [ColorTheme.accent, Color(hex: "90CAF9")],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                }
                .padding(.bottom, 40)

                VStack(spacing: 16) {
                    Text("You're All Set!")
                        .font(.system(size: 36, weight: .bold))
                        .foregroundColor(.white)

                    Text("Let's personalize your experience")
                        .font(.system(size: 17, weight: .medium))
                        .foregroundColor(.white.opacity(0.7))
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 40)
                }

                Spacer()

                Button(action: {
                    HapticManager.shared.impact(style: .medium)
                    onContinue()
                }) {
                    Text("Continue")
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 56)
                        .background(
                            LinearGradient(
                                colors: [ColorTheme.accent, Color(hex: "90CAF9")],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .cornerRadius(16)
                }
                .padding(.horizontal, 24)
                .padding(.bottom, 60)
            }
        }
        .onAppear {
            animate = true
        }
    }
}

