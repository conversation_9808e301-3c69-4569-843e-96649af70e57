//
//  EnhancedOnboardingView.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 01/10/2025.
//

import SwiftUI

struct EnhancedOnboardingView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var currentPage = 0
    @State private var showProfileSetup = false
    @State private var onboardingComplete = false
    
    var onComplete: () -> Void
    
    var body: some View {
        ZStack {
            if showProfileSetup {
                ProfileSetupView {
                    onboardingComplete = true
                    onComplete()
                }
                .transition(.move(edge: .trailing))
            } else {
                TabView(selection: $currentPage) {
                    WelcomeScreen()
                        .tag(0)
                    
                    FeaturesScreen()
                        .tag(1)
                    
                    IntelligenceScreen()
                        .tag(2)
                    
                    PermissionsScreen()
                        .tag(3)
                    
                    GetStartedScreen {
                        withAnimation(.spring(response: 0.5)) {
                            showProfileSetup = true
                        }
                    }
                    .tag(4)
                }
                .tabViewStyle(.page)
                .indexViewStyle(.page(backgroundDisplayMode: .always))
                .transition(.move(edge: .leading))
            }
        }
        .overlay(alignment: .topTrailing) {
            if !showProfileSetup && currentPage < 4 {
                Button(action: {
                    withAnimation(.spring(response: 0.5)) {
                        showProfileSetup = true
                    }
                }) {
                    Text("Skip")
                        .font(.headline)
                        .foregroundColor(.white.opacity(0.7))
                        .padding(.horizontal, 20)
                        .padding(.vertical, 10)
                }
                .padding()
            }
        }
    }
}

// MARK: - Welcome Screen

struct WelcomeScreen: View {
    @State private var animateIcon = false
    @State private var animateText = false

    var body: some View {
        ZStack {
            // Background - use photo if available, otherwise animated background
            if UIImage(named: "onboarding-welcome-hero") != nil {
                Image("onboarding-welcome-hero")
                    .resizable()
                    .scaledToFill()
                    .ignoresSafeArea()

                // Gradient overlay for text readability
                LinearGradient(
                    colors: [
                        Color.black.opacity(0.7),
                        Color.black.opacity(0.4),
                        Color.clear,
                        Color.black.opacity(0.3)
                    ],
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()
            } else {
                // Fallback to animated background
                AnimatedBackground(isTracking: false)
            }

            VStack(spacing: 40) {
                Spacer()

                // Circular photo overlay or icon fallback
                if UIImage(named: "onboarding-person-sleeping") != nil {
                    Image("onboarding-person-sleeping")
                        .resizable()
                        .scaledToFill()
                        .frame(width: 180, height: 180)
                        .clipShape(Circle())
                        .overlay(
                            Circle()
                                .stroke(
                                    LinearGradient(
                                        colors: [ColorTheme.accent, ColorTheme.secondary],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    ),
                                    lineWidth: 4
                                )
                        )
                        .shadow(color: ColorTheme.accent.opacity(0.5), radius: 20, x: 0, y: 10)
                        .scaleEffect(animateIcon ? 1.0 : 0.8)
                        .opacity(animateIcon ? 1.0 : 0.0)
                } else {
                    // Fallback to icon
                    ZStack {
                        Circle()
                            .fill(
                                LinearGradient(
                                    colors: [ColorTheme.accent.opacity(0.3), ColorTheme.secondary.opacity(0.3)],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .frame(width: 180, height: 180)
                            .scaleEffect(animateIcon ? 1.0 : 0.8)
                            .opacity(animateIcon ? 1.0 : 0.0)

                        Image(systemName: "moon.stars.fill")
                            .font(.system(size: 80))
                            .foregroundStyle(
                                LinearGradient(
                                    colors: [ColorTheme.accent, ColorTheme.secondary],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .scaleEffect(animateIcon ? 1.0 : 0.5)
                            .rotationEffect(.degrees(animateIcon ? 0 : -180))
                    }
                }

                VStack(spacing: 16) {
                    Text("Welcome to")
                        .font(.title2)
                        .foregroundColor(.white.opacity(0.9))
                        .shadow(color: .black.opacity(0.5), radius: 4, x: 0, y: 2)
                        .opacity(animateText ? 1.0 : 0.0)
                        .offset(y: animateText ? 0 : 20)

                    Text("SleepSound Coach")
                        .font(.system(size: 42, weight: .bold, design: .rounded))
                        .foregroundStyle(
                            LinearGradient(
                                colors: [ColorTheme.accent, ColorTheme.secondary],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .shadow(color: .black.opacity(0.5), radius: 4, x: 0, y: 2)
                        .opacity(animateText ? 1.0 : 0.0)
                        .offset(y: animateText ? 0 : 20)

                    Text("Your AI-Powered Sleep Companion")
                        .font(.title3)
                        .foregroundColor(.white.opacity(0.9))
                        .multilineTextAlignment(.center)
                        .shadow(color: .black.opacity(0.5), radius: 4, x: 0, y: 2)
                        .padding(.horizontal, 40)
                        .opacity(animateText ? 1.0 : 0.0)
                        .offset(y: animateText ? 0 : 20)
                }

                Spacer()

                VStack(spacing: 12) {
                    FeatureBadge(icon: "waveform", text: "Sound Detection")
                    FeatureBadge(icon: "heart.text.square", text: "Health Insights")
                    FeatureBadge(icon: "brain.head.profile", text: "AI Predictions")
                }
                .opacity(animateText ? 1.0 : 0.0)
                .offset(y: animateText ? 0 : 20)

                Text("Swipe to continue")
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.8))
                    .shadow(color: .black.opacity(0.5), radius: 2, x: 0, y: 1)
                    .padding(.bottom, 40)
                    .opacity(animateText ? 1.0 : 0.0)
            }
        }
        .onAppear {
            withAnimation(.spring(response: 0.8, dampingFraction: 0.7)) {
                animateIcon = true
            }

            withAnimation(.easeOut(duration: 0.6).delay(0.3)) {
                animateText = true
            }
        }
    }
}

// MARK: - Features Screen

struct FeaturesScreen: View {
    var body: some View {
        ZStack {
            // Background - use photo if available, otherwise animated background
            if UIImage(named: "feature-photo-morning-wakeup") != nil {
                Image("feature-photo-morning-wakeup")
                    .resizable()
                    .scaledToFill()
                    .ignoresSafeArea()
                    .blur(radius: 8)

                // Gradient overlay
                LinearGradient(
                    colors: [
                        Color.black.opacity(0.8),
                        Color.black.opacity(0.6),
                        Color.black.opacity(0.7)
                    ],
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()
            } else {
                AnimatedBackground(isTracking: false)
            }

            ScrollView {
                VStack(spacing: 30) {
                    VStack(spacing: 12) {
                        Image(systemName: "sparkles")
                            .font(.system(size: 60))
                            .foregroundColor(ColorTheme.accent)
                            .shadow(color: .black.opacity(0.5), radius: 4, x: 0, y: 2)

                        Text("Powerful Features")
                            .font(.system(size: 36, weight: .bold))
                            .foregroundColor(.white)
                            .shadow(color: .black.opacity(0.5), radius: 4, x: 0, y: 2)

                        Text("Everything you need for better sleep")
                            .font(.title3)
                            .foregroundColor(.white.opacity(0.9))
                            .shadow(color: .black.opacity(0.5), radius: 2, x: 0, y: 1)
                    }
                    .padding(.top, 60)

                    VStack(spacing: 20) {
                        FeatureCard(
                            icon: "waveform.badge.mic",
                            title: "Smart Sound Detection",
                            description: "Automatically detect snoring, coughing, and sleep talking with advanced AI",
                            color: ColorTheme.accent
                        )

                        FeatureCard(
                            icon: "speaker.wave.3.fill",
                            title: "Sleep Sounds",
                            description: "8 calming ambient sounds to help you fall asleep faster",
                            color: ColorTheme.secondary
                        )

                        FeatureCard(
                            icon: "heart.text.square.fill",
                            title: "Health Integration",
                            description: "Track 12 comprehensive health metrics from HealthKit",
                            color: Color(hex: "#E91E63")
                        )

                        FeatureCard(
                            icon: "chart.line.uptrend.xyaxis",
                            title: "Detailed Analytics",
                            description: "Visualize trends and patterns in your sleep data",
                            color: Color(hex: "#9C27B0")
                        )
                    }
                    .padding(.horizontal, 30)

                    Spacer(minLength: 40)
                }
            }
        }
    }
}

// MARK: - Intelligence Screen

struct IntelligenceScreen: View {
    var body: some View {
        ZStack {
            // Background - use photo if available, otherwise animated background
            if UIImage(named: "intelligence-photo-insights") != nil {
                Image("intelligence-photo-insights")
                    .resizable()
                    .scaledToFill()
                    .ignoresSafeArea()
                    .blur(radius: 6)

                // Gradient overlay
                LinearGradient(
                    colors: [
                        Color.black.opacity(0.8),
                        Color.black.opacity(0.5),
                        Color.black.opacity(0.7)
                    ],
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()
            } else {
                AnimatedBackground(isTracking: false)
            }

            ScrollView {
                VStack(spacing: 30) {
                    VStack(spacing: 12) {
                        Image(systemName: "brain.head.profile")
                            .font(.system(size: 60))
                            .foregroundColor(ColorTheme.accent)
                            .shadow(color: .black.opacity(0.5), radius: 4, x: 0, y: 2)

                        Text("AI-Powered Intelligence")
                            .font(.system(size: 36, weight: .bold))
                            .foregroundColor(.white)
                            .multilineTextAlignment(.center)
                            .shadow(color: .black.opacity(0.5), radius: 4, x: 0, y: 2)

                        Text("Insights no other app provides")
                            .font(.title3)
                            .foregroundColor(.white.opacity(0.9))
                            .shadow(color: .black.opacity(0.5), radius: 2, x: 0, y: 1)
                    }
                    .padding(.top, 60)
                    .padding(.horizontal, 30)
                    
                    VStack(spacing: 20) {
                        IntelligenceCard(
                            icon: "arrow.triangle.2.circlepath",
                            title: "Correlation Analysis",
                            description: "Discover how exercise, caffeine, and other factors affect your sleep quality",
                            example: "\"Your sleep improves by 25% when you exercise\""
                        )
                        
                        IntelligenceCard(
                            icon: "crystal.ball.fill",
                            title: "Sleep Prediction",
                            description: "Forecast tonight's sleep quality based on your daily activities",
                            example: "\"85% confidence you'll sleep well tonight\""
                        )
                        
                        IntelligenceCard(
                            icon: "exclamationmark.triangle.fill",
                            title: "Anomaly Detection",
                            description: "Automatically flag unusual patterns in your health metrics",
                            example: "\"Heart rate 15% higher than usual\""
                        )
                    }
                    .padding(.horizontal, 30)
                    
                    Spacer(minLength: 40)
                }
            }
        }
    }
}

// MARK: - Permissions Screen

struct PermissionsScreen: View {
    @Environment(\.modelContext) private var modelContext
    @State private var microphoneGranted = false
    @State private var healthKitGranted = false
    @State private var notificationsGranted = false

    var body: some View {
        ZStack {
            // Background - use photo if available, otherwise animated background
            if UIImage(named: "permissions-photo-bedroom") != nil {
                Image("permissions-photo-bedroom")
                    .resizable()
                    .scaledToFill()
                    .ignoresSafeArea()
                    .blur(radius: 10)

                // Gradient overlay
                LinearGradient(
                    colors: [
                        Color.black.opacity(0.85),
                        Color.black.opacity(0.6),
                        Color.black.opacity(0.75)
                    ],
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()
            } else {
                AnimatedBackground(isTracking: false)
            }

            VStack(spacing: 30) {
                VStack(spacing: 12) {
                    Image(systemName: "hand.raised.fill")
                        .font(.system(size: 60))
                        .foregroundColor(ColorTheme.accent)
                        .shadow(color: .black.opacity(0.5), radius: 4, x: 0, y: 2)

                    Text("Permissions")
                        .font(.system(size: 36, weight: .bold))
                        .foregroundColor(.white)
                        .shadow(color: .black.opacity(0.5), radius: 4, x: 0, y: 2)

                    Text("We need a few permissions to work our magic")
                        .font(.title3)
                        .foregroundColor(.white.opacity(0.9))
                        .multilineTextAlignment(.center)
                        .shadow(color: .black.opacity(0.5), radius: 2, x: 0, y: 1)
                        .padding(.horizontal, 40)
                }
                .padding(.top, 60)

                VStack(spacing: 16) {
                    PermissionActionRow(
                        icon: "mic.fill",
                        title: "Microphone",
                        description: "To detect sleep sounds like snoring and sleep talking",
                        isGranted: $microphoneGranted,
                        action: requestMicrophone
                    )

                    PermissionActionRow(
                        icon: "heart.fill",
                        title: "HealthKit",
                        description: "To track heart rate, HRV, and other health metrics",
                        isGranted: $healthKitGranted,
                        action: requestHealthKit
                    )

                    PermissionActionRow(
                        icon: "bell.fill",
                        title: "Notifications",
                        description: "To remind you about bedtime and wake you gently",
                        isGranted: $notificationsGranted,
                        action: requestNotifications
                    )
                }
                .padding(.horizontal, 30)

                Text("You can change these anytime in Settings")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.5))
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 40)

                Spacer()
            }
        }
    }

    private func requestMicrophone() {
        HapticManager.shared.impact(style: .medium)
        Task {
            let service = SoundAnalysisService()
            microphoneGranted = await service.requestMicrophonePermission()
            if microphoneGranted {
                HapticManager.shared.notification(type: .success)
            }
        }
    }

    private func requestHealthKit() {
        HapticManager.shared.impact(style: .medium)
        Task {
            let service = HealthKitService()
            healthKitGranted = await service.requestAuthorization()
            if healthKitGranted {
                HapticManager.shared.notification(type: .success)
            }
        }
    }

    private func requestNotifications() {
        HapticManager.shared.impact(style: .medium)
        Task {
            let service = NotificationService()
            notificationsGranted = await service.requestAuthorization()
            if notificationsGranted {
                HapticManager.shared.notification(type: .success)
            }
        }
    }
}

// MARK: - Get Started Screen

struct GetStartedScreen: View {
    var onGetStarted: () -> Void
    
    var body: some View {
        ZStack {
            AnimatedBackground(isTracking: false)
            
            VStack(spacing: 40) {
                Spacer()
                
                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 100))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [ColorTheme.accent, ColorTheme.secondary],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                
                VStack(spacing: 16) {
                    Text("You're All Set!")
                        .font(.system(size: 42, weight: .bold))
                        .foregroundColor(.white)
                    
                    Text("Let's set up your profile to get personalized insights")
                        .font(.title3)
                        .foregroundColor(.white.opacity(0.7))
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 40)
                }
                
                Spacer()
                
                Button(action: {
                    HapticManager.shared.impact(style: .medium)
                    onGetStarted()
                }) {
                    HStack {
                        Text("Create Your Profile")
                            .font(.title3.bold())
                        
                        Image(systemName: "arrow.right")
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 20)
                    .background(
                        LinearGradient(
                            colors: [ColorTheme.accent, ColorTheme.secondary],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(20)
                    .shadow(color: ColorTheme.accent.opacity(0.5), radius: 20, x: 0, y: 10)
                }
                .padding(.horizontal, 30)
                .padding(.bottom, 60)
            }
        }
    }
}

// MARK: - Helper Components

struct FeatureBadge: View {
    let icon: String
    let text: String

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(ColorTheme.accent)

            Text(text)
                .font(.headline)
                .foregroundColor(.white)
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 12)
        .background(.ultraThinMaterial)
        .cornerRadius(20)
    }
}

struct FeatureCard: View {
    let icon: String
    let title: String
    let description: String
    let color: Color

    var body: some View {
        HStack(alignment: .top, spacing: 16) {
            Image(systemName: icon)
                .font(.system(size: 30))
                .foregroundColor(color)
                .frame(width: 50, height: 50)

            VStack(alignment: .leading, spacing: 6) {
                Text(title)
                    .font(.headline)
                    .foregroundColor(.white)

                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.7))
                    .fixedSize(horizontal: false, vertical: true)
            }
        }
        .padding()
        .background(.ultraThinMaterial)
        .cornerRadius(16)
    }
}

struct IntelligenceCard: View {
    let icon: String
    let title: String
    let description: String
    let example: String

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: icon)
                    .font(.system(size: 24))
                    .foregroundColor(ColorTheme.accent)

                Text(title)
                    .font(.headline)
                    .foregroundColor(.white)
            }

            Text(description)
                .font(.subheadline)
                .foregroundColor(.white.opacity(0.7))

            Text(example)
                .font(.caption)
                .italic()
                .foregroundColor(ColorTheme.accent)
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(ColorTheme.accent.opacity(0.2))
                .cornerRadius(8)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .background(
            LinearGradient(
                colors: [Color.white.opacity(0.1), Color.white.opacity(0.05)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .cornerRadius(16)
    }
}

struct PermissionActionRow: View {
    let icon: String
    let title: String
    let description: String
    @Binding var isGranted: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(alignment: .top, spacing: 16) {
                ZStack {
                    Circle()
                        .fill(isGranted ? Color.green.opacity(0.2) : ColorTheme.accent.opacity(0.2))
                        .frame(width: 50, height: 50)

                    Image(systemName: isGranted ? "checkmark.circle.fill" : icon)
                        .font(.system(size: 24))
                        .foregroundColor(isGranted ? .green : ColorTheme.accent)
                }

                VStack(alignment: .leading, spacing: 6) {
                    Text(title)
                        .font(.headline)
                        .foregroundColor(.white)

                    Text(description)
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.7))
                        .fixedSize(horizontal: false, vertical: true)
                }

                Spacer()

                if !isGranted {
                    Image(systemName: "chevron.right")
                        .foregroundColor(.white.opacity(0.5))
                }
            }
            .padding()
            .background(.ultraThinMaterial)
            .cornerRadius(16)
        }
        .disabled(isGranted)
    }
}

struct PermissionRow: View {
    let icon: String
    let title: String
    let description: String
    let isGranted: Bool

    var body: some View {
        HStack(alignment: .top, spacing: 16) {
            ZStack {
                Circle()
                    .fill(isGranted ? Color.green.opacity(0.2) : ColorTheme.accent.opacity(0.2))
                    .frame(width: 50, height: 50)

                Image(systemName: icon)
                    .foregroundColor(isGranted ? .green : ColorTheme.accent)
            }

            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(title)
                        .font(.headline)
                        .foregroundColor(.white)

                    if isGranted {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.green)
                            .font(.caption)
                    }
                }

                Text(description)
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
            }

            Spacer()
        }
        .padding()
        .background(.ultraThinMaterial)
        .cornerRadius(12)
    }
}

