//
//  ProfileSetupView.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 01/10/2025.
//

import SwiftUI
import SwiftData

struct ProfileSetupView: View {
    @Environment(\.modelContext) private var modelContext
    @Environment(\.dismiss) private var dismiss
    
    @State private var age: Int = 30
    @State private var sex: Sex = .preferNotToSay
    @State private var weightKg: Double = 70
    @State private var heightCm: Double = 170
    @State private var sleepGoalHours: Double = 8.0
    @State private var fitnessLevel: FitnessLevel = .moderate
    @State private var preferredBedtime = Calendar.current.date(from: DateComponents(hour: 22, minute: 0)) ?? Date()
    @State private var preferredWakeTime = Calendar.current.date(from: DateComponents(hour: 6, minute: 0)) ?? Date()
    
    @State private var currentStep = 0
    @State private var showingSkipAlert = false
    
    var onComplete: () -> Void
    
    var body: some View {
        ZStack {
            // Background - use photo if available, otherwise animated background
            if UIImage(named: "profile-photo-diversity") != nil {
                Image("profile-photo-diversity")
                    .resizable()
                    .scaledToFill()
                    .ignoresSafeArea()
                    .blur(radius: 15)

                // Gradient overlay
                LinearGradient(
                    colors: [
                        Color.black.opacity(0.85),
                        Color.black.opacity(0.7),
                        Color.black.opacity(0.85)
                    ],
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()
            } else {
                AnimatedBackground(isTracking: false)
            }

            VStack(spacing: 0) {
                // Progress indicator
                progressIndicator
                
                // Content
                TabView(selection: $currentStep) {
                    welcomeStep
                        .tag(0)
                    
                    demographicsStep
                        .tag(1)
                    
                    physicalStep
                        .tag(2)
                    
                    sleepGoalsStep
                        .tag(3)
                    
                    fitnessStep
                        .tag(4)
                    
                    summaryStep
                        .tag(5)
                }
                .tabViewStyle(.page(indexDisplayMode: .never))
                
                // Navigation buttons
                navigationButtons
            }
        }
        .alert("Skip Profile Setup?", isPresented: $showingSkipAlert) {
            Button("Continue Setup", role: .cancel) { }
            Button("Skip", role: .destructive) {
                createDefaultProfile()
                onComplete()
            }
        } message: {
            Text("Setting up your profile helps us provide personalized sleep insights. Are you sure you want to skip?")
        }
    }
    
    // MARK: - Progress Indicator
    
    private var progressIndicator: some View {
        HStack(spacing: 8) {
            ForEach(0..<6) { index in
                Capsule()
                    .fill(index <= currentStep ? ColorTheme.accent : Color.white.opacity(0.3))
                    .frame(height: 4)
                    .animation(.spring(response: 0.3), value: currentStep)
            }
        }
        .padding(.horizontal, 30)
        .padding(.top, 20)
        .padding(.bottom, 10)
    }
    
    // MARK: - Steps
    
    private var welcomeStep: some View {
        VStack(spacing: 30) {
            Spacer()
            
            Image(systemName: "person.circle.fill")
                .font(.system(size: 80))
                .foregroundStyle(
                    LinearGradient(
                        colors: [ColorTheme.accent, ColorTheme.secondary],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
            
            Text("Let's Personalize Your Experience")
                .font(.system(size: 32, weight: .bold, design: .rounded))
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
            
            Text("We'll ask a few questions to provide you with personalized sleep insights and recommendations")
                .font(.title3)
                .foregroundColor(.white.opacity(0.8))
                .multilineTextAlignment(.center)
                .padding(.horizontal, 30)
            
            Spacer()
        }
        .padding()
    }
    
    private var demographicsStep: some View {
        ScrollView {
            VStack(spacing: 30) {
                VStack(spacing: 12) {
                    Image(systemName: "calendar")
                        .font(.system(size: 50))
                        .foregroundColor(ColorTheme.accent)
                    
                    Text("About You")
                        .font(.title.bold())
                        .foregroundColor(.white)
                    
                    Text("This helps us tailor recommendations to your age group")
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.7))
                        .multilineTextAlignment(.center)
                }
                .padding(.top, 40)
                
                VStack(spacing: 20) {
                    // Age
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Age")
                            .font(.headline)
                            .foregroundColor(.white)
                        
                        HStack {
                            Text("\(age) years")
                                .font(.title2.bold())
                                .foregroundColor(ColorTheme.accent)
                            
                            Spacer()
                            
                            Stepper("", value: $age, in: 13...100)
                                .labelsHidden()
                        }
                        .padding()
                        .background(.ultraThinMaterial)
                        .cornerRadius(12)
                    }
                    
                    // Sex
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Sex")
                            .font(.headline)
                            .foregroundColor(.white)
                        
                        Picker("Sex", selection: $sex) {
                            ForEach(Sex.allCases, id: \.self) { sex in
                                Text(sex.rawValue).tag(sex)
                            }
                        }
                        .pickerStyle(.segmented)
                    }
                }
                .padding(.horizontal, 30)
                
                Spacer()
            }
        }
    }
    
    private var physicalStep: some View {
        ScrollView {
            VStack(spacing: 30) {
                VStack(spacing: 12) {
                    Image(systemName: "figure.stand")
                        .font(.system(size: 50))
                        .foregroundColor(ColorTheme.accent)
                    
                    Text("Physical Stats")
                        .font(.title.bold())
                        .foregroundColor(.white)
                    
                    Text("Helps us calculate BMI and sleep apnea risk")
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.7))
                        .multilineTextAlignment(.center)
                }
                .padding(.top, 40)
                
                VStack(spacing: 20) {
                    // Weight
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Weight")
                            .font(.headline)
                            .foregroundColor(.white)
                        
                        HStack {
                            TextField("Weight", value: $weightKg, format: .number)
                                .keyboardType(.decimalPad)
                                .font(.title2.bold())
                                .foregroundColor(.white)
                            
                            Text("kg")
                                .foregroundColor(.white.opacity(0.7))
                        }
                        .padding()
                        .background(.ultraThinMaterial)
                        .cornerRadius(12)
                    }
                    
                    // Height
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Height")
                            .font(.headline)
                            .foregroundColor(.white)
                        
                        HStack {
                            TextField("Height", value: $heightCm, format: .number)
                                .keyboardType(.decimalPad)
                                .font(.title2.bold())
                                .foregroundColor(.white)
                            
                            Text("cm")
                                .foregroundColor(.white.opacity(0.7))
                        }
                        .padding()
                        .background(.ultraThinMaterial)
                        .cornerRadius(12)
                    }
                    
                    // BMI Preview
                    if weightKg > 0 && heightCm > 0 {
                        let bmi = weightKg / pow(heightCm / 100, 2)
                        HStack {
                            Text("BMI:")
                                .foregroundColor(.white.opacity(0.7))
                            Text(String(format: "%.1f", bmi))
                                .font(.headline)
                                .foregroundColor(ColorTheme.accent)
                        }
                        .padding()
                        .background(.ultraThinMaterial)
                        .cornerRadius(12)
                    }
                }
                .padding(.horizontal, 30)
                
                Spacer()
            }
        }
    }
    
    private var sleepGoalsStep: some View {
        ScrollView {
            VStack(spacing: 30) {
                VStack(spacing: 12) {
                    Image(systemName: "moon.stars.fill")
                        .font(.system(size: 50))
                        .foregroundColor(ColorTheme.accent)
                    
                    Text("Sleep Goals")
                        .font(.title.bold())
                        .foregroundColor(.white)
                    
                    Text("Set your ideal sleep schedule")
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.7))
                }
                .padding(.top, 40)
                
                VStack(spacing: 20) {
                    // Sleep Goal
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Sleep Goal")
                            .font(.headline)
                            .foregroundColor(.white)
                        
                        HStack {
                            Text("\(String(format: "%.1f", sleepGoalHours)) hours")
                                .font(.title2.bold())
                                .foregroundColor(ColorTheme.accent)
                            
                            Spacer()
                        }
                        .padding()
                        .background(.ultraThinMaterial)
                        .cornerRadius(12)
                        
                        Slider(value: $sleepGoalHours, in: 6...10, step: 0.5)
                            .tint(ColorTheme.accent)
                    }
                    
                    // Bedtime
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Preferred Bedtime")
                            .font(.headline)
                            .foregroundColor(.white)
                        
                        DatePicker("Bedtime", selection: $preferredBedtime, displayedComponents: .hourAndMinute)
                            .datePickerStyle(.wheel)
                            .labelsHidden()
                            .colorScheme(.dark)
                    }
                    
                    // Wake Time
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Preferred Wake Time")
                            .font(.headline)
                            .foregroundColor(.white)
                        
                        DatePicker("Wake Time", selection: $preferredWakeTime, displayedComponents: .hourAndMinute)
                            .datePickerStyle(.wheel)
                            .labelsHidden()
                            .colorScheme(.dark)
                    }
                }
                .padding(.horizontal, 30)
                
                Spacer()
            }
        }
    }
    
    private var fitnessStep: some View {
        ScrollView {
            VStack(spacing: 30) {
                VStack(spacing: 12) {
                    Image(systemName: "figure.run")
                        .font(.system(size: 50))
                        .foregroundColor(ColorTheme.accent)

                    Text("Fitness Level")
                        .font(.title.bold())
                        .foregroundColor(.white)

                    Text("Exercise affects sleep quality")
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.7))
                }
                .padding(.top, 40)

                VStack(spacing: 16) {
                    ForEach(FitnessLevel.allCases, id: \.self) { level in
                        Button(action: {
                            withAnimation(.spring(response: 0.3)) {
                                fitnessLevel = level
                            }
                            HapticManager.shared.impact(style: .light)
                        }) {
                            HStack {
                                VStack(alignment: .leading, spacing: 4) {
                                    Text(level.rawValue)
                                        .font(.headline)
                                        .foregroundColor(.white)

                                    Text(level.description)
                                        .font(.caption)
                                        .foregroundColor(.white.opacity(0.7))
                                }

                                Spacer()

                                if fitnessLevel == level {
                                    Image(systemName: "checkmark.circle.fill")
                                        .foregroundColor(ColorTheme.accent)
                                        .font(.title2)
                                }
                            }
                            .padding()
                            .background(
                                fitnessLevel == level
                                    ? LinearGradient(
                                        colors: [ColorTheme.accent.opacity(0.3), ColorTheme.secondary.opacity(0.3)],
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    )
                                    : LinearGradient(
                                        colors: [Color.white.opacity(0.1), Color.white.opacity(0.1)],
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    )
                            )
                            .cornerRadius(12)
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(fitnessLevel == level ? ColorTheme.accent : Color.clear, lineWidth: 2)
                            )
                        }
                    }
                }
                .padding(.horizontal, 30)

                Spacer()
            }
        }
    }

    private var summaryStep: some View {
        ScrollView {
            VStack(spacing: 30) {
                VStack(spacing: 12) {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 60))
                        .foregroundStyle(
                            LinearGradient(
                                colors: [ColorTheme.accent, ColorTheme.secondary],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )

                    Text("Profile Summary")
                        .font(.title.bold())
                        .foregroundColor(.white)

                    Text("Review your information")
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.7))
                }
                .padding(.top, 40)

                VStack(spacing: 16) {
                    SummaryRow(icon: "person.fill", label: "Age", value: "\(age) years")
                    SummaryRow(icon: "figure.stand", label: "Sex", value: sex.rawValue)
                    SummaryRow(icon: "scalemass.fill", label: "Weight", value: "\(String(format: "%.1f", weightKg)) kg")
                    SummaryRow(icon: "ruler.fill", label: "Height", value: "\(String(format: "%.0f", heightCm)) cm")
                    SummaryRow(icon: "heart.fill", label: "BMI", value: String(format: "%.1f", weightKg / pow(heightCm / 100, 2)))
                    SummaryRow(icon: "moon.stars.fill", label: "Sleep Goal", value: "\(String(format: "%.1f", sleepGoalHours)) hours")
                    SummaryRow(icon: "figure.run", label: "Fitness", value: fitnessLevel.rawValue)
                    SummaryRow(icon: "bed.double.fill", label: "Bedtime", value: formatTime(preferredBedtime))
                    SummaryRow(icon: "sunrise.fill", label: "Wake Time", value: formatTime(preferredWakeTime))
                }
                .padding(.horizontal, 30)

                // Personalized insights preview
                VStack(alignment: .leading, spacing: 12) {
                    Text("Your Personalized Insights")
                        .font(.headline)
                        .foregroundColor(.white)

                    let tempProfile = UserProfile(
                        age: age,
                        sex: sex,
                        weightKg: weightKg,
                        heightCm: heightCm,
                        sleepGoalHours: sleepGoalHours,
                        fitnessLevel: fitnessLevel
                    )

                    InsightPreviewCard(
                        icon: "bed.double.fill",
                        title: "Recommended Sleep",
                        value: "\(String(format: "%.0f", tempProfile.recommendedSleepHours.lowerBound))-\(String(format: "%.0f", tempProfile.recommendedSleepHours.upperBound)) hours",
                        description: "Based on your age group"
                    )

                    InsightPreviewCard(
                        icon: "heart.text.square.fill",
                        title: "Sleep Apnea Risk",
                        value: tempProfile.sleepApneaRisk.rawValue,
                        description: tempProfile.sleepApneaRisk.recommendation
                    )
                }
                .padding(.horizontal, 30)
                .padding(.top, 20)

                Spacer()
            }
        }
    }
    
    // MARK: - Navigation Buttons
    
    private var navigationButtons: some View {
        HStack(spacing: 16) {
            if currentStep > 0 {
                Button(action: {
                    withAnimation {
                        currentStep -= 1
                    }
                }) {
                    Text("Back")
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.white.opacity(0.2))
                        .cornerRadius(16)
                }
            }
            
            Button(action: {
                if currentStep < 5 {
                    withAnimation {
                        currentStep += 1
                    }
                } else {
                    saveProfile()
                }
            }) {
                Text(currentStep < 5 ? "Continue" : "Complete")
                    .font(.headline)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        LinearGradient(
                            colors: [ColorTheme.accent, ColorTheme.secondary],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(16)
            }
        }
        .padding(.horizontal, 30)
        .padding(.bottom, 30)
    }
    
    // MARK: - Helper Methods

    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }

    // MARK: - Actions

    private func saveProfile() {
        let profile = UserProfile(
            age: age,
            sex: sex,
            weightKg: weightKg,
            heightCm: heightCm,
            sleepGoalHours: sleepGoalHours,
            preferredBedtime: preferredBedtime,
            preferredWakeTime: preferredWakeTime,
            fitnessLevel: fitnessLevel
        )
        
        modelContext.insert(profile)
        try? modelContext.save()
        
        HapticManager.shared.notification(type: .success)
        onComplete()
    }
    
    private func createDefaultProfile() {
        let profile = UserProfile(
            age: 30,
            sex: .preferNotToSay,
            weightKg: 70,
            heightCm: 170
        )
        
        modelContext.insert(profile)
        try? modelContext.save()
    }
}

// MARK: - Helper Views

struct SummaryRow: View {
    let icon: String
    let label: String
    let value: String

    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(ColorTheme.accent)
                .frame(width: 30)

            Text(label)
                .foregroundColor(.white.opacity(0.7))

            Spacer()

            Text(value)
                .font(.headline)
                .foregroundColor(.white)
        }
        .padding()
        .background(.ultraThinMaterial)
        .cornerRadius(12)
    }
}

struct InsightPreviewCard: View {
    let icon: String
    let title: String
    let value: String
    let description: String

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(ColorTheme.accent)

                Text(title)
                    .font(.headline)
                    .foregroundColor(.white)
            }

            Text(value)
                .font(.title2.bold())
                .foregroundColor(ColorTheme.accent)

            Text(description)
                .font(.caption)
                .foregroundColor(.white.opacity(0.7))
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .background(
            LinearGradient(
                colors: [ColorTheme.accent.opacity(0.2), ColorTheme.secondary.opacity(0.2)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .cornerRadius(12)
    }
}

