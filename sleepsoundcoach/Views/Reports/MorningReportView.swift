//
//  MorningReportView.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 30/09/2025.
//

import SwiftUI
import Charts

struct MorningReportView: View {
    let session: SleepSession
    @Environment(\.dismiss) private var dismiss
    @State private var showConfetti = false
    @State private var audioPlaybackService = AudioPlaybackService()
    @State private var showingAudioPlayer = false
    @State private var selectedEvent: SoundEvent?
    @State private var predictiveService = PredictiveAnalyticsService()

    // Historical sessions for anomaly detection
    var historicalSessions: [SleepSession] = []
    
    var body: some View {
        NavigationStack {
            ZStack {
                // Animated background
                AnimatedBackground(isTracking: false)

                ScrollView {
                    VStack(spacing: 25) {
                        // Header
                        headerSection
                        
                        // Quiet Score Card
                        quietScoreCard
                        
                        // Summary Stats
                        summaryStats
                        
                        // Timeline Chart
                        if let events = session.soundEvents, !events.isEmpty {
                            timelineChart(events: events)
                        }
                        
                        // Event Breakdown
                        eventBreakdown
                        
                        // HealthKit Correlation
                        if session.averageHeartRate != nil || session.deepSleepMinutes != nil {
                            healthKitSection
                        }

                        // Anomaly Detection (NEW)
                        if !historicalSessions.isEmpty {
                            anomalySection
                        }

                        // Coaching Tips
                        coachingSection
                    }
                    .padding()
                }
                
                if showConfetti {
                    ConfettiView()
                }
            }
            .navigationTitle("Sleep Report")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                    .foregroundColor(.white)
                }
            }
            .sheet(isPresented: $showingAudioPlayer) {
                if let event = selectedEvent {
                    AudioPlayerSheet(event: event, audioService: audioPlaybackService)
                }
            }
        }
        .onAppear {
            if session.quietScore >= 80 {
                showConfetti = true
                DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                    showConfetti = false
                }
            }
        }
    }
    
    private var headerSection: some View {
        ZStack {
            // Background photo based on sleep quality (with fallback)
            let photoName = session.quietScore >= 70 ? "morning-report-photo-excellent" : "morning-report-photo-poor"

            if UIImage(named: photoName) != nil {
                Image(photoName)
                    .resizable()
                    .scaledToFill()
                    .frame(height: 200)
                    .clipped()
                    .cornerRadius(20)

                // Gradient overlay
                LinearGradient(
                    colors: [
                        Color.black.opacity(0.6),
                        Color.black.opacity(0.3),
                        Color.black.opacity(0.6)
                    ],
                    startPoint: .top,
                    endPoint: .bottom
                )
                .cornerRadius(20)
            } else {
                // Fallback gradient background
                LinearGradient(
                    colors: session.quietScore >= 70 ?
                        [ColorTheme.accent.opacity(0.3), ColorTheme.secondary.opacity(0.3)] :
                        [Color.orange.opacity(0.3), Color.red.opacity(0.3)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .frame(height: 200)
                .cornerRadius(20)
            }

            VStack(spacing: 10) {
                Text("Good Morning! ☀️")
                    .font(.system(size: 32, weight: .bold))
                    .foregroundColor(.white)
                    .shadow(color: .black.opacity(0.5), radius: 4, x: 0, y: 2)

                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.9))
                    .shadow(color: .black.opacity(0.5), radius: 2, x: 0, y: 1)

                Text("You slept for \(session.formattedDuration)")
                    .font(.title3)
                    .foregroundColor(.white)
                    .shadow(color: .black.opacity(0.5), radius: 2, x: 0, y: 1)
            }
            .padding()
        }
        .frame(height: 200)
    }
    
    private var quietScoreCard: some View {
        VStack(spacing: 15) {
            Text("Quiet Score")
                .font(.headline)
                .foregroundColor(.white.opacity(0.8))
                .shimmer()

            ZStack {
                // Background circle
                Circle()
                    .stroke(lineWidth: 15)
                    .foregroundColor(.white.opacity(0.2))
                    .frame(width: 150, height: 150)

                // Animated progress circle
                Circle()
                    .trim(from: 0, to: CGFloat(session.quietScore) / 100)
                    .stroke(
                        AngularGradient(
                            colors: scoreColors + [scoreColors.first!],
                            center: .center
                        ),
                        style: StrokeStyle(lineWidth: 15, lineCap: .round)
                    )
                    .frame(width: 150, height: 150)
                    .rotationEffect(.degrees(-90))
                    .glow(color: scoreColors.first ?? .blue, radius: 10)
                    .animation(.spring(response: 1.5, dampingFraction: 0.6).delay(0.3), value: session.quietScore)

                // Pulsing glow
                Circle()
                    .fill(
                        RadialGradient(
                            colors: [scoreColors.first?.opacity(0.3) ?? .blue.opacity(0.3), .clear],
                            center: .center,
                            startRadius: 50,
                            endRadius: 80
                        )
                    )
                    .frame(width: 150, height: 150)
                    .blur(radius: 10)

                VStack(spacing: 5) {
                    Text("\(session.quietScore)")
                        .font(.system(size: 50, weight: .bold))
                        .foregroundColor(.white)
                        .contentTransition(.numericText())

                    Text(session.sleepQuality)
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.7))
                }
                .floating(duration: 2.5, distance: 5)
            }

            Text(scoreMessage)
                .font(.body)
                .foregroundColor(.white.opacity(0.8))
                .multilineTextAlignment(.center)
                .padding(.horizontal)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .glow(color: scoreColors.first ?? .blue, radius: 5)
        )
    }
    
    private var scoreColors: [Color] {
        if session.quietScore >= 80 {
            return [.green, .blue]
        } else if session.quietScore >= 60 {
            return [.blue, .cyan]
        } else if session.quietScore >= 40 {
            return [.orange, .yellow]
        } else {
            return [.red, .orange]
        }
    }
    
    private var scoreMessage: String {
        if session.quietScore >= 80 {
            return "Excellent! You had a very peaceful night."
        } else if session.quietScore >= 60 {
            return "Good night with minimal disturbances."
        } else if session.quietScore >= 40 {
            return "Fair night. Some improvements possible."
        } else {
            return "Challenging night. Let's work on improvements."
        }
    }
    
    private var summaryStats: some View {
        HStack(spacing: 15) {
            StatCard(
                icon: "zzz",
                value: "\(session.totalSnoreEvents)",
                label: "Snores",
                color: .blue
            )
            
            StatCard(
                icon: "lungs.fill",
                value: "\(session.totalCoughEvents)",
                label: "Coughs",
                color: .orange
            )
            
            StatCard(
                icon: "bubble.left.fill",
                value: "\(session.totalSleepTalkEvents)",
                label: "Talks",
                color: .purple
            )
        }
    }
    
    private func timelineChart(events: [SoundEvent]) -> some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("Sleep Timeline")
                .font(.headline)
                .foregroundColor(.white)
                .padding(.horizontal)
            
            Chart(events) { event in
                PointMark(
                    x: .value("Time", event.timestamp),
                    y: .value("Intensity", event.intensity)
                )
                .foregroundStyle(by: .value("Type", event.eventType.displayName))
                .symbol(by: .value("Type", event.eventType.displayName))
            }
            .frame(height: 200)
            .chartXAxis {
                AxisMarks(values: .automatic) { _ in
                    AxisValueLabel(format: .dateTime.hour().minute())
                        .foregroundStyle(.white)
                }
            }
            .chartYAxis {
                AxisMarks { _ in
                    AxisValueLabel()
                        .foregroundStyle(.white)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(.ultraThinMaterial)
            )
        }
    }
    
    private var eventBreakdown: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Event Details")
                .font(.headline)
                .foregroundColor(.white)

            if let events = session.soundEvents, !events.isEmpty {
                ForEach(events.prefix(10)) { event in
                    EventPlaybackRow(
                        event: event,
                        onPlay: {
                            selectedEvent = event
                            showingAudioPlayer = true
                        }
                    )
                }

                if events.count > 10 {
                    Text("+ \(events.count - 10) more events")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.5))
                        .padding(.top, 5)
                }
            }

            Divider()
                .background(Color.white.opacity(0.2))

            if session.totalSnoreEvents > 0 {
                EventDetailRow(
                    icon: "zzz",
                    title: "Snoring",
                    count: session.totalSnoreEvents,
                    detail: "Avg: \(String(format: "%.0fs", session.averageSnoreDuration))",
                    color: .blue
                )
            }

            if session.totalCoughEvents > 0 {
                EventDetailRow(
                    icon: "lungs.fill",
                    title: "Coughing",
                    count: session.totalCoughEvents,
                    detail: "Throughout night",
                    color: .orange
                )
            }

            if session.totalSleepTalkEvents > 0 {
                EventDetailRow(
                    icon: "bubble.left.fill",
                    title: "Sleep Talking",
                    count: session.totalSleepTalkEvents,
                    detail: "Transcription available",
                    color: .purple
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
        )
    }
    
    private var healthKitSection: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("HealthKit Data")
                .font(.headline)
                .foregroundColor(.white)
            
            if let heartRate = session.averageHeartRate {
                HStack {
                    Image(systemName: "heart.fill")
                        .foregroundStyle(.red)
                    Text("Avg Heart Rate:")
                        .foregroundColor(.white.opacity(0.8))
                    Spacer()
                    Text("\(Int(heartRate)) bpm")
                        .foregroundColor(.white)
                        .bold()
                }
            }
            
            if let deepSleep = session.deepSleepMinutes {
                HStack {
                    Image(systemName: "bed.double.fill")
                        .foregroundStyle(.blue)
                    Text("Deep Sleep:")
                        .foregroundColor(.white.opacity(0.8))
                    Spacer()
                    Text("\(Int(deepSleep)) min")
                        .foregroundColor(.white)
                        .bold()
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
        )
    }
    
    // NEW: Anomaly Detection Section
    private var anomalySection: some View {
        VStack(alignment: .leading, spacing: 15) {
            HStack {
                Image(systemName: "exclamationmark.triangle.fill")
                    .foregroundStyle(ColorTheme.warningColor)
                Text("Health Alerts")
                    .font(.headline)
                    .foregroundColor(.white)
            }

            let anomalies = predictiveService.detectAnomalies(
                session: session,
                historicalSessions: historicalSessions
            )

            if !anomalies.isEmpty {
                ForEach(anomalies) { anomaly in
                    AnomalyAlertCard(anomaly: anomaly)
                }
            } else {
                HStack(spacing: 12) {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.title2)
                        .foregroundColor(ColorTheme.successColor)

                    Text("No unusual patterns detected. All metrics look normal!")
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.8))
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(ColorTheme.successColor.opacity(0.1))
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(ColorTheme.successColor.opacity(0.3), lineWidth: 1)
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
        )
    }

    private var coachingSection: some View {
        VStack(alignment: .leading, spacing: 15) {
            HStack {
                Image(systemName: "sparkles")
                    .foregroundStyle(.yellow)
                Text("Coaching Tips")
                    .font(.headline)
                    .foregroundColor(.white)
            }
            
            let tips = InsightsService().generateCoachingTips(session: session)
            
            ForEach(tips.prefix(3), id: \.self) { tip in
                HStack(alignment: .top, spacing: 10) {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundStyle(.green)
                        .font(.caption)
                    
                    Text(tip)
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.9))
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
        )
    }
}

struct StatCard: View {
    let icon: String
    let value: String
    let label: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 10) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundStyle(color)
            
            Text(value)
                .font(.title.bold())
                .foregroundColor(.white)
            
            Text(label)
                .font(.caption)
                .foregroundColor(.white.opacity(0.7))
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 15)
                .fill(.ultraThinMaterial)
        )
    }
}

struct EventDetailRow: View {
    let icon: String
    let title: String
    let count: Int
    let detail: String
    let color: Color
    
    var body: some View {
        HStack(spacing: 15) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundStyle(color)
                .frame(width: 30)
            
            VStack(alignment: .leading, spacing: 3) {
                Text(title)
                    .font(.subheadline.bold())
                    .foregroundColor(.white)
                
                Text(detail)
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
            }
            
            Spacer()
            
            Text("\(count)")
                .font(.title3.bold())
                .foregroundColor(.white)
        }
    }
}

struct ConfettiView: View {
    @State private var animate = false

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                ForEach(0..<50, id: \.self) { index in
                    ConfettiPiece(screenWidth: geometry.size.width, screenHeight: geometry.size.height)
                        .offset(y: animate ? geometry.size.height : -50)
                        .animation(
                            .linear(duration: Double.random(in: 2...4))
                            .delay(Double.random(in: 0...0.5)),
                            value: animate
                        )
                }
            }
            .allowsHitTesting(false)
            .onAppear {
                animate = true
            }
        }
    }
}

struct ConfettiPiece: View {
    let colors: [Color] = [.red, .blue, .green, .yellow, .purple, .orange, .pink]
    let color: Color
    let x: CGFloat
    let rotation: Double

    init(screenWidth: CGFloat, screenHeight: CGFloat) {
        self.color = Color.random(from: [.red, .blue, .green, .yellow, .purple, .orange, .pink])
        self.x = CGFloat.random(in: 0...screenWidth)
        self.rotation = Double.random(in: 0...360)
    }
    
    var body: some View {
        Rectangle()
            .fill(color)
            .frame(width: 10, height: 10)
            .rotationEffect(.degrees(rotation))
            .position(x: x, y: 0)
    }
}

extension Color {
    static func random(from colors: [Color]) -> Color {
        colors.randomElement() ?? .blue
    }
}

struct EventPlaybackRow: View {
    let event: SoundEvent
    let onPlay: () -> Void

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: event.eventType.icon)
                .foregroundColor(ColorTheme.eventColor(for: event.eventType))
                .font(.title3)

            VStack(alignment: .leading, spacing: 4) {
                Text(event.eventType.displayName)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white)

                HStack(spacing: 8) {
                    Text(event.timestamp, style: .time)
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.6))

                    Text("•")
                        .foregroundColor(.white.opacity(0.4))

                    Text(String(format: "%.1fs", event.duration))
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.6))

                    if let transcription = event.transcription, !transcription.isEmpty {
                        Text("•")
                            .foregroundColor(.white.opacity(0.4))

                        Text(transcription)
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.6))
                            .lineLimit(1)
                    }
                }
            }

            Spacer()

            Button(action: onPlay) {
                Image(systemName: "play.circle.fill")
                    .font(.title2)
                    .foregroundColor(ColorTheme.accent)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.05))
        )
    }
}

struct AudioPlayerSheet: View {
    let event: SoundEvent
    @ObservedObject var audioService: AudioPlaybackService
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationStack {
            ZStack {
                AnimatedBackground(isTracking: false)

                VStack(spacing: 30) {
                    Spacer()

                    // Event icon
                    ZStack {
                        Circle()
                            .fill(
                                LinearGradient(
                                    colors: [ColorTheme.eventColor(for: event.eventType).opacity(0.3), ColorTheme.eventColor(for: event.eventType).opacity(0.1)],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .frame(width: 150, height: 150)

                        Image(systemName: event.eventType.icon)
                            .font(.system(size: 60))
                            .foregroundColor(ColorTheme.eventColor(for: event.eventType))
                    }

                    // Event details
                    VStack(spacing: 10) {
                        Text(event.eventType.displayName)
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.white)

                        Text(event.timestamp, style: .time)
                            .font(.subheadline)
                            .foregroundColor(.white.opacity(0.7))

                        if let transcription = event.transcription, !transcription.isEmpty {
                            Text("\"\(transcription)\"")
                                .font(.body)
                                .foregroundColor(.white.opacity(0.8))
                                .italic()
                                .multilineTextAlignment(.center)
                                .padding(.horizontal)
                        }
                    }

                    // Progress bar
                    VStack(spacing: 10) {
                        ProgressView(value: audioService.playbackProgress)
                            .tint(ColorTheme.accent)

                        HStack {
                            Text(formatTime(audioService.playbackProgress * audioService.duration))
                                .font(.caption)
                                .foregroundColor(.white.opacity(0.6))

                            Spacer()

                            Text(formatTime(audioService.duration))
                                .font(.caption)
                                .foregroundColor(.white.opacity(0.6))
                        }
                    }
                    .padding(.horizontal, 40)

                    // Playback controls
                    HStack(spacing: 40) {
                        Button(action: {
                            audioService.seek(to: max(0, audioService.playbackProgress - 0.1))
                        }) {
                            Image(systemName: "gobackward.10")
                                .font(.title)
                                .foregroundColor(.white)
                        }

                        Button(action: {
                            if audioService.isPlaying {
                                audioService.togglePlayPause()
                            } else {
                                audioService.playEvent(event)
                            }
                        }) {
                            Image(systemName: audioService.isPlaying ? "pause.circle.fill" : "play.circle.fill")
                                .font(.system(size: 70))
                                .foregroundColor(.white)
                        }

                        Button(action: {
                            audioService.seek(to: min(1, audioService.playbackProgress + 0.1))
                        }) {
                            Image(systemName: "goforward.10")
                                .font(.title)
                                .foregroundColor(.white)
                        }
                    }

                    Spacer()
                }
                .padding()
            }
            .navigationTitle("Playback")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        audioService.stopPlayback()
                        dismiss()
                    }
                    .foregroundColor(.white)
                }
            }
        }
        .onDisappear {
            audioService.stopPlayback()
        }
    }

    private func formatTime(_ seconds: TimeInterval) -> String {
        let mins = Int(seconds) / 60
        let secs = Int(seconds) % 60
        return String(format: "%d:%02d", mins, secs)
    }
}

