//
//  TrendsView.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 01/10/2025.
//

import SwiftUI
import SwiftData
import Charts

struct TrendsView: View {
    @Environment(\.modelContext) private var modelContext
    @Query(sort: \SleepSession.startTime, order: .reverse) private var sessions: [SleepSession]
    
    @State private var selectedPeriod: TrendPeriod = .week
    @State private var selectedMetric: TrendMetric = .quietScore
    @State private var predictiveService = PredictiveAnalyticsService()
    
    enum TrendPeriod: String, CaseIterable {
        case week = "7 Days"
        case month = "30 Days"
        case quarter = "90 Days"
    }
    
    enum TrendMetric: String, CaseIterable {
        case quietScore = "Quiet Score"
        case duration = "Sleep Duration"
        case snoring = "Snoring Events"
        case heartRate = "Heart Rate"
    }
    
    var body: some View {
        NavigationStack {
            ZStack {
                AnimatedBackground(isTracking: false)
                
                ScrollView {
                    VStack(spacing: 25) {
                        // Period selector
                        periodSelector
                        
                        // Main trend chart
                        mainTrendChart
                        
                        // Metric selector
                        metricSelector
                        
                        // Statistics cards
                        statisticsCards

                        // Sleep Debt Widget (NEW)
                        sleepDebtSection

                        // Weekly comparison
                        weeklyComparison

                        // Best/Worst nights
                        bestWorstNights
                    }
                    .padding()
                }
            }
            .navigationTitle("Sleep Trends")
            .navigationBarTitleDisplayMode(.large)
        }
    }
    
    private var periodSelector: some View {
        Picker("Period", selection: $selectedPeriod) {
            ForEach(TrendPeriod.allCases, id: \.self) { period in
                Text(period.rawValue).tag(period)
            }
        }
        .pickerStyle(.segmented)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 15)
                .fill(.ultraThinMaterial)
        )
    }
    
    private var filteredSessions: [SleepSession] {
        let calendar = Calendar.current
        let now = Date()
        
        let daysAgo: Int
        switch selectedPeriod {
        case .week: daysAgo = 7
        case .month: daysAgo = 30
        case .quarter: daysAgo = 90
        }
        
        guard let cutoffDate = calendar.date(byAdding: .day, value: -daysAgo, to: now) else {
            return []
        }
        
        return sessions.filter { $0.startTime >= cutoffDate }
    }
    
    private var mainTrendChart: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Trend Analysis")
                .font(.headline)
                .foregroundColor(.white)
            
            if !filteredSessions.isEmpty {
                Chart(filteredSessions.reversed()) { session in
                    LineMark(
                        x: .value("Date", session.startTime),
                        y: .value("Value", metricValue(for: session))
                    )
                    .foregroundStyle(ColorTheme.chartLineColor)
                    .interpolationMethod(.catmullRom)
                    
                    AreaMark(
                        x: .value("Date", session.startTime),
                        y: .value("Value", metricValue(for: session))
                    )
                    .foregroundStyle(
                        LinearGradient(
                            colors: [ColorTheme.chartAreaColor, Color.clear],
                            startPoint: .top,
                            endPoint: .bottom
                        )
                    )
                    .interpolationMethod(.catmullRom)
                }
                .frame(height: 250)
                .chartXAxis {
                    AxisMarks(values: .automatic) { _ in
                        AxisValueLabel(format: .dateTime.month().day())
                            .foregroundStyle(.white)
                    }
                }
                .chartYAxis {
                    AxisMarks { _ in
                        AxisValueLabel()
                            .foregroundStyle(.white)
                    }
                }
            } else {
                emptyChartState
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
        )
    }
    
    private var metricSelector: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 10) {
                ForEach(TrendMetric.allCases, id: \.self) { metric in
                    Button(action: {
                        selectedMetric = metric
                        HapticManager.shared.impact(style: .light)
                    }) {
                        Text(metric.rawValue)
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(selectedMetric == metric ? .white : .white.opacity(0.6))
                            .padding(.horizontal, 16)
                            .padding(.vertical, 10)
                            .background(
                                Capsule()
                                    .fill(selectedMetric == metric ? ColorTheme.accent : Color.white.opacity(0.1))
                            )
                    }
                }
            }
            .padding(.horizontal)
        }
    }
    
    private var statisticsCards: some View {
        LazyVGrid(columns: [GridItem(.flexible()), GridItem(.flexible())], spacing: 15) {
            TrendStatCard(
                title: "Average",
                value: String(format: "%.1f", averageValue),
                icon: "chart.line.uptrend.xyaxis",
                color: ColorTheme.infoColor
            )

            TrendStatCard(
                title: "Best",
                value: String(format: "%.1f", bestValue),
                icon: "star.fill",
                color: ColorTheme.successColor
            )

            TrendStatCard(
                title: "Worst",
                value: String(format: "%.1f", worstValue),
                icon: "exclamationmark.triangle.fill",
                color: ColorTheme.warningColor
            )

            TrendStatCard(
                title: "Trend",
                value: trendDirection,
                icon: trendIcon,
                color: trendColor
            )
        }
    }
    
    private var weeklyComparison: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Weekly Comparison")
                .font(.headline)
                .foregroundColor(.white)
            
            if filteredSessions.count >= 7 {
                Chart {
                    ForEach(0..<7, id: \.self) { dayIndex in
                        let dayName = Calendar.current.shortWeekdaySymbols[dayIndex]
                        let daySessions = sessionsForWeekday(dayIndex)
                        let avgValue = daySessions.isEmpty ? 0 : daySessions.map { metricValue(for: $0) }.reduce(0, +) / Double(daySessions.count)
                        
                        BarMark(
                            x: .value("Day", dayName),
                            y: .value("Average", avgValue)
                        )
                        .foregroundStyle(ColorTheme.chartLineColor)
                    }
                }
                .frame(height: 200)
                .chartXAxis {
                    AxisMarks { _ in
                        AxisValueLabel()
                            .foregroundStyle(.white)
                    }
                }
                .chartYAxis {
                    AxisMarks { _ in
                        AxisValueLabel()
                            .foregroundStyle(.white)
                    }
                }
            } else {
                Text("Need at least 7 days of data")
                    .foregroundColor(.white.opacity(0.6))
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
        )
    }
    
    // NEW: Sleep Debt Section
    private var sleepDebtSection: some View {
        let sleepDebt = predictiveService.calculateSleepDebt(sessions: Array(filteredSessions))
        return SleepDebtWidget(sleepDebt: sleepDebt)
    }

    private var bestWorstNights: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Best & Worst Nights")
                .font(.headline)
                .foregroundColor(.white)
            
            if let bestSession = filteredSessions.max(by: { metricValue(for: $0) < metricValue(for: $1) }) {
                NightCard(session: bestSession, isBest: true, metric: selectedMetric)
            }
            
            if let worstSession = filteredSessions.min(by: { metricValue(for: $0) < metricValue(for: $1) }) {
                NightCard(session: worstSession, isBest: false, metric: selectedMetric)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
        )
    }
    
    private var emptyChartState: some View {
        VStack(spacing: 15) {
            Image(systemName: "chart.xyaxis.line")
                .font(.system(size: 50))
                .foregroundColor(.white.opacity(0.3))
            
            Text("No data available")
                .font(.headline)
                .foregroundColor(.white.opacity(0.6))
            
            Text("Start tracking your sleep to see trends")
                .font(.subheadline)
                .foregroundColor(.white.opacity(0.4))
        }
        .frame(height: 250)
        .frame(maxWidth: .infinity)
    }
    
    // MARK: - Helper Functions
    
    private func metricValue(for session: SleepSession) -> Double {
        switch selectedMetric {
        case .quietScore:
            return Double(session.quietScore)
        case .duration:
            return session.duration / 3600.0
        case .snoring:
            return Double(session.totalSnoreEvents)
        case .heartRate:
            return session.averageHeartRate ?? 0
        }
    }
    
    private var averageValue: Double {
        guard !filteredSessions.isEmpty else { return 0 }
        return filteredSessions.map { metricValue(for: $0) }.reduce(0, +) / Double(filteredSessions.count)
    }
    
    private var bestValue: Double {
        filteredSessions.map { metricValue(for: $0) }.max() ?? 0
    }
    
    private var worstValue: Double {
        filteredSessions.map { metricValue(for: $0) }.min() ?? 0
    }
    
    private var trendDirection: String {
        guard filteredSessions.count >= 2 else { return "N/A" }
        
        let recent = filteredSessions.prefix(filteredSessions.count / 2).map { metricValue(for: $0) }.reduce(0, +) / Double(filteredSessions.count / 2)
        let older = filteredSessions.suffix(filteredSessions.count / 2).map { metricValue(for: $0) }.reduce(0, +) / Double(filteredSessions.count / 2)
        
        let change = ((recent - older) / older) * 100
        return String(format: "%.1f%%", abs(change))
    }
    
    private var trendIcon: String {
        guard filteredSessions.count >= 2 else { return "minus" }
        
        let recent = filteredSessions.prefix(filteredSessions.count / 2).map { metricValue(for: $0) }.reduce(0, +) / Double(filteredSessions.count / 2)
        let older = filteredSessions.suffix(filteredSessions.count / 2).map { metricValue(for: $0) }.reduce(0, +) / Double(filteredSessions.count / 2)
        
        return recent > older ? "arrow.up.right" : "arrow.down.right"
    }
    
    private var trendColor: Color {
        guard filteredSessions.count >= 2 else { return .gray }
        
        let recent = filteredSessions.prefix(filteredSessions.count / 2).map { metricValue(for: $0) }.reduce(0, +) / Double(filteredSessions.count / 2)
        let older = filteredSessions.suffix(filteredSessions.count / 2).map { metricValue(for: $0) }.reduce(0, +) / Double(filteredSessions.count / 2)
        
        return recent > older ? ColorTheme.successColor : ColorTheme.errorColor
    }
    
    private func sessionsForWeekday(_ weekday: Int) -> [SleepSession] {
        filteredSessions.filter { session in
            let calendar = Calendar.current
            let sessionWeekday = calendar.component(.weekday, from: session.startTime) - 1
            return sessionWeekday == weekday
        }
    }
}

struct TrendStatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color

    var body: some View {
        VStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)

            Text(value)
                .font(.title3)
                .fontWeight(.bold)
                .foregroundColor(.white)

            Text(title)
                .font(.caption)
                .foregroundColor(.white.opacity(0.6))
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 15)
                .fill(.ultraThinMaterial)
        )
    }
}

struct NightCard: View {
    let session: SleepSession
    let isBest: Bool
    let metric: TrendsView.TrendMetric

    var body: some View {
        HStack {
            Image(systemName: isBest ? "star.fill" : "exclamationmark.triangle.fill")
                .foregroundColor(isBest ? ColorTheme.successColor : ColorTheme.warningColor)

            VStack(alignment: .leading, spacing: 4) {
                Text(isBest ? "Best Night" : "Worst Night")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.6))

                Text(session.startTime, style: .date)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
            }

            Spacer()

            Text(metricValueString)
                .font(.headline)
                .foregroundColor(.white)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 15)
                .fill(Color.white.opacity(0.05))
        )
    }

    private var metricValueString: String {
        switch metric {
        case .quietScore:
            return "\(session.quietScore)"
        case .duration:
            return String(format: "%.1fh", session.duration / 3600)
        case .snoring:
            return "\(session.totalSnoreEvents)"
        case .heartRate:
            return session.averageHeartRate != nil ? String(format: "%.0f bpm", session.averageHeartRate!) : "N/A"
        }
    }
}

