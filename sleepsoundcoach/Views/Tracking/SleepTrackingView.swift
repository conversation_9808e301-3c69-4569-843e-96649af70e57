//
//  SleepTrackingView.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 30/09/2025.
//

import SwiftUI
import SwiftData

struct SleepTrackingView: View {
    @Environment(\.modelContext) private var modelContext
    @State private var soundService = SoundAnalysisService()
    @State private var healthService = HealthKitService()
    
    @State private var currentSession: SleepSession?
    @State private var showingReport = false
    @State private var sessionToShow: SleepSession?
    @State private var showingSleepSounds = false

    var body: some View {
        ZStack {
            // Animated Background
            AnimatedBackground(isTracking: soundService.isRecording)

            // Floating particles
            FloatingParticles()
                .opacity(soundService.isRecording ? 0.6 : 0.3)

            VStack(spacing: 40) {
                // Sleep sounds button (top right)
                HStack {
                    Spacer()
                    Button(action: {
                        showingSleepSounds = true
                        HapticManager.shared.impact(style: .light)
                    }) {
                        HStack(spacing: 6) {
                            Image(systemName: "speaker.wave.2.fill")
                            Text("Sleep Sounds")
                                .font(.caption)
                                .fontWeight(.medium)
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(
                            Capsule()
                                .fill(.ultraThinMaterial)
                        )
                    }
                }
                .padding(.horizontal)

                Spacer()

                // Moon icon with glow
                moonView
                
                // Status text
                statusText
                
                // Waveform visualization
                if soundService.isRecording {
                    WaveformView(level: soundService.currentWaveformLevel)
                        .frame(height: 100)
                        .padding(.horizontal, 40)
                }
                
                // Event counter
                if soundService.isRecording {
                    eventCounter
                }
                
                Spacer()
                
                // Main action button
                actionButton

                // Debug test buttons (only show when recording)
                #if DEBUG
                if soundService.isRecording {
                    testButtonsView
                }
                #endif

                Spacer()
            }
            .padding()
        }
        .sheet(isPresented: $showingReport) {
            if let session = sessionToShow {
                MorningReportView(session: session)
            }
        }
        .sheet(isPresented: $showingSleepSounds) {
            SleepSoundsView()
        }
    }
    

    
    private var moonView: some View {
        ZStack {
            // Pulsing circles
            if soundService.isRecording {
                PulsingCircle(color: .blue, duration: 2.0)
                    .frame(width: 150, height: 150)

                PulsingCircle(color: .purple, duration: 2.5)
                    .frame(width: 150, height: 150)
            }

            // Glow effect
            Circle()
                .fill(
                    RadialGradient(
                        colors: [.blue.opacity(0.4), .purple.opacity(0.3), .clear],
                        center: .center,
                        startRadius: 30,
                        endRadius: 120
                    )
                )
                .frame(width: 200, height: 200)
                .blur(radius: 20)
                .opacity(soundService.isRecording ? 1 : 0.5)
                .animation(.easeInOut(duration: 2).repeatForever(autoreverses: true), value: soundService.isRecording)

            // Moon with floating animation
            Image(systemName: soundService.isRecording ? "moon.fill" : "moon.stars.fill")
                .font(.system(size: 80))
                .foregroundStyle(
                    LinearGradient(
                        colors: [.white, .blue.opacity(0.8)],
                        startPoint: .top,
                        endPoint: .bottom
                    )
                )
                .glow(color: .blue, radius: 20)
                .floating(duration: 3.0, distance: 8)
        }
    }
    
    private var statusText: some View {
        VStack(spacing: 10) {
            Text(soundService.isRecording ? "Tracking Your Sleep" : "Ready to Sleep")
                .font(.system(size: 32, weight: .bold, design: .rounded))
                .foregroundColor(.white)
            
            if soundService.isRecording, let session = currentSession {
                Text(formatDuration(Date().timeIntervalSince(session.startTime)))
                    .font(.title2)
                    .foregroundColor(.white.opacity(0.7))
                    .monospacedDigit()
            } else {
                Text("Tap to start tracking")
                    .font(.title3)
                    .foregroundColor(.white.opacity(0.6))
            }
        }
    }
    
    private var eventCounter: some View {
        HStack(spacing: 30) {
            EventCountBadge(
                icon: "zzz",
                count: soundService.detectedEvents.filter { $0.eventType == .snoring }.count,
                color: .blue
            )
            
            EventCountBadge(
                icon: "lungs.fill",
                count: soundService.detectedEvents.filter { $0.eventType == .coughing }.count,
                color: .orange
            )
            
            EventCountBadge(
                icon: "bubble.left.fill",
                count: soundService.detectedEvents.filter { $0.eventType == .sleepTalking }.count,
                color: .purple
            )
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
        )
    }
    
    private var actionButton: some View {
        Button(action: toggleTracking) {
            HStack(spacing: 15) {
                Image(systemName: soundService.isRecording ? "stop.fill" : "play.fill")
                    .font(.title2)

                Text(soundService.isRecording ? "Stop Tracking" : "Start Sleep Tracking")
                    .font(.title3.bold())
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding(.vertical, 20)
            .background(
                ZStack {
                    RoundedRectangle(cornerRadius: 20)
                        .fill(
                            LinearGradient(
                                colors: soundService.isRecording ? [.red, .orange] : [.blue, .purple],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )

                    if !soundService.isRecording {
                        RoundedRectangle(cornerRadius: 20)
                            .fill(
                                LinearGradient(
                                    colors: [.white.opacity(0.3), .clear],
                                    startPoint: .top,
                                    endPoint: .bottom
                                )
                            )
                    }
                }
                .glow(
                    color: soundService.isRecording ? .red : .blue,
                    radius: 15
                )
            )
        }
        .buttonStyle(BounceButtonStyle())
        .padding(.horizontal, 30)
    }

    private var testButtonsView: some View {
        VStack(spacing: 10) {
            Text("Test Sound Detection")
                .font(.caption)
                .foregroundColor(.white.opacity(0.6))

            HStack(spacing: 10) {
                Button(action: {
                    soundService.simulateSnoring()
                    HapticManager.shared.impact(style: .light)
                }) {
                    Text("🌙 Snore")
                        .font(.caption)
                        .foregroundColor(.white)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(
                            Capsule()
                                .fill(.blue.opacity(0.3))
                        )
                }

                Button(action: {
                    soundService.simulateCoughing()
                    HapticManager.shared.impact(style: .light)
                }) {
                    Text("😷 Cough")
                        .font(.caption)
                        .foregroundColor(.white)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(
                            Capsule()
                                .fill(.orange.opacity(0.3))
                        )
                }

                Button(action: {
                    soundService.simulateSleepTalking()
                    HapticManager.shared.impact(style: .light)
                }) {
                    Text("💬 Talk")
                        .font(.caption)
                        .foregroundColor(.white)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(
                            Capsule()
                                .fill(.purple.opacity(0.3))
                        )
                }
            }
        }
        .padding(.top, 10)
    }

    private func toggleTracking() {
        if soundService.isRecording {
            stopTracking()
        } else {
            startTracking()
        }
    }
    
    private func startTracking() {
        do {
            try soundService.startRecording()

            let session = SleepSession()
            modelContext.insert(session)
            currentSession = session

            // Enhanced feedback
            SoundEffectsManager.shared.playTrackingStartSound()
        } catch {
            print("Failed to start recording: \(error)")
            SoundEffectsManager.shared.playError()
        }
    }
    
    private func stopTracking() {
        soundService.stopRecording()
        
        guard let session = currentSession else { return }
        
        // Save all detected events
        for event in soundService.detectedEvents {
            event.session = session
            modelContext.insert(event)
        }
        
        // End session and calculate metrics
        session.endSession()
        
        // Fetch HealthKit data
        Task {
            if let heartRate = await healthService.fetchAverageHeartRate(from: session.startTime, to: session.endTime ?? Date()) {
                session.averageHeartRate = heartRate
            }
            
            if let sleepData = await healthService.fetchSleepData(for: session.startTime) {
                session.deepSleepMinutes = sleepData.deepSleep
                session.remSleepMinutes = sleepData.remSleep
                session.lightSleepMinutes = sleepData.lightSleep
            }
            
            try? modelContext.save()
        }
        
        try? modelContext.save()
        
        // Clear events for next session
        soundService.clearEvents()
        
        // Show report
        sessionToShow = session
        showingReport = true
        
        // Enhanced feedback
        SoundEffectsManager.shared.playTrackingStopSound()

        currentSession = nil
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let hours = Int(duration) / 3600
        let minutes = (Int(duration) % 3600) / 60
        let seconds = Int(duration) % 60
        return String(format: "%02d:%02d:%02d", hours, minutes, seconds)
    }
}

struct EventCountBadge: View {
    let icon: String
    let count: Int
    let color: Color
    
    var body: some View {
        VStack(spacing: 5) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundStyle(color)
            
            Text("\(count)")
                .font(.headline)
                .foregroundColor(.white)
        }
    }
}

struct WaveformView: View {
    let level: Float
    @State private var animationPhase: CGFloat = 0

    var body: some View {
        HStack(spacing: 4) {
            ForEach(0..<30, id: \.self) { index in
                RoundedRectangle(cornerRadius: 3)
                    .fill(
                        LinearGradient(
                            colors: [
                                .blue.opacity(0.8),
                                .purple.opacity(0.8),
                                .pink.opacity(0.6)
                            ],
                            startPoint: .bottom,
                            endPoint: .top
                        )
                    )
                    .frame(width: 4)
                    .frame(height: barHeight(for: index))
                    .glow(color: .blue, radius: 3)
                    .animation(.spring(response: 0.3, dampingFraction: 0.6), value: level)
            }
        }
        .onAppear {
            withAnimation(.linear(duration: 2).repeatForever(autoreverses: false)) {
                animationPhase = 1
            }
        }
    }

    private func barHeight(for index: Int) -> CGFloat {
        let normalizedLevel = CGFloat(level) * 120
        let randomVariation = CGFloat.random(in: 0.8...1.2)
        let baseHeight = normalizedLevel * randomVariation

        // Create wave pattern with animation
        let wave = sin((Double(index) * 0.5) + Double(animationPhase) * .pi * 2) * 25
        return max(8, min(100, baseHeight + CGFloat(wave)))
    }
}



