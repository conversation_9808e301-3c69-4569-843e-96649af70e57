//
//  MainTabView.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 30/09/2025.
//

import SwiftUI
import SwiftData

struct MainTabView: View {
    @State private var selectedTab = 0
    
    var body: some View {
        TabView(selection: $selectedTab) {
            SleepTrackingView()
                .tabItem {
                    Label("Track", systemImage: "moon.stars.fill")
                }
                .tag(0)
            
            HistoryView()
                .tabItem {
                    Label("History", systemImage: "clock.fill")
                }
                .tag(1)
            
            TrendsView()
                .tabItem {
                    Label("Trends", systemImage: "chart.xyaxis.line")
                }
                .tag(2)

            InsightsView()
                .tabItem {
                    Label("Insights", systemImage: "lightbulb.fill")
                }
                .tag(3)

            CoachingView()
                .tabItem {
                    Label("Coach", systemImage: "figure.mind.and.body")
                }
                .tag(4)

            SettingsView()
                .tabItem {
                    Label("Settings", systemImage: "gearshape.fill")
                }
                .tag(5)
        }
        .tint(.blue)
    }
}

struct HistoryView: View {
    @Environment(\.modelContext) private var modelContext
    @Query(sort: \SleepSession.startTime, order: .reverse) private var sessions: [SleepSession]
    @State private var selectedSession: SleepSession?
    @State private var showingReport = false
    @State private var searchText = ""
    @State private var isRefreshing = false

    var filteredSessions: [SleepSession] {
        if searchText.isEmpty {
            return sessions
        } else {
            return sessions.filter { session in
                let dateFormatter = DateFormatter()
                dateFormatter.dateStyle = .medium
                let dateString = dateFormatter.string(from: session.startTime)
                return dateString.localizedCaseInsensitiveContains(searchText) ||
                       session.sleepQuality.localizedCaseInsensitiveContains(searchText)
            }
        }
    }

    var body: some View {
        NavigationStack {
            ZStack {
                AnimatedBackground(isTracking: false)

                if sessions.isEmpty {
                    emptyState
                } else {
                    ScrollView {
                        LazyVStack(spacing: 15) {
                            ForEach(filteredSessions) { session in
                                SessionCard(session: session)
                                    .onTapGesture {
                                        selectedSession = session
                                        showingReport = true
                                    }
                                    .swipeActions(edge: .trailing, allowsFullSwipe: false) {
                                        Button(role: .destructive) {
                                            deleteSession(session)
                                        } label: {
                                            Label("Delete", systemImage: "trash")
                                        }

                                        Button {
                                            shareSession(session)
                                        } label: {
                                            Label("Share", systemImage: "square.and.arrow.up")
                                        }
                                        .tint(.blue)
                                    }
                                    .swipeActions(edge: .leading, allowsFullSwipe: true) {
                                        Button {
                                            toggleFavorite(session)
                                        } label: {
                                            Label(
                                                session.isFavorite ? "Unfavorite" : "Favorite",
                                                systemImage: session.isFavorite ? "star.slash.fill" : "star.fill"
                                            )
                                        }
                                        .tint(.yellow)
                                    }
                            }
                        }
                        .padding()
                    }
                    .refreshable {
                        await refreshData()
                    }
                }
            }
            .navigationTitle("Sleep History")
            .searchable(text: $searchText, prompt: "Search sessions...")
            .sheet(isPresented: $showingReport) {
                if let session = selectedSession {
                    MorningReportView(session: session)
                }
            }
        }
    }

    private func deleteSession(_ session: SleepSession) {
        withAnimation {
            modelContext.delete(session)
            try? modelContext.save()
            HapticManager.shared.notification(type: .success)
        }
    }

    private func shareSession(_ session: SleepSession) {
        HapticManager.shared.impact(style: .medium)

        // Create share text
        let duration = session.endTime?.timeIntervalSince(session.startTime) ?? 0
        let hours = Int(duration) / 3600
        let minutes = Int(duration) % 3600 / 60

        let shareText = """
        🌙 My Sleep Report from SleepSound Coach

        📅 Date: \(session.startTime.formatted(date: .abbreviated, time: .omitted))
        ⏱️ Duration: \(hours)h \(minutes)m
        ⭐ Quality: \(session.sleepQuality)
        🎯 Quiet Score: \(session.quietScore)/100

        💤 Snoring: \(session.totalSnoreEvents) events
        😷 Coughing: \(session.totalCoughEvents) events
        💬 Sleep Talking: \(session.totalSleepTalkEvents) events

        Track your sleep with SleepSound Coach!
        """

        // Present share sheet
        let activityVC = UIActivityViewController(
            activityItems: [shareText],
            applicationActivities: nil
        )

        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let rootViewController = windowScene.windows.first?.rootViewController {
            activityVC.popoverPresentationController?.sourceView = rootViewController.view
            rootViewController.present(activityVC, animated: true)
        }
    }

    private func toggleFavorite(_ session: SleepSession) {
        withAnimation(.spring(response: 0.3)) {
            session.isFavorite.toggle()
        }

        HapticManager.shared.impact(style: .light)

        // Save changes
        try? modelContext.save()
    }

    private func refreshData() async {
        isRefreshing = true
        try? await Task.sleep(nanoseconds: 1_000_000_000)
        isRefreshing = false
    }
    
    private var emptyState: some View {
        ZStack {
            // Background - use photo if available, otherwise simple background
            if UIImage(named: "empty-state-photo-peaceful-sleep") != nil {
                Image("empty-state-photo-peaceful-sleep")
                    .resizable()
                    .scaledToFill()
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .clipped()
                    .blur(radius: 4)

                // Gradient overlay
                LinearGradient(
                    colors: [
                        Color.black.opacity(0.7),
                        Color.black.opacity(0.5),
                        Color.black.opacity(0.7)
                    ],
                    startPoint: .top,
                    endPoint: .bottom
                )
            }

            VStack(spacing: 20) {
                Image(systemName: "moon.zzz.fill")
                    .font(.system(size: 80))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [ColorTheme.accent, ColorTheme.secondary],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .shadow(color: .black.opacity(0.5), radius: 10, x: 0, y: 5)

                Text("No Sleep Sessions Yet")
                    .font(.title2.bold())
                    .foregroundColor(.white)
                    .shadow(color: .black.opacity(0.5), radius: 4, x: 0, y: 2)

                Text("Start tracking your sleep to see your history here")
                    .font(.body)
                    .foregroundColor(.white.opacity(0.9))
                    .multilineTextAlignment(.center)
                    .shadow(color: .black.opacity(0.5), radius: 2, x: 0, y: 1)
                    .padding(.horizontal, 40)
            }
        }
    }
}

struct SessionCard: View {
    let session: SleepSession
    
    var body: some View {
        VStack(spacing: 15) {
            HStack {
                VStack(alignment: .leading, spacing: 5) {
                    Text(session.startTime, style: .date)
                        .font(.headline)
                        .foregroundColor(.white)
                    
                    Text(session.formattedDuration)
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.7))
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 5) {
                    HStack(spacing: 5) {
                        Text("\(session.quietScore)")
                            .font(.title.bold())
                            .foregroundColor(.white)
                        
                        Image(systemName: "moon.stars.fill")
                            .foregroundStyle(scoreColor(session.quietScore))
                    }
                    
                    Text(session.sleepQuality)
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.7))
                }
            }
            
            Divider()
                .background(.white.opacity(0.3))
            
            HStack(spacing: 20) {
                EventBadge(icon: "zzz", count: session.totalSnoreEvents, color: .blue)
                EventBadge(icon: "lungs.fill", count: session.totalCoughEvents, color: .orange)
                EventBadge(icon: "bubble.left.fill", count: session.totalSleepTalkEvents, color: .purple)
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .foregroundColor(.white.opacity(0.5))
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
        )
    }
    
    private func scoreColor(_ score: Int) -> Color {
        if score >= 80 {
            return .green
        } else if score >= 60 {
            return .blue
        } else if score >= 40 {
            return .orange
        } else {
            return .red
        }
    }
}

struct EventBadge: View {
    let icon: String
    let count: Int
    let color: Color
    
    var body: some View {
        HStack(spacing: 5) {
            Image(systemName: icon)
                .font(.caption)
                .foregroundStyle(color)
            
            Text("\(count)")
                .font(.caption.bold())
                .foregroundColor(.white)
        }
    }
}

struct SettingsView: View {
    @Environment(\.modelContext) private var modelContext
    @Environment(StoreManager.self) private var storeManager // NEW: Access StoreManager
    @Query private var preferences: [UserPreferences]
    @Query(sort: \SleepSession.startTime, order: .reverse) private var sessions: [SleepSession]
    @State private var subscriptionService = SubscriptionService()
    @State private var notificationService = NotificationService()
    @State private var exportService = ExportService()
    @State private var showingSubscription = false
    @State private var showingPaywall = false // NEW: Show paywall
    @State private var showingExportOptions = false

    var currentPreferences: UserPreferences? {
        preferences.first
    }
    
    var body: some View {
        NavigationStack {
            ZStack {
                AnimatedBackground(isTracking: false)

                Form {
                    // NEW: Premium Section with StoreManager
                    Section {
                        if storeManager.isPremium {
                            HStack {
                                Image(systemName: "crown.fill")
                                    .foregroundStyle(.yellow)
                                Text("Premium Active")
                                    .foregroundColor(.white)
                                Spacer()
                                Image(systemName: "checkmark.circle.fill")
                                    .foregroundStyle(.green)
                            }

                            Button(action: {
                                Task {
                                    await storeManager.restorePurchases()
                                }
                            }) {
                                HStack {
                                    Image(systemName: "arrow.clockwise")
                                    Text("Restore Purchases")
                                        .foregroundColor(.white)
                                }
                            }
                        } else {
                            Button(action: { showingPaywall = true }) {
                                HStack {
                                    Image(systemName: "crown.fill")
                                        .foregroundStyle(.yellow)
                                    Text("Upgrade to Premium")
                                        .foregroundColor(.white)
                                    Spacer()
                                    Image(systemName: "chevron.right")
                                        .foregroundColor(.white.opacity(0.5))
                                }
                            }

                            Button(action: {
                                Task {
                                    await storeManager.restorePurchases()
                                }
                            }) {
                                HStack {
                                    Image(systemName: "arrow.clockwise")
                                    Text("Restore Purchases")
                                        .foregroundColor(.white.opacity(0.8))
                                }
                            }
                        }
                    } header: {
                        Text("Subscription")
                    } footer: {
                        if !storeManager.isPremium {
                            Text("Unlock unlimited tracking, all sounds, AI insights, and more")
                        }
                    }
                    
                    Section {
                        Toggle("Sleep Talking Transcription", isOn: Binding(
                            get: { currentPreferences?.enableSleepTalkingTranscription ?? true },
                            set: { newValue in
                                currentPreferences?.enableSleepTalkingTranscription = newValue
                                try? modelContext.save()
                            }
                        ))

                        Toggle("HealthKit Integration", isOn: Binding(
                            get: { currentPreferences?.enableHealthKitIntegration ?? true },
                            set: { newValue in
                                currentPreferences?.enableHealthKitIntegration = newValue
                                try? modelContext.save()
                            }
                        ))
                    } header: {
                        Text("Features")
                    }

                    Section {
                        Toggle("Bedtime Reminder", isOn: $notificationService.bedtimeReminderEnabled)
                            .onChange(of: notificationService.bedtimeReminderEnabled) { _, newValue in
                                if newValue {
                                    Task {
                                        if await notificationService.requestAuthorization() {
                                            notificationService.scheduleBedtimeReminder()
                                        }
                                    }
                                } else {
                                    notificationService.cancelBedtimeReminder()
                                }
                            }

                        if notificationService.bedtimeReminderEnabled {
                            DatePicker("Bedtime", selection: $notificationService.bedtimeReminderTime, displayedComponents: .hourAndMinute)
                                .onChange(of: notificationService.bedtimeReminderTime) { _, _ in
                                    notificationService.scheduleBedtimeReminder()
                                }
                        }

                        Toggle("Smart Alarm", isOn: $notificationService.smartAlarmEnabled)
                            .onChange(of: notificationService.smartAlarmEnabled) { _, newValue in
                                if newValue {
                                    Task {
                                        if await notificationService.requestAuthorization() {
                                            notificationService.scheduleSmartAlarm(session: sessions.first)
                                        }
                                    }
                                } else {
                                    notificationService.cancelSmartAlarm()
                                }
                            }

                        if notificationService.smartAlarmEnabled {
                            DatePicker("Wake Time", selection: $notificationService.smartAlarmTime, displayedComponents: .hourAndMinute)
                                .onChange(of: notificationService.smartAlarmTime) { _, _ in
                                    notificationService.scheduleSmartAlarm(session: sessions.first)
                                }
                        }
                    } header: {
                        Text("Notifications & Alarms")
                    } footer: {
                        Text("Smart alarm wakes you during light sleep within 30 minutes before your target time")
                    }

                    Section {
                        Button(action: {
                            showingExportOptions = true
                        }) {
                            HStack {
                                Image(systemName: "square.and.arrow.up")
                                Text("Export Sleep Data")
                                Spacer()
                                Image(systemName: "chevron.right")
                                    .foregroundColor(.white.opacity(0.5))
                            }
                        }
                    } header: {
                        Text("Data Export")
                    }
                    
                    Section {
                        NavigationLink {
                            PrivacyPolicyView()
                        } label: {
                            Text("Privacy Policy")
                        }
                        
                        NavigationLink {
                            AboutView()
                        } label: {
                            Text("About")
                        }
                    } header: {
                        Text("Information")
                    }
                }
                .scrollContentBackground(.hidden)
            }
            .navigationTitle("Settings")
            .sheet(isPresented: $showingPaywall) {
                PaywallView() // NEW: Use new PaywallView
            }
            .confirmationDialog("Export Sleep Data", isPresented: $showingExportOptions) {
                Button("Export as PDF") {
                    exportPDF()
                }
                Button("Export as CSV") {
                    exportCSV()
                }
                Button("Cancel", role: .cancel) {}
            } message: {
                Text("Choose export format")
            }
        }
    }

    private func exportPDF() {
        if let url = exportService.generatePDFReport(sessions: sessions) {
            shareFile(url)
        }
    }

    private func exportCSV() {
        if let url = exportService.generateCSVReport(sessions: sessions) {
            shareFile(url)
        }
    }

    private func shareFile(_ url: URL) {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first,
              let rootVC = window.rootViewController else {
            return
        }

        let activityVC = UIActivityViewController(activityItems: [url], applicationActivities: nil)
        rootVC.present(activityVC, animated: true)
    }
}

struct SubscriptionView: View {
    @Environment(\.dismiss) private var dismiss
    @ObservedObject var subscriptionService: SubscriptionService
    
    var body: some View {
        NavigationStack {
            ZStack {
                AnimatedBackground(isTracking: false)

                ScrollView {
                    VStack(spacing: 30) {
                        // Header
                        VStack(spacing: 15) {
                            Image(systemName: "crown.fill")
                                .font(.system(size: 60))
                                .foregroundStyle(.yellow)
                            
                            Text("Upgrade to Premium")
                                .font(.system(size: 32, weight: .bold))
                                .foregroundColor(.white)
                            
                            Text("Unlock unlimited history and advanced features")
                                .font(.body)
                                .foregroundColor(.white.opacity(0.7))
                                .multilineTextAlignment(.center)
                        }
                        .padding()
                        
                        // Features
                        VStack(spacing: 20) {
                            PremiumFeature(icon: "infinity", title: "Unlimited History", description: "Access all your sleep sessions")
                            PremiumFeature(icon: "waveform", title: "Audio Playback", description: "Listen to recorded sleep sounds")
                            PremiumFeature(icon: "doc.text.fill", title: "PDF Export", description: "Share reports with your doctor")
                            PremiumFeature(icon: "sparkles", title: "AI Summaries", description: "Weekly insights and trends")
                        }
                        .padding()
                        
                        // Pricing (placeholder)
                        VStack(spacing: 15) {
                            Text("Choose Your Plan")
                                .font(.headline)
                                .foregroundColor(.white)
                            
                            Button(action: {}) {
                                VStack(spacing: 5) {
                                    Text("Monthly")
                                        .font(.headline)
                                    Text("$4.99/month")
                                        .font(.title2.bold())
                                }
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(
                                    RoundedRectangle(cornerRadius: 15)
                                        .fill(.blue)
                                )
                            }
                            
                            Button(action: {}) {
                                VStack(spacing: 5) {
                                    Text("Yearly")
                                        .font(.headline)
                                    Text("$39.99/year")
                                        .font(.title2.bold())
                                    Text("Save 33%")
                                        .font(.caption)
                                }
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(
                                    RoundedRectangle(cornerRadius: 15)
                                        .fill(
                                            LinearGradient(
                                                colors: [.blue, .purple],
                                                startPoint: .leading,
                                                endPoint: .trailing
                                            )
                                        )
                                )
                            }
                        }
                        .padding()
                    }
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Close") {
                        dismiss()
                    }
                    .foregroundColor(.white)
                }
            }
        }
    }
}

struct PremiumFeature: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 15) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundStyle(.blue)
                .frame(width: 40)
            
            VStack(alignment: .leading, spacing: 3) {
                Text(title)
                    .font(.headline)
                    .foregroundColor(.white)
                
                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.7))
            }
            
            Spacer()
        }
    }
}

struct PrivacyPolicyView: View {
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                Text("Privacy Policy")
                    .font(.title.bold())
                
                Text("Your privacy is important to us. All sleep sound analysis happens on your device. We never upload or store your audio recordings on our servers.")
                
                Text("Data Collection")
                    .font(.headline)
                
                Text("We only collect anonymous usage statistics to improve the app. You can opt out in settings.")
                
                Text("HealthKit Data")
                    .font(.headline)
                
                Text("HealthKit data is only accessed with your permission and is never shared with third parties.")
            }
            .padding()
        }
        .background(Color(red: 0.1, green: 0.1, blue: 0.3))
    }
}

struct AboutView: View {
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                Image(systemName: "moon.stars.fill")
                    .font(.system(size: 80))
                    .foregroundStyle(.blue)
                
                Text("SleepSound Coach")
                    .font(.title.bold())
                
                Text("Version 1.0.0")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Text("Your personal AI-powered sleep companion")
                    .multilineTextAlignment(.center)
                    .padding()
            }
            .padding()
        }
        .background(Color(red: 0.1, green: 0.1, blue: 0.3))
    }
}

