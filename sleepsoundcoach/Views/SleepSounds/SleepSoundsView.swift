//
//  SleepSoundsView.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 01/10/2025.
//

import SwiftUI

struct SleepSoundsView: View {
    @State private var soundsService = SleepSoundsService()
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationStack {
            ZStack {
                AnimatedBackground(isTracking: false)
                
                ScrollView {
                    VStack(spacing: 25) {
                        // Header
                        headerSection
                        
                        // Currently playing
                        if soundsService.isPlaying {
                            nowPlayingCard
                        }
                        
                        // Volume control
                        volumeControl
                        
                        // Timer control
                        timerControl
                        
                        // Sound grid
                        soundGrid
                    }
                    .padding()
                }
            }
            .navigationTitle("Sleep Sounds")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                    .foregroundColor(.white)
                }
            }
        }
    }
    
    private var headerSection: some View {
        VStack(spacing: 10) {
            Image(systemName: "speaker.wave.3.fill")
                .font(.system(size: 50))
                .foregroundStyle(
                    LinearGradient(
                        colors: ColorTheme.waveformGradient,
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
            
            Text("Relax & Fall Asleep")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.white)
            
            Text("Choose calming sounds to help you drift off")
                .font(.subheadline)
                .foregroundColor(.white.opacity(0.7))
                .multilineTextAlignment(.center)
        }
        .padding()
    }
    
    private var nowPlayingCard: some View {
        VStack(spacing: 15) {
            HStack {
                Image(systemName: soundsService.currentSound?.icon ?? "speaker.wave.2.fill")
                    .font(.title2)
                    .foregroundColor(soundsService.currentSound?.color ?? .blue)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("Now Playing")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.6))
                    
                    Text(soundsService.currentSound?.rawValue ?? "")
                        .font(.headline)
                        .foregroundColor(.white)
                }
                
                Spacer()
                
                Button(action: {
                    soundsService.stopSound(fadeOut: true)
                    HapticManager.shared.impact(style: .medium)
                }) {
                    Image(systemName: "stop.circle.fill")
                        .font(.title)
                        .foregroundColor(.red)
                }
            }
            
            // Timer display
            if soundsService.timerDuration > 0 {
                HStack {
                    Image(systemName: "timer")
                        .foregroundColor(.white.opacity(0.6))
                    
                    Text(formatTime(soundsService.remainingTime))
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.8))
                    
                    Spacer()
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 20)
                .stroke(soundsService.currentSound?.color ?? .blue, lineWidth: 2)
        )
    }
    
    private var volumeControl: some View {
        VStack(alignment: .leading, spacing: 10) {
            HStack {
                Image(systemName: "speaker.fill")
                    .foregroundColor(.white.opacity(0.6))
                
                Text("Volume")
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.8))
                
                Spacer()
                
                Text("\(Int(soundsService.volume * 100))%")
                    .font(.subheadline)
                    .foregroundColor(.white)
            }
            
            Slider(value: Binding(
                get: { Double(soundsService.volume) },
                set: { soundsService.setVolume(Float($0)) }
            ), in: 0...1)
            .tint(ColorTheme.accent)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
        )
    }
    
    private var timerControl: some View {
        VStack(alignment: .leading, spacing: 10) {
            HStack {
                Image(systemName: "timer")
                    .foregroundColor(.white.opacity(0.6))
                
                Text("Sleep Timer")
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.8))
                
                Spacer()
            }
            
            HStack(spacing: 10) {
                ForEach([0, 15, 30, 45, 60], id: \.self) { minutes in
                    Button(action: {
                        soundsService.setTimer(duration: TimeInterval(minutes * 60))
                        HapticManager.shared.impact(style: .light)
                    }) {
                        Text(minutes == 0 ? "Off" : "\(minutes)m")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(soundsService.timerDuration == TimeInterval(minutes * 60) ? .white : .white.opacity(0.6))
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(
                                Capsule()
                                    .fill(soundsService.timerDuration == TimeInterval(minutes * 60) ? ColorTheme.accent : Color.white.opacity(0.1))
                            )
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
        )
    }
    
    private var soundGrid: some View {
        LazyVGrid(columns: [GridItem(.flexible()), GridItem(.flexible())], spacing: 15) {
            ForEach(SleepSound.allCases) { sound in
                SoundCard(
                    sound: sound,
                    isPlaying: soundsService.currentSound == sound && soundsService.isPlaying,
                    action: {
                        if soundsService.currentSound == sound && soundsService.isPlaying {
                            soundsService.stopSound(fadeOut: true)
                        } else {
                            soundsService.playSound(sound)
                        }
                        HapticManager.shared.impact(style: .medium)
                    }
                )
            }
        }
    }
    
    private func formatTime(_ seconds: TimeInterval) -> String {
        let minutes = Int(seconds) / 60
        let secs = Int(seconds) % 60
        return String(format: "%d:%02d", minutes, secs)
    }
}

struct SoundCard: View {
    let sound: SleepSound
    let isPlaying: Bool
    let action: () -> Void
    
    @State private var isPressing = false
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [sound.color.opacity(0.3), sound.color.opacity(0.1)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 70, height: 70)
                    
                    Image(systemName: sound.icon)
                        .font(.system(size: 30))
                        .foregroundColor(sound.color)
                    
                    if isPlaying {
                        Circle()
                            .stroke(sound.color, lineWidth: 3)
                            .frame(width: 80, height: 80)
                            .scaleEffect(isPressing ? 1.1 : 1.0)
                            .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: isPressing)
                    }
                }
                
                Text(sound.rawValue)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 20)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(.ultraThinMaterial)
            )
            .overlay(
                RoundedRectangle(cornerRadius: 20)
                    .stroke(isPlaying ? sound.color : Color.clear, lineWidth: 2)
            )
        }
        .buttonStyle(ScaleButtonStyle())
        .onAppear {
            if isPlaying {
                isPressing = true
            }
        }
        .onChange(of: isPlaying) { _, newValue in
            isPressing = newValue
        }
    }
}

#Preview {
    SleepSoundsView()
}

