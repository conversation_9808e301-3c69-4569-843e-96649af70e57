//
//  AnimatedBackground.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 30/09/2025.
//

import SwiftUI

struct AnimatedBackground: View {
    @State private var animate = false
    let isTracking: Bool

    var body: some View {
        ZStack {
            // Base gradient - optimized for dark mode nighttime viewing
            LinearGradient(
                colors: isTracking ?
                    [Color(red: 0.03, green: 0.03, blue: 0.15), Color(red: 0.08, green: 0.03, blue: 0.25)] :
                    [Color(red: 0.08, green: 0.08, blue: 0.25), Color(red: 0.15, green: 0.08, blue: 0.35)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            .animation(.easeInOut(duration: 2.0), value: isTracking)

            // Animated gradient overlay - calming blue
            RadialGradient(
                colors: [
                    Color(hex: "5B9FED").opacity(animate ? 0.25 : 0.08),
                    Color(hex: "8B5CF6").opacity(animate ? 0.18 : 0.04),
                    Color.clear
                ],
                center: animate ? .topLeading : .bottomTrailing,
                startRadius: 100,
                endRadius: 600
            )
            .ignoresSafeArea()
            .blur(radius: 30)

            // Secondary gradient - calming purple
            RadialGradient(
                colors: [
                    Color(hex: "8B5CF6").opacity(animate ? 0.18 : 0.08),
                    Color(hex: "5B9FED").opacity(animate ? 0.12 : 0.04),
                    Color.clear
                ],
                center: animate ? .bottomTrailing : .topLeading,
                startRadius: 100,
                endRadius: 600
            )
            .ignoresSafeArea()
            .blur(radius: 30)
        }
        .onAppear {
            withAnimation(.easeInOut(duration: 8).repeatForever(autoreverses: true)) {
                animate = true
            }
        }
    }
}

struct FloatingParticles: View {
    @State private var particles: [Particle] = []

    struct Particle: Identifiable {
        let id = UUID()
        var x: CGFloat
        var y: CGFloat
        var size: CGFloat
        var opacity: Double
        var speed: Double
    }

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                ForEach(particles) { particle in
                    Circle()
                        .fill(.white)
                        .frame(width: particle.size, height: particle.size)
                        .opacity(particle.opacity)
                        .position(x: particle.x, y: particle.y)
                        .blur(radius: 1)
                }
            }
            .onAppear {
                generateParticles(in: geometry.size)
                startAnimation(in: geometry.size)
            }
        }
    }
    
    private func generateParticles(in size: CGSize) {
        particles = (0..<30).map { _ in
            Particle(
                x: CGFloat.random(in: 0...size.width),
                y: CGFloat.random(in: 0...size.height),
                size: CGFloat.random(in: 2...4),
                opacity: Double.random(in: 0.1...0.4),
                speed: Double.random(in: 20...40)
            )
        }
    }
    
    private func startAnimation(in size: CGSize) {
        Timer.scheduledTimer(withTimeInterval: 0.05, repeats: true) { _ in
            for i in 0..<particles.count {
                particles[i].y -= particles[i].speed * 0.05
                
                if particles[i].y < -10 {
                    particles[i].y = size.height + 10
                    particles[i].x = CGFloat.random(in: 0...size.width)
                }
            }
        }
    }
}

struct PulsingCircle: View {
    @State private var scale: CGFloat = 1.0
    @State private var opacity: Double = 0.8
    let color: Color
    let duration: Double
    
    var body: some View {
        Circle()
            .stroke(color, lineWidth: 2)
            .scaleEffect(scale)
            .opacity(opacity)
            .onAppear {
                withAnimation(.easeOut(duration: duration).repeatForever(autoreverses: false)) {
                    scale = 2.0
                    opacity = 0.0
                }
            }
    }
}

struct SleepWaveAnimation: View {
    @State private var phase: CGFloat = 0
    let isActive: Bool
    
    var body: some View {
        GeometryReader { geometry in
            Path { path in
                let width = geometry.size.width
                let height = geometry.size.height
                let midHeight = height / 2
                
                path.move(to: CGPoint(x: 0, y: midHeight))
                
                for x in stride(from: 0, through: width, by: 1) {
                    let relativeX = x / width
                    let sine = sin((relativeX + phase) * .pi * 4)
                    let y = midHeight + sine * 20
                    path.addLine(to: CGPoint(x: x, y: y))
                }
            }
            .stroke(
                LinearGradient(
                    colors: [.blue.opacity(0.6), .purple.opacity(0.6)],
                    startPoint: .leading,
                    endPoint: .trailing
                ),
                lineWidth: 3
            )
            .blur(radius: 1)
        }
        .onAppear {
            if isActive {
                withAnimation(.linear(duration: 2).repeatForever(autoreverses: false)) {
                    phase = 1
                }
            }
        }
    }
}

struct ShimmerEffect: ViewModifier {
    @State private var phase: CGFloat = 0
    
    func body(content: Content) -> some View {
        content
            .overlay(
                LinearGradient(
                    colors: [
                        .clear,
                        .white.opacity(0.3),
                        .clear
                    ],
                    startPoint: .leading,
                    endPoint: .trailing
                )
                .offset(x: phase)
                .mask(content)
            )
            .onAppear {
                withAnimation(.linear(duration: 2).repeatForever(autoreverses: false)) {
                    phase = 300
                }
            }
    }
}

extension View {
    func shimmer() -> some View {
        modifier(ShimmerEffect())
    }
}

struct GlowEffect: ViewModifier {
    let color: Color
    let radius: CGFloat
    
    func body(content: Content) -> some View {
        content
            .shadow(color: color.opacity(0.6), radius: radius)
            .shadow(color: color.opacity(0.4), radius: radius * 0.7)
            .shadow(color: color.opacity(0.2), radius: radius * 0.4)
    }
}

extension View {
    func glow(color: Color = .blue, radius: CGFloat = 10) -> some View {
        modifier(GlowEffect(color: color, radius: radius))
    }
}

struct ScaleButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: configuration.isPressed)
    }
}

struct BounceButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.9 : 1.0)
            .animation(.spring(response: 0.4, dampingFraction: 0.5), value: configuration.isPressed)
    }
}

struct FloatingAnimation: ViewModifier {
    @State private var isFloating = false
    let duration: Double
    let distance: CGFloat
    
    func body(content: Content) -> some View {
        content
            .offset(y: isFloating ? -distance : distance)
            .onAppear {
                withAnimation(.easeInOut(duration: duration).repeatForever(autoreverses: true)) {
                    isFloating = true
                }
            }
    }
}

extension View {
    func floating(duration: Double = 2.0, distance: CGFloat = 10) -> some View {
        modifier(FloatingAnimation(duration: duration, distance: distance))
    }
}

struct RotatingGradient: View {
    @State private var rotation: Double = 0
    let colors: [Color]
    
    var body: some View {
        AngularGradient(
            colors: colors,
            center: .center,
            angle: .degrees(rotation)
        )
        .onAppear {
            withAnimation(.linear(duration: 8).repeatForever(autoreverses: false)) {
                rotation = 360
            }
        }
    }
}

