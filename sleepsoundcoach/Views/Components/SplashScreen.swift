//
//  SplashScreen.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 30/09/2025.
//

import SwiftUI

struct SplashScreen: View {
    @State private var scale: CGFloat = 0.5
    @State private var opacity: Double = 0
    @State private var rotation: Double = 0
    @Binding var isActive: Bool
    
    var body: some View {
        ZStack {
            AnimatedBackground(isTracking: false)
            
            FloatingParticles()
                .opacity(0.6)
            
            VStack(spacing: 30) {
                // App icon with animation
                ZStack {
                    // Outer glow rings
                    ForEach(0..<3, id: \.self) { index in
                        Circle()
                            .stroke(
                                LinearGradient(
                                    colors: [.blue.opacity(0.3), .purple.opacity(0.3)],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 2
                            )
                            .frame(width: 120 + CGFloat(index) * 30, height: 120 + CGFloat(index) * 30)
                            .scaleEffect(scale)
                            .opacity(1.0 - Double(index) * 0.3)
                    }
                    
                    // Main icon circle
                    Circle()
                        .fill(
                            RadialGradient(
                                colors: [
                                    .blue.opacity(0.8),
                                    .purple.opacity(0.6),
                                    .pink.opacity(0.4)
                                ],
                                center: .center,
                                startRadius: 30,
                                endRadius: 80
                            )
                        )
                        .frame(width: 120, height: 120)
                        .glow(color: .blue, radius: 20)
                        .scaleEffect(scale)
                    
                    // Moon icon
                    Image(systemName: "moon.stars.fill")
                        .font(.system(size: 50))
                        .foregroundStyle(
                            LinearGradient(
                                colors: [.white, .blue.opacity(0.8)],
                                startPoint: .top,
                                endPoint: .bottom
                            )
                        )
                        .rotationEffect(.degrees(rotation))
                        .scaleEffect(scale)
                }
                
                // App name
                VStack(spacing: 8) {
                    Text("SleepSound Coach")
                        .font(.system(size: 32, weight: .bold, design: .rounded))
                        .foregroundStyle(
                            LinearGradient(
                                colors: [.white, .blue.opacity(0.9)],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .opacity(opacity)
                    
                    Text("Your AI Sleep Companion")
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.7))
                        .opacity(opacity)
                }
                
                // Loading indicator
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    .scaleEffect(1.5)
                    .opacity(opacity)
                    .padding(.top, 20)
            }
        }
        .onAppear {
            // Animate icon
            withAnimation(.spring(response: 1.0, dampingFraction: 0.6)) {
                scale = 1.0
            }
            
            // Fade in text
            withAnimation(.easeIn(duration: 0.8).delay(0.3)) {
                opacity = 1.0
            }
            
            // Rotate moon
            withAnimation(.linear(duration: 20).repeatForever(autoreverses: false)) {
                rotation = 360
            }
            
            // Dismiss splash after delay
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.5) {
                withAnimation(.easeOut(duration: 0.5)) {
                    isActive = false
                }
            }
        }
    }
}

struct LoadingDots: View {
    @State private var animating = false
    
    var body: some View {
        HStack(spacing: 8) {
            ForEach(0..<3) { index in
                Circle()
                    .fill(.white)
                    .frame(width: 8, height: 8)
                    .scaleEffect(animating ? 1.0 : 0.5)
                    .animation(
                        .easeInOut(duration: 0.6)
                            .repeatForever()
                            .delay(Double(index) * 0.2),
                        value: animating
                    )
            }
        }
        .onAppear {
            animating = true
        }
    }
}

struct SkeletonView: View {
    @State private var animating = false
    
    var body: some View {
        RoundedRectangle(cornerRadius: 10)
            .fill(
                LinearGradient(
                    colors: [
                        .white.opacity(0.1),
                        .white.opacity(0.2),
                        .white.opacity(0.1)
                    ],
                    startPoint: animating ? .leading : .trailing,
                    endPoint: animating ? .trailing : .leading
                )
            )
            .onAppear {
                withAnimation(.linear(duration: 1.5).repeatForever(autoreverses: false)) {
                    animating = true
                }
            }
    }
}

struct PulsingLoadingView: View {
    @State private var isPulsing = false
    let text: String
    
    var body: some View {
        VStack(spacing: 20) {
            ZStack {
                Circle()
                    .fill(
                        RadialGradient(
                            colors: [.blue.opacity(0.6), .purple.opacity(0.4)],
                            center: .center,
                            startRadius: 20,
                            endRadius: 50
                        )
                    )
                    .frame(width: 80, height: 80)
                    .scaleEffect(isPulsing ? 1.2 : 1.0)
                    .opacity(isPulsing ? 0.5 : 1.0)
                
                Image(systemName: "moon.stars.fill")
                    .font(.system(size: 30))
                    .foregroundColor(.white)
            }
            
            Text(text)
                .font(.headline)
                .foregroundColor(.white)
            
            LoadingDots()
        }
        .onAppear {
            withAnimation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true)) {
                isPulsing = true
            }
        }
    }
}

struct SuccessAnimation: View {
    @State private var scale: CGFloat = 0
    @State private var checkmarkScale: CGFloat = 0
    @State private var showCheckmark = false
    
    var body: some View {
        ZStack {
            // Success circle
            Circle()
                .stroke(Color.green, lineWidth: 4)
                .frame(width: 80, height: 80)
                .scaleEffect(scale)
            
            Circle()
                .fill(Color.green.opacity(0.2))
                .frame(width: 80, height: 80)
                .scaleEffect(scale)
            
            // Checkmark
            if showCheckmark {
                Image(systemName: "checkmark")
                    .font(.system(size: 40, weight: .bold))
                    .foregroundColor(.green)
                    .scaleEffect(checkmarkScale)
            }
        }
        .onAppear {
            // Animate circle
            withAnimation(.spring(response: 0.6, dampingFraction: 0.6)) {
                scale = 1.0
            }
            
            // Show and animate checkmark
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                showCheckmark = true
                withAnimation(.spring(response: 0.4, dampingFraction: 0.5)) {
                    checkmarkScale = 1.0
                }
                
                // Haptic feedback
                HapticManager.shared.notification(type: .success)
            }
        }
    }
}

struct ErrorAnimation: View {
    @State private var scale: CGFloat = 0
    @State private var xScale: CGFloat = 0
    @State private var showX = false
    
    var body: some View {
        ZStack {
            // Error circle
            Circle()
                .stroke(Color.red, lineWidth: 4)
                .frame(width: 80, height: 80)
                .scaleEffect(scale)
            
            Circle()
                .fill(Color.red.opacity(0.2))
                .frame(width: 80, height: 80)
                .scaleEffect(scale)
            
            // X mark
            if showX {
                Image(systemName: "xmark")
                    .font(.system(size: 40, weight: .bold))
                    .foregroundColor(.red)
                    .scaleEffect(xScale)
            }
        }
        .onAppear {
            // Animate circle
            withAnimation(.spring(response: 0.6, dampingFraction: 0.6)) {
                scale = 1.0
            }
            
            // Show and animate X
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                showX = true
                withAnimation(.spring(response: 0.4, dampingFraction: 0.5)) {
                    xScale = 1.0
                }
                
                // Haptic feedback
                HapticManager.shared.notification(type: .error)
            }
        }
    }
}

