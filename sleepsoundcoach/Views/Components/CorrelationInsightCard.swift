//
//  CorrelationInsightCard.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 01/10/2025.
//

import SwiftUI

struct CorrelationInsightCard: View {
    let insight: CorrelationInsight
    @State private var isExpanded = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header
            HStack(spacing: 12) {
                // Icon
                Image(systemName: insight.icon)
                    .font(.title2)
                    .foregroundColor(correlationColor)
                    .frame(width: 40, height: 40)
                    .background(correlationColor.opacity(0.2))
                    .clipShape(Circle())
                
                // Title and strength
                VStack(alignment: .leading, spacing: 4) {
                    Text(insight.type.displayName)
                        .font(.headline)
                        .foregroundColor(ColorTheme.primaryText)
                    
                    HStack(spacing: 4) {
                        Image(systemName: insight.correlation == .positive ? "arrow.up.circle.fill" : "arrow.down.circle.fill")
                            .font(.caption)
                        Text(insight.correlation == .positive ? "Positive" : "Negative")
                            .font(.caption)
                    }
                    .foregroundColor(correlationColor)
                }
                
                Spacer()
                
                // Strength percentage
                VStack(spacing: 2) {
                    Text("\(Int(insight.strength))%")
                        .font(.title2.bold())
                        .foregroundColor(correlationColor)
                    Text("impact")
                        .font(.caption2)
                        .foregroundColor(ColorTheme.secondaryText)
                }
            }
            
            // Message
            Text(insight.message)
                .font(.subheadline)
                .foregroundColor(ColorTheme.secondaryText)
                .lineLimit(isExpanded ? nil : 2)
            
            // Recommendation (expandable)
            if isExpanded {
                VStack(alignment: .leading, spacing: 8) {
                    Divider()
                        .background(ColorTheme.secondaryText.opacity(0.3))
                    
                    HStack(alignment: .top, spacing: 8) {
                        Image(systemName: "lightbulb.fill")
                            .font(.caption)
                            .foregroundColor(ColorTheme.warningColor)
                        
                        Text(insight.recommendation)
                            .font(.caption)
                            .foregroundColor(ColorTheme.secondaryText)
                    }
                    .padding(12)
                    .background(ColorTheme.cardBackgroundElevated)
                    .cornerRadius(12)
                }
                .transition(.opacity.combined(with: .move(edge: .top)))
            }
            
            // Expand/collapse button
            Button(action: {
                withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                    isExpanded.toggle()
                }
            }) {
                HStack {
                    Spacer()
                    Text(isExpanded ? "Show Less" : "Show Recommendation")
                        .font(.caption.bold())
                        .foregroundColor(ColorTheme.accent)
                    Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                        .font(.caption)
                        .foregroundColor(ColorTheme.accent)
                    Spacer()
                }
                .padding(.vertical, 8)
            }
        }
        .padding(16)
        .background(.ultraThinMaterial)
        .cornerRadius(20)
        .overlay(
            RoundedRectangle(cornerRadius: 20)
                .stroke(correlationColor.opacity(0.3), lineWidth: 1)
        )
    }
    
    private var correlationColor: Color {
        switch insight.correlation {
        case .positive:
            return ColorTheme.correlationPositive
        case .negative:
            return ColorTheme.correlationNegative
        case .neutral:
            return ColorTheme.correlationNeutral
        }
    }
}

// MARK: - CorrelationType Extension

extension CorrelationType {
    var displayName: String {
        switch self {
        case .exercise: return "Exercise"
        case .caffeine: return "Caffeine"
        case .hrv: return "Heart Rate Variability"
        case .mindfulness: return "Mindfulness"
        case .hydration: return "Hydration"
        case .temperature: return "Body Temperature"
        }
    }
}

// MARK: - Preview

#Preview {
    ZStack {
        ColorTheme.backgroundGradientStart.ignoresSafeArea()
        
        VStack(spacing: 20) {
            CorrelationInsightCard(
                insight: CorrelationInsight(
                    type: .exercise,
                    correlation: .positive,
                    strength: 25,
                    message: "Your sleep quality improves by 25% when you exercise for 30+ minutes",
                    recommendation: "Try to exercise for at least 30 minutes, but finish 3-4 hours before bed"
                )
            )
            
            CorrelationInsightCard(
                insight: CorrelationInsight(
                    type: .caffeine,
                    correlation: .negative,
                    strength: 15,
                    message: "High caffeine intake (>200mg) reduces your sleep quality by 15%",
                    recommendation: "Try limiting caffeine to <200mg per day and avoid it after 2 PM"
                )
            )
        }
        .padding()
    }
}

