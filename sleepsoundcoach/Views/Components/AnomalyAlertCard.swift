//
//  AnomalyAlertCard.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 01/10/2025.
//

import SwiftUI

struct AnomalyAlertCard: View {
    let anomaly: HealthAnomaly
    @State private var isExpanded = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack(alignment: .top, spacing: 12) {
                // Icon
                Image(systemName: anomaly.icon)
                    .font(.title2)
                    .foregroundColor(severityColor)
                    .frame(width: 44, height: 44)
                    .background(severityColor.opacity(0.2))
                    .clipShape(Circle())
                
                // Content
                VStack(alignment: .leading, spacing: 8) {
                    // Title with severity badge
                    HStack(spacing: 8) {
                        Text(anomaly.type.displayName)
                            .font(.headline)
                            .foregroundColor(ColorTheme.primaryText)
                        
                        Spacer()
                        
                        Text(anomaly.severity.displayName)
                            .font(.caption.bold())
                            .foregroundColor(.white)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(severityColor)
                            .cornerRadius(8)
                    }
                    
                    // Message
                    Text(anomaly.message)
                        .font(.subheadline)
                        .foregroundColor(ColorTheme.secondaryText)
                        .lineLimit(isExpanded ? nil : 2)
                    
                    // Recommendation (expandable)
                    if isExpanded {
                        VStack(alignment: .leading, spacing: 8) {
                            Divider()
                                .background(ColorTheme.secondaryText.opacity(0.3))
                            
                            HStack(alignment: .top, spacing: 8) {
                                Image(systemName: "info.circle.fill")
                                    .font(.caption)
                                    .foregroundColor(ColorTheme.infoColor)
                                
                                Text(anomaly.recommendation)
                                    .font(.caption)
                                    .foregroundColor(ColorTheme.secondaryText)
                            }
                            .padding(12)
                            .background(ColorTheme.cardBackgroundElevated)
                            .cornerRadius(12)
                        }
                        .transition(.opacity.combined(with: .move(edge: .top)))
                    }
                    
                    // Expand/collapse button
                    Button(action: {
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                            isExpanded.toggle()
                        }
                    }) {
                        HStack {
                            Text(isExpanded ? "Show Less" : "Learn More")
                                .font(.caption.bold())
                                .foregroundColor(severityColor)
                            Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                                .font(.caption)
                                .foregroundColor(severityColor)
                        }
                    }
                }
            }
        }
        .padding(16)
        .background(severityColor.opacity(0.1))
        .cornerRadius(16)
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(severityColor, lineWidth: 2)
        )
    }
    
    private var severityColor: Color {
        switch anomaly.severity {
        case .low:
            return ColorTheme.anomalyLow
        case .medium:
            return ColorTheme.anomalyMedium
        case .high:
            return ColorTheme.anomalyHigh
        }
    }
}

// MARK: - Extensions

extension AnomalyType {
    var displayName: String {
        switch self {
        case .lowHRV: return "Low Heart Rate Variability"
        case .elevatedHeartRate: return "Elevated Heart Rate"
        case .abnormalBreathing: return "Abnormal Breathing Pattern"
        case .lowOxygen: return "Low Oxygen Saturation"
        }
    }
}

extension AnomalySeverity {
    var displayName: String {
        switch self {
        case .low: return "Low"
        case .medium: return "Medium"
        case .high: return "High"
        }
    }
}

// MARK: - Preview

#Preview {
    ZStack {
        ColorTheme.backgroundGradientStart.ignoresSafeArea()
        
        ScrollView {
            VStack(spacing: 16) {
                AnomalyAlertCard(
                    anomaly: HealthAnomaly(
                        type: .lowHRV,
                        severity: .high,
                        message: "Your HRV is 30% lower than average",
                        recommendation: "This may indicate high stress or poor recovery. Consider rest and stress management techniques like meditation or light exercise."
                    )
                )
                
                AnomalyAlertCard(
                    anomaly: HealthAnomaly(
                        type: .abnormalBreathing,
                        severity: .high,
                        message: "Your respiratory rate was 22.5 breaths/min (normal: 12-20)",
                        recommendation: "Abnormal breathing patterns may indicate sleep apnea or other respiratory issues. Consider consulting a doctor for a sleep study."
                    )
                )
                
                AnomalyAlertCard(
                    anomaly: HealthAnomaly(
                        type: .elevatedHeartRate,
                        severity: .medium,
                        message: "Your heart rate was 12 bpm higher than average",
                        recommendation: "Elevated heart rate during sleep may indicate stress, illness, or poor sleep environment. Ensure your bedroom is cool and dark."
                    )
                )
                
                AnomalyAlertCard(
                    anomaly: HealthAnomaly(
                        type: .lowOxygen,
                        severity: .high,
                        message: "Your oxygen saturation was 93.5% (normal: >95%)",
                        recommendation: "Low oxygen levels during sleep require medical attention. Please consult a doctor as soon as possible."
                    )
                )
            }
            .padding()
        }
    }
}

