//
//  SleepPredictionCard.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 01/10/2025.
//

import SwiftUI

struct SleepPredictionCard: View {
    let prediction: SleepPrediction
    
    var body: some View {
        VStack(spacing: 20) {
            // Header
            HStack {
                Image(systemName: "moon.stars.fill")
                    .font(.title2)
                    .foregroundColor(ColorTheme.accent)
                
                Text("Tonight's Sleep Prediction")
                    .font(.headline)
                    .foregroundColor(ColorTheme.primaryText)
                
                Spacer()
            }
            
            // Circular progress indicator
            ZStack {
                // Background circle
                Circle()
                    .stroke(ColorTheme.secondaryText.opacity(0.2), lineWidth: 12)
                
                // Progress circle
                Circle()
                    .trim(from: 0, to: prediction.predictedScore / 100)
                    .stroke(
                        LinearGradient(
                            colors: gradientForScore(prediction.predictedScore),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        style: StrokeStyle(lineWidth: 12, lineCap: .round)
                    )
                    .rotationEffect(.degrees(-90))
                    .animation(.spring(response: 1.0, dampingFraction: 0.7), value: prediction.predictedScore)
                
                // Center content
                VStack(spacing: 4) {
                    Text("\(Int(prediction.predictedScore))")
                        .font(.system(size: 56, weight: .bold, design: .rounded))
                        .foregroundColor(ColorTheme.primaryText)
                    
                    Text(prediction.qualityLevel)
                        .font(.subheadline.bold())
                        .foregroundColor(colorForScore(prediction.predictedScore))
                    
                    Text("\(Int(prediction.confidence * 100))% confident")
                        .font(.caption2)
                        .foregroundColor(ColorTheme.tertiaryText)
                }
            }
            .frame(width: 180, height: 180)
            
            // Factors
            if !prediction.factors.isEmpty {
                VStack(alignment: .leading, spacing: 12) {
                    Text("Contributing Factors")
                        .font(.subheadline.bold())
                        .foregroundColor(ColorTheme.secondaryText)
                    
                    ForEach(prediction.factors.prefix(5), id: \.name) { factor in
                        HStack(spacing: 12) {
                            Image(systemName: factor.isPositive ? "arrow.up.circle.fill" : "arrow.down.circle.fill")
                                .font(.title3)
                                .foregroundColor(factor.isPositive ? ColorTheme.successColor : ColorTheme.errorColor)
                            
                            VStack(alignment: .leading, spacing: 2) {
                                Text(factor.name)
                                    .font(.subheadline.bold())
                                    .foregroundColor(ColorTheme.primaryText)
                                
                                Text(factor.description)
                                    .font(.caption)
                                    .foregroundColor(ColorTheme.tertiaryText)
                            }
                            
                            Spacer()
                            
                            Text("\(factor.isPositive ? "+" : "")\(Int(factor.scoreChange))")
                                .font(.subheadline.bold())
                                .foregroundColor(factor.isPositive ? ColorTheme.successColor : ColorTheme.errorColor)
                        }
                        .padding(12)
                        .background(ColorTheme.cardBackground)
                        .cornerRadius(12)
                    }
                }
            }
            
            // Recommendation
            HStack(alignment: .top, spacing: 12) {
                Image(systemName: "lightbulb.fill")
                    .font(.title3)
                    .foregroundColor(ColorTheme.warningColor)
                
                Text(prediction.recommendation)
                    .font(.subheadline)
                    .foregroundColor(ColorTheme.secondaryText)
                    .multilineTextAlignment(.leading)
            }
            .padding(16)
            .background(ColorTheme.cardBackgroundElevated)
            .cornerRadius(16)
        }
        .padding(20)
        .background(.ultraThinMaterial)
        .cornerRadius(24)
        .overlay(
            RoundedRectangle(cornerRadius: 24)
                .stroke(
                    LinearGradient(
                        colors: gradientForScore(prediction.predictedScore),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 2
                )
        )
    }
    
    private func gradientForScore(_ score: Double) -> [Color] {
        if score >= 85 {
            return [ColorTheme.predictionExcellent, ColorTheme.predictionGood]
        } else if score >= 70 {
            return [ColorTheme.predictionGood, ColorTheme.accent]
        } else if score >= 55 {
            return [ColorTheme.predictionFair, ColorTheme.warningColor]
        } else {
            return [ColorTheme.predictionPoor, ColorTheme.errorColor]
        }
    }
    
    private func colorForScore(_ score: Double) -> Color {
        if score >= 85 {
            return ColorTheme.predictionExcellent
        } else if score >= 70 {
            return ColorTheme.predictionGood
        } else if score >= 55 {
            return ColorTheme.predictionFair
        } else {
            return ColorTheme.predictionPoor
        }
    }
}

// MARK: - Preview

#Preview {
    ZStack {
        ColorTheme.backgroundGradientStart.ignoresSafeArea()
        
        ScrollView {
            VStack(spacing: 20) {
                SleepPredictionCard(
                    prediction: SleepPrediction(
                        predictedScore: 82,
                        confidence: 0.85,
                        factors: [
                            PredictionFactor(name: "Exercise", scoreChange: 5, description: "Good exercise today"),
                            PredictionFactor(name: "Caffeine", scoreChange: -5, description: "High caffeine intake"),
                            PredictionFactor(name: "HRV", scoreChange: 3, description: "Good recovery"),
                            PredictionFactor(name: "Mindfulness", scoreChange: 3, description: "Meditated today")
                        ],
                        recommendation: "Good sleep expected. Consider reducing caffeine intake for even better results."
                    )
                )
                
                SleepPredictionCard(
                    prediction: SleepPrediction(
                        predictedScore: 92,
                        confidence: 0.90,
                        factors: [
                            PredictionFactor(name: "Exercise", scoreChange: 5, description: "Excellent workout"),
                            PredictionFactor(name: "HRV", scoreChange: 4, description: "Great recovery"),
                            PredictionFactor(name: "Mindfulness", scoreChange: 3, description: "Meditated 15 min")
                        ],
                        recommendation: "Excellent! You're set up for great sleep tonight. Keep up your healthy habits!"
                    )
                )
            }
            .padding()
        }
    }
}

