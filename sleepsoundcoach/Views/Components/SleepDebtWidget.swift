//
//  SleepDebtWidget.swift
//  sleepsoundcoach
//
//  Created by <PERSON><PERSON><PERSON> on 01/10/2025.
//

import SwiftUI

struct SleepDebtWidget: View {
    let sleepDebt: SleepDebt
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header
            HStack {
                Image(systemName: "moon.zzz.fill")
                    .font(.title2)
                    .foregroundColor(severityColor)
                
                Text("Sleep Debt")
                    .font(.headline)
                    .foregroundColor(ColorTheme.primaryText)
                
                Spacer()
                
                Text(sleepDebt.severity)
                    .font(.caption.bold())
                    .foregroundColor(.white)
                    .padding(.horizontal, 10)
                    .padding(.vertical, 5)
                    .background(severityColor)
                    .cornerRadius(8)
            }
            
            // Debt amount
            HStack(alignment: .firstTextBaseline, spacing: 4) {
                Text(String(format: "%.1f", sleepDebt.totalDebt))
                    .font(.system(size: 48, weight: .bold, design: .rounded))
                    .foregroundColor(severityColor)
                
                Text("hours")
                    .font(.title3)
                    .foregroundColor(ColorTheme.secondaryText)
            }
            
            // Progress bar
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    // Background
                    RoundedRectangle(cornerRadius: 8)
                        .fill(ColorTheme.secondaryText.opacity(0.2))
                        .frame(height: 8)
                    
                    // Progress
                    RoundedRectangle(cornerRadius: 8)
                        .fill(
                            LinearGradient(
                                colors: [severityColor, severityColor.opacity(0.6)],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .frame(width: min(CGFloat(sleepDebt.totalDebt / 10.0) * geometry.size.width, geometry.size.width), height: 8)
                        .animation(.spring(response: 0.6, dampingFraction: 0.7), value: sleepDebt.totalDebt)
                }
            }
            .frame(height: 8)
            
            // Recommendation
            HStack(alignment: .top, spacing: 10) {
                Image(systemName: "lightbulb.fill")
                    .font(.subheadline)
                    .foregroundColor(ColorTheme.warningColor)
                
                Text(sleepDebt.recommendation)
                    .font(.caption)
                    .foregroundColor(ColorTheme.secondaryText)
                    .fixedSize(horizontal: false, vertical: true)
            }
            .padding(12)
            .background(ColorTheme.cardBackgroundElevated)
            .cornerRadius(12)
            
            // Daily breakdown (if available)
            if !sleepDebt.dailyDeficit.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Last 7 Days")
                        .font(.caption.bold())
                        .foregroundColor(ColorTheme.tertiaryText)
                    
                    ForEach(sleepDebt.dailyDeficit.prefix(7), id: \.date) { deficit in
                        HStack {
                            Text(deficit.date, style: .date)
                                .font(.caption)
                                .foregroundColor(ColorTheme.secondaryText)
                            
                            Spacer()
                            
                            Text("-\(String(format: "%.1f", deficit.deficit / 3600.0))h")
                                .font(.caption.bold())
                                .foregroundColor(ColorTheme.errorColor)
                        }
                    }
                }
                .padding(.top, 8)
            }
        }
        .padding(20)
        .background(.ultraThinMaterial)
        .cornerRadius(20)
        .overlay(
            RoundedRectangle(cornerRadius: 20)
                .stroke(severityColor.opacity(0.5), lineWidth: 2)
        )
    }
    
    private var severityColor: Color {
        if sleepDebt.totalDebt == 0 {
            return ColorTheme.successColor
        } else if sleepDebt.totalDebt < 3 {
            return ColorTheme.warningColor
        } else if sleepDebt.totalDebt < 7 {
            return ColorTheme.errorColor
        } else {
            return Color(hex: "D32F2F")  // Dark red for severe
        }
    }
}

// MARK: - Preview

#Preview {
    ZStack {
        ColorTheme.backgroundGradientStart.ignoresSafeArea()
        
        ScrollView {
            VStack(spacing: 20) {
                // No debt
                SleepDebtWidget(
                    sleepDebt: SleepDebt(
                        totalDebt: 0,
                        dailyDeficit: [],
                        recommendation: "Excellent! You have no sleep debt. Keep maintaining your sleep schedule."
                    )
                )
                
                // Minor debt
                SleepDebtWidget(
                    sleepDebt: SleepDebt(
                        totalDebt: 2.5,
                        dailyDeficit: [
                            (Date().addingTimeInterval(-86400 * 1), 1800),
                            (Date().addingTimeInterval(-86400 * 2), 2700)
                        ],
                        recommendation: "Minor sleep debt of 2.5 hours. Try going to bed 30 minutes earlier tonight."
                    )
                )
                
                // Moderate debt
                SleepDebtWidget(
                    sleepDebt: SleepDebt(
                        totalDebt: 5.5,
                        dailyDeficit: [
                            (Date().addingTimeInterval(-86400 * 1), 3600),
                            (Date().addingTimeInterval(-86400 * 2), 2700),
                            (Date().addingTimeInterval(-86400 * 3), 3600),
                            (Date().addingTimeInterval(-86400 * 4), 1800),
                            (Date().addingTimeInterval(-86400 * 5), 2700),
                            (Date().addingTimeInterval(-86400 * 6), 3600)
                        ],
                        recommendation: "Moderate sleep debt of 5.5 hours. Plan for 1-2 recovery nights with extra sleep."
                    )
                )
                
                // Significant debt
                SleepDebtWidget(
                    sleepDebt: SleepDebt(
                        totalDebt: 8.5,
                        dailyDeficit: [
                            (Date().addingTimeInterval(-86400 * 1), 5400),
                            (Date().addingTimeInterval(-86400 * 2), 3600),
                            (Date().addingTimeInterval(-86400 * 3), 4500),
                            (Date().addingTimeInterval(-86400 * 4), 3600),
                            (Date().addingTimeInterval(-86400 * 5), 5400),
                            (Date().addingTimeInterval(-86400 * 6), 4500),
                            (Date().addingTimeInterval(-86400 * 7), 3600)
                        ],
                        recommendation: "Significant sleep debt of 8.5 hours. Prioritize sleep this week and consider naps if possible."
                    )
                )
            }
            .padding()
        }
    }
}

