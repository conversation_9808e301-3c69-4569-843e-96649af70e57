# Sleep Sounds Audio Files

## 📁 Folder Purpose
This folder contains MP3 audio files for the sleep sounds feature.

## 📝 Required Files

Place the following MP3 files in this directory:

1. **rain.mp3** - Rain sounds
2. **ocean.mp3** - Ocean waves
3. **forest.mp3** - Forest ambience
4. **whitenoise.mp3** - White noise
5. **brownnoise.mp3** - Brown noise
6. **pinknoise.mp3** - Pink noise
7. **fireplace.mp3** - Fireplace crackling
8. **thunderstorm.mp3** - Thunderstorm

## 🔧 How to Add Files to Xcode Project

### Method 1: Drag and Drop (Recommended)
1. Download or prepare your MP3 files
2. Open the Xcode project
3. In the Project Navigator (left sidebar), locate the `sleepsoundcoach/Resources/Audio` folder
4. Drag and drop your MP3 files into this folder in Xcode
5. In the dialog that appears:
   - ✅ Check "Copy items if needed"
   - ✅ Check "Create groups"
   - ✅ Ensure "sleepsoundcoach" target is selected
   - Click "Finish"

### Method 2: Add Files Menu
1. Right-click on the `sleepsoundcoach/Resources/Audio` folder in Xcode
2. Select "Add Files to 'sleepsoundcoach'..."
3. Navigate to your MP3 files
4. Select all 8 files
5. Ensure:
   - ✅ "Copy items if needed" is checked
   - ✅ "Create groups" is selected
   - ✅ "sleepsoundcoach" target is checked
6. Click "Add"

### Method 3: File System + Xcode Refresh
1. Copy your MP3 files directly to this folder in Finder:
   `/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Resources/Audio/`
2. In Xcode, right-click the `Audio` folder
3. Select "Add Files to 'sleepsoundcoach'..."
4. Navigate to this folder and select all MP3 files
5. Follow the same settings as Method 1

## ✅ Verification

After adding files, verify they're included:

1. Select your project in Xcode (top of Project Navigator)
2. Select the "sleepsoundcoach" target
3. Go to "Build Phases" tab
4. Expand "Copy Bundle Resources"
5. Verify all 8 MP3 files are listed

If files are missing:
- Click the "+" button
- Add the missing files

## 🎵 Audio File Specifications

### Recommended Settings:
- **Format**: MP3
- **Bitrate**: 128-192 kbps (good quality, reasonable file size)
- **Sample Rate**: 44.1 kHz
- **Channels**: Stereo or Mono
- **Duration**: 1-5 minutes (will loop automatically)
- **File Size**: Keep under 5 MB per file for optimal app size

### Where to Get Audio Files:

1. **Free Resources**:
   - Freesound.org (CC0 license)
   - YouTube Audio Library
   - Free Music Archive
   - Incompetech.com

2. **Paid Resources**:
   - AudioJungle
   - Pond5
   - Premium Beat

3. **Generate Your Own**:
   - Use Audacity (free) to generate noise
   - Record natural sounds
   - Use online noise generators

## 🔄 Fallback Behavior

If MP3 files are not found, the app will:
1. Attempt to load from bundle
2. Fall back to procedural audio generation
3. Display a warning in console
4. Continue functioning with generated audio

## 🐛 Troubleshooting

### Files not playing?
1. Check file names match exactly (case-sensitive)
2. Verify files are in "Copy Bundle Resources"
3. Clean build folder (Cmd+Shift+K)
4. Rebuild project (Cmd+B)
5. Check console for error messages

### Files too large?
- Compress MP3 files to lower bitrate
- Reduce duration to 1-2 minutes
- Convert to mono if stereo not needed

### Wrong audio playing?
- Verify file names match the enum cases in `SleepSound`
- Check for typos in file names

## 📱 Testing

After adding files:
1. Build and run the app
2. Navigate to Sleep Sounds
3. Tap each sound to verify playback
4. Check console for "✅ Loaded audio file" messages
5. Test volume control
6. Test sleep timer

## 💡 Tips

- Use seamless loops for better experience
- Normalize audio levels across all files
- Remove silence from start/end of files
- Test on actual device, not just simulator
- Consider adding fade-in/fade-out to loops

---

**Note**: The app will work without MP3 files using procedural generation, but real audio files provide a much better user experience!

