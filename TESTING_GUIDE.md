# 🧪 SleepSound Coach - Comprehensive Testing Guide

## 🔴 CRITICAL BUG FIXES IMPLEMENTED

### 1. Sound Detection - FIXED ✅

#### What Was Fixed:
- ✅ Added comprehensive logging throughout audio pipeline
- ✅ Lowered detection threshold from 0.03 to 0.01 (more sensitive)
- ✅ Reduced minimum event duration from 0.5s to 0.3s
- ✅ Adjusted classification thresholds:
  - Snoring: RMS threshold lowered from 0.04 to 0.015
  - Coughing: Peak threshold lowered from 0.2 to 0.1
  - Sleep Talking: RMS threshold lowered from 0.03 to 0.012
- ✅ Added detailed event logging with timestamps and metrics
- ✅ Added microphone permission checking
- ✅ Added audio session configuration logging

#### How to Test:
1. **Start Tracking**:
   - Launch app
   - Tap "Start Tracking" button
   - Check console for: `🎤 ========== STARTING SOUND ANALYSIS ==========`
   - Verify: `🔐 Microphone permission: ✅ GRANTED`
   - Verify: `✅ Audio engine started successfully`

2. **Test Sound Detection**:
   - Make sounds near the device:
     - **Snoring**: Low humming/buzzing sound
     - **Coughing**: Sharp, loud cough
     - **Sleep Talking**: Normal speech
   - Watch console for:
     - `📊 Processing audio: X buffers processed`
     - `🎚️ Audio levels: RMS=X, Peak=X, Threshold=X`
     - `🔔 ========== SOUND EVENT STARTED ==========`
     - `🏁 ========== SOUND EVENT ENDED ==========`

3. **Use Debug Buttons** (in DEBUG mode):
   - Tap "🌙 Snore" button
   - Tap "😷 Cough" button
   - Tap "💬 Talk" button
   - Each should create an event immediately

4. **Verify Events Recorded**:
   - Check event counter increases
   - Stop tracking
   - View morning report
   - Verify events appear in list

#### Expected Console Output:
```
🎤 ========== STARTING SOUND ANALYSIS ==========
   🔐 Microphone permission: ✅ GRANTED
   🔊 Configuring audio session...
   ✅ Audio session configured: category=.record, mode=.measurement
   🎛️ Creating audio engine...
   ✅ Audio engine created
   ✅ Input node obtained
   🎵 Recording format:
      📊 Sample rate: 44100.0 Hz
      📊 Channels: 1
   🔬 Creating stream analyzer...
   ✅ Stream analyzer created
   🎧 Installing audio tap (buffer size: 4096)...
   ✅ Audio tap installed
   ▶️ Starting audio engine...
   ✅ Audio engine started successfully
   🔍 Sound detection ACTIVE
      🎚️ Detection threshold: 0.01
      🎚️ Minimum event duration: 0.3s
========================================

📊 Processing audio: 100 buffers processed (10.0s since last log)
🎚️ Audio levels: RMS=0.0234, Peak=0.0567, Threshold=0.0100

🔔 ========== SOUND EVENT STARTED ==========
   Type: SNORING
   RMS: 0.0234 (threshold: 0.0100)
   Peak: 0.0567
   Exceeded RMS: true, Exceeded Peak: true
   Time: 2025-10-01 12:34:56
==========================================

🏁 ========== SOUND EVENT ENDED ==========
   Type: SNORING
   Duration: 2.34s (min: 0.30s)
   Samples: 23
   Avg Intensity: 0.0245
   Confidence: 85%
   ✅ RECORDING EVENT
   📝 Event saved to array. Total events: 1
=========================================
```

---

### 2. Sleep Sounds - FIXED ✅

#### What Was Fixed:
- ✅ Added MP3 file loading from bundle
- ✅ Created fallback to procedural generation if MP3 not found
- ✅ Fixed audio session configuration for playback
- ✅ Implemented proper AVAudioPlayer setup with looping
- ✅ Added comprehensive logging for debugging
- ✅ Fixed audio buffer generation and file writing
- ✅ Created Resources/Audio folder structure
- ✅ Added detailed README for adding MP3 files

#### How to Test:

**Without MP3 Files (Procedural Generation)**:
1. Navigate to Sleep Sounds (tap button during tracking or from menu)
2. Tap any sound (e.g., "White Noise")
3. Check console for:
   ```
   🎵 ========== PLAYING SLEEP SOUND ==========
      Sound: White Noise
      🔍 Looking for audio file: whitenoise.mp3
      ❌ Audio file not found in bundle
      ⚠️ MP3 file not found, using procedural generation
      🎛️ Generating procedural audio for: White Noise
      🔊 Configuring audio session...
      ✅ Audio session configured
      🎵 Creating audio buffer: 2646000 samples at 44100.0 Hz
      💾 Saving to temporary file: sleepsound_XXX.wav
      ✅ Audio file written successfully
      📀 Creating audio player from file...
      ✅ Audio player prepared
      ▶️ Starting playback...
      Playback: ✅ SUCCESS
      🔊 Audio is now playing
      🔊 Playback started
   =========================================
   ```
4. Verify you hear the sound playing
5. Test volume slider - sound should get louder/quieter
6. Test timer - set to 15 minutes, verify countdown
7. Test stop button - sound should stop immediately

**With MP3 Files**:
1. Add MP3 files to `sleepsoundcoach/Resources/Audio/` (see instructions below)
2. Rebuild project
3. Tap any sound
4. Check console for:
   ```
   🎵 ========== PLAYING SLEEP SOUND ==========
      Sound: Rain
      🔍 Looking for audio file: rain.mp3
      ✅ Found MP3 file: rain.mp3
      🎛️ Configuring audio session for playback...
      ✅ Audio session configured
      📀 Creating audio player...
      ✅ Audio player created and prepared
      ▶️ Starting playback...
      Playback started: ✅ SUCCESS
      🔊 Playback started
   =========================================
   ```

#### Adding MP3 Files:

**Quick Method**:
1. Download or create 8 MP3 files named:
   - `rain.mp3`
   - `ocean.mp3`
   - `forest.mp3`
   - `whitenoise.mp3`
   - `brownnoise.mp3`
   - `pinknoise.mp3`
   - `fireplace.mp3`
   - `thunderstorm.mp3`

2. In Xcode:
   - Right-click `sleepsoundcoach/Resources/Audio` folder
   - Select "Add Files to 'sleepsoundcoach'..."
   - Select all 8 MP3 files
   - ✅ Check "Copy items if needed"
   - ✅ Check "Create groups"
   - ✅ Ensure "sleepsoundcoach" target is selected
   - Click "Add"

3. Verify in Build Phases:
   - Select project → sleepsoundcoach target
   - Build Phases → Copy Bundle Resources
   - Verify all 8 MP3 files are listed

4. Clean and rebuild (Cmd+Shift+K, then Cmd+B)

**Where to Get MP3 Files**:
- Freesound.org (search for "rain loop", "ocean waves", etc.)
- YouTube Audio Library
- Generate with Audacity (Generate → Noise)
- Use online generators (mynoise.net, etc.)

---

## 🧪 COMPREHENSIVE FEATURE TESTING

### 3. Notifications Testing

#### Bedtime Reminder:
1. Go to Settings
2. Enable "Bedtime Reminder"
3. Set time to 2 minutes from now
4. Wait for notification
5. Verify notification appears with correct message

#### Smart Alarm:
1. Go to Settings
2. Enable "Smart Alarm"
3. Set time to 5 minutes from now
4. Start a tracking session
5. Wait for alarm
6. Verify alarm triggers within 30-minute window

**Note**: Notifications require device/simulator to be unlocked and app in background.

---

### 4. Sleep Tracking Testing

#### Start/Stop Session:
1. Tap "Start Tracking"
2. Verify animated moon appears
3. Verify waveform animates
4. Make sounds to trigger events
5. Tap "Stop Tracking"
6. Verify morning report appears

#### Session Data:
1. Check quiet score (0-100)
2. Check duration
3. Check event counts
4. Check HealthKit data (if available)
5. Verify charts display correctly

---

### 5. Audio Playback Testing

1. Complete a tracking session with events
2. Open morning report
3. Tap play button on any event
4. Verify audio player sheet appears
5. Test play/pause button
6. Test seek slider
7. Test skip forward/backward
8. Verify audio plays (procedurally generated)

---

### 6. Trends Testing

1. Navigate to Trends tab
2. Test period selector (7/30/90 days)
3. Test metric selector (Quiet Score, Duration, etc.)
4. Verify charts update correctly
5. Check statistics cards
6. Verify weekly comparison chart
7. Check best/worst nights section

---

### 7. Insights Testing

1. Navigate to Insights tab
2. Pull to refresh
3. Verify insights load
4. Check pattern insights section
5. Verify recommendations appear
6. Test insight cards expand/collapse

---

### 8. Export Testing

#### PDF Export:
1. Go to Settings
2. Tap "Export Sleep Data"
3. Select "PDF Report"
4. Verify share sheet appears
5. Save to Files or share via AirDrop
6. Open PDF and verify content

#### CSV Export:
1. Go to Settings
2. Tap "Export Sleep Data"
3. Select "CSV Data"
4. Verify share sheet appears
5. Save to Files
6. Open in Numbers/Excel and verify data

---

### 9. Search & Filter Testing

1. Navigate to History tab
2. Enter search term in search bar
3. Verify sessions filter in real-time
4. Test with date, score, duration keywords
5. Clear search and verify all sessions return

---

### 10. Swipe Actions Testing

1. Navigate to History tab
2. Swipe left on a session
3. Verify Delete and Share buttons appear
4. Swipe right on a session
5. Verify Favorite button appears
6. Test each action:
   - Delete: Session removed
   - Share: Share sheet appears
   - Favorite: Star icon appears

---

## 🐛 KNOWN ISSUES & LIMITATIONS

### Current Limitations:
1. **Simulator Audio**: Microphone input may not work perfectly in simulator
   - **Solution**: Test on real device for accurate sound detection
   - **Workaround**: Use debug buttons to simulate events

2. **MP3 Files Not Included**: App ships without MP3 files
   - **Solution**: Add your own MP3 files (see instructions above)
   - **Fallback**: Procedural generation works but quality varies

3. **HealthKit in Simulator**: Limited HealthKit data available
   - **Solution**: Test on real device with Apple Watch
   - **Workaround**: App works without HealthKit data

4. **Background Recording**: May be interrupted by system
   - **Solution**: Keep device plugged in and screen on
   - **Note**: Background audio capability is enabled

5. **Speech Transcription**: Requires real speech input
   - **Solution**: Test on device with actual sleep talking
   - **Note**: Transcription may not be 100% accurate

---

## ✅ VERIFICATION CHECKLIST

### Critical Features:
- [ ] Sound detection works (events are recorded)
- [ ] Sleep sounds play (with or without MP3 files)
- [ ] Sessions save to database
- [ ] Morning report displays correctly
- [ ] Trends charts render
- [ ] Export generates PDF/CSV
- [ ] Search filters sessions
- [ ] Swipe actions work

### Audio Features:
- [ ] Microphone permission granted
- [ ] Audio engine starts successfully
- [ ] RMS/Peak levels detected
- [ ] Events classified correctly
- [ ] Audio playback works
- [ ] Volume control works
- [ ] Sleep timer works

### UI/UX Features:
- [ ] Animations smooth
- [ ] Haptic feedback works
- [ ] Pull-to-refresh works
- [ ] Navigation flows correctly
- [ ] Dark mode looks good
- [ ] Text readable
- [ ] Buttons responsive

---

## 📊 PERFORMANCE TESTING

### Battery Usage:
1. Start tracking session
2. Let run for 8 hours
3. Check battery drain
4. **Expected**: < 15% battery drain overnight

### Memory Usage:
1. Start tracking session
2. Monitor memory in Xcode
3. **Expected**: < 100 MB memory usage
4. **Expected**: No memory leaks

### CPU Usage:
1. Start tracking session
2. Monitor CPU in Xcode
3. **Expected**: < 5% CPU usage average
4. **Expected**: Spikes only during event detection

---

## 🚨 TROUBLESHOOTING

### Sound Detection Not Working:
1. Check microphone permission in Settings
2. Verify audio engine started (check console)
3. Try making louder sounds
4. Lower detection threshold in code if needed
5. Test on real device instead of simulator

### Sleep Sounds Not Playing:
1. Check audio session configuration (console)
2. Verify volume is not muted
3. Check device volume
4. Try different sound types
5. Add MP3 files if using procedural generation

### App Crashes:
1. Check console for error messages
2. Verify all permissions granted
3. Clean build folder (Cmd+Shift+K)
4. Rebuild project (Cmd+B)
5. Reset simulator if needed

### No Events Recorded:
1. Verify sound detection is active (console)
2. Check audio levels in console
3. Make louder sounds
4. Use debug buttons to test
5. Check minimum event duration setting

---

## 📝 TESTING NOTES

### Best Practices:
- Test on real device for accurate results
- Use headphones to avoid feedback loops
- Test in quiet environment first
- Gradually increase ambient noise
- Test all sound types individually
- Verify data persists after app restart
- Test with low battery scenarios
- Test with airplane mode
- Test with Do Not Disturb enabled

### Regression Testing:
After any code changes, re-test:
- Sound detection
- Sleep sounds playback
- Session saving
- Morning report
- Export functionality

---

**Last Updated**: 2025-10-01
**Version**: 1.0.0
**Status**: ✅ Critical bugs fixed, ready for testing

