# 🌙 SleepSound Coach

**A professional-grade iOS sleep tracking app with advanced sound detection, AI insights, and comprehensive sleep analysis.**

![iOS](https://img.shields.io/badge/iOS-26.0+-blue.svg)
![Swift](https://img.shields.io/badge/Swift-6.0-orange.svg)
![SwiftUI](https://img.shields.io/badge/SwiftUI-6.0-green.svg)
![License](https://img.shields.io/badge/License-MIT-yellow.svg)

---

## ✨ Features

### 🎯 Core Functionality
- **Real-time Sound Detection** - FFT-based audio analysis for snoring, coughing, and sleep talking
- **Sleep Tracking** - Comprehensive session tracking with metrics and insights
- **Audio Playback** - Listen to recorded sleep events
- **Sleep Sounds** - 8 ambient sounds to help you fall asleep
- **Smart Alarm** - Wake during optimal sleep phase
- **Bedtime Reminders** - Customizable notifications

### 📊 Analytics & Insights
- **Trend Visualizations** - 7, 30, and 90-day charts
- **AI-Powered Insights** - Pattern detection and personalized recommendations
- **Sleep Scoring** - Multi-factor scoring with letter grades
- **Sleep Debt Tracking** - Monitor and recover from sleep deficit
- **HealthKit Integration** - Correlate with heart rate, oxygen, and sleep stages

### 🎨 User Experience
- **Dark Mode Only** - Optimized for nighttime use
- **Beautiful Animations** - Smooth transitions and haptic feedback
- **Glassmorphism UI** - Modern, elegant design
- **Search & Filter** - Find sessions quickly
- **Swipe Actions** - Delete, share, favorite sessions
- **Pull-to-Refresh** - Update insights and data

### 📤 Export & Sharing
- **PDF Reports** - Professional sleep reports
- **CSV Export** - Raw data for analysis
- **Share Sheet** - Email, AirDrop, save to Files

---

## 🏗️ Architecture

### Tech Stack
- **SwiftUI** - Modern declarative UI framework
- **SwiftData** - Persistence with @Model and @Query
- **AVFoundation** - Audio recording and playback
- **Accelerate** - High-performance FFT and DSP
- **HealthKit** - Sleep data and health metrics
- **Swift Charts** - Data visualization
- **UserNotifications** - Smart alarms and reminders

### Design Patterns
- **MVVM** - Model-View-ViewModel architecture
- **Observable** - State management with @Observable macro
- **Dependency Injection** - Services injected via @State
- **Reactive Programming** - SwiftData @Query for reactive updates

---

## 📱 Screenshots

### Main Features
- **Sleep Tracking** - Animated moon, real-time waveform, event counter
- **Sleep Sounds** - 8 ambient sounds with timer and volume control
- **Morning Report** - Detailed session analysis with charts
- **Trends** - Interactive charts with multiple time periods
- **Insights** - AI-generated insights and pattern detection
- **Settings** - Notifications, export, preferences

---

## 🚀 Getting Started

### Requirements
- Xcode 16.0+
- iOS 26.0+
- macOS 15.0+ (for development)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/sleepsoundcoach.git
   cd sleepsoundcoach
   ```

2. **Open in Xcode**
   ```bash
   open sleepsoundcoach.xcodeproj
   ```

3. **Build and Run**
   - Select a simulator or device
   - Press `Cmd + R` to build and run

### Permissions

The app requires the following permissions:
- **Microphone** - For sleep sound detection
- **Speech Recognition** - For sleep talking transcription
- **HealthKit** - For sleep data correlation
- **Notifications** - For bedtime reminders and alarms

All permissions are requested at appropriate times with clear explanations.

---

## 🎵 Sleep Sounds

The app includes 8 procedurally generated ambient sounds:

1. **Rain** 🌧️ - Filtered white noise
2. **Ocean Waves** 🌊 - Low-frequency oscillation
3. **Forest** 🌲 - Ambient with chirps
4. **White Noise** ⚪ - Full spectrum
5. **Brown Noise** 🟤 - Low-pass filtered
6. **Pink Noise** 🩷 - Voss-McCartney algorithm
7. **Fireplace** 🔥 - Crackling sounds
8. **Thunderstorm** ⛈️ - Thunder and rain

---

## 🧠 Sound Detection

### How It Works

1. **Audio Capture** - AVAudioEngine captures microphone input
2. **FFT Analysis** - Accelerate framework performs Fast Fourier Transform
3. **Frequency Analysis** - Analyzes low, mid, and high frequency bands
4. **Classification** - Intelligent algorithm classifies sound type:
   - **Snoring**: Low frequencies (50-300 Hz), lowRatio > 0.6
   - **Coughing**: Sharp onset, broad spectrum, peak > 0.2
   - **Sleep Talking**: Mid frequencies (300-2000 Hz), midRatio > 0.5
5. **Event Recording** - Stores event with timestamp, duration, confidence

### Sensitivity Levels
- **Low** - Only detect loud, clear events
- **Medium** - Balanced detection (default)
- **High** - Detect subtle sounds

---

## 📊 Sleep Scoring

### Scoring Algorithm

**Overall Score** = (Quietness × 0.35) + (Duration × 0.25) + (Consistency × 0.20) + (Health × 0.20)

- **Quietness Score** - Based on disturbance frequency and severity
- **Duration Score** - Optimal sleep duration (7-9 hours)
- **Consistency Score** - Regular sleep schedule
- **Health Score** - Heart rate, oxygen saturation, sleep stages

### Letter Grades
- **A+** (95-100) - Exceptional sleep
- **A** (90-94) - Excellent sleep
- **B** (80-89) - Good sleep
- **C** (70-79) - Fair sleep
- **D** (60-69) - Poor sleep
- **F** (0-59) - Very poor sleep

---

## 🔔 Smart Alarm

The smart alarm uses sleep cycle detection to wake you during light sleep:

1. **Sleep Cycles** - Detects 90-minute sleep cycles
2. **Wake Window** - 30 minutes before target time
3. **Optimal Time** - Finds nearest cycle end within window
4. **Gentle Wake** - Notification during light sleep phase

---

## 📈 Trends & Insights

### Trend Visualizations
- **7-Day Trends** - Weekly patterns
- **30-Day Trends** - Monthly overview
- **90-Day Trends** - Quarterly analysis

### Metrics Tracked
- Quiet Score
- Sleep Duration
- Snoring Events
- Heart Rate
- Coughing Events
- Sleep Talking Events

### AI Insights
- Weekend vs weekday patterns
- Bedtime correlation with quality
- Mood-sleep correlation
- Consistency analysis
- Personalized recommendations

---

## 🗂️ Project Structure

```
sleepsoundcoach/
├── Models/
│   ├── SleepSession.swift
│   ├── SoundEvent.swift
│   ├── UserPreferences.swift
│   ├── JournalEntry.swift
│   └── InsightData.swift
├── Services/
│   ├── SoundAnalysisService.swift
│   ├── SleepSoundsService.swift
│   ├── AudioPlaybackService.swift
│   ├── NotificationService.swift
│   ├── ExportService.swift
│   ├── InsightsService.swift
│   ├── SleepScoreService.swift
│   └── HealthKitService.swift
├── Views/
│   ├── Tracking/
│   ├── Reports/
│   ├── Trends/
│   ├── Insights/
│   ├── Coaching/
│   ├── SleepSounds/
│   └── Components/
├── Utilities/
│   ├── ColorTheme.swift
│   ├── HapticManager.swift
│   └── SoundEffectsManager.swift
└── sleepsoundcoachApp.swift
```

---

## 🧪 Testing

### Manual Testing Checklist

- [ ] Sound detection (snoring, coughing, sleep talking)
- [ ] Sleep sounds playback and timer
- [ ] Audio playback of recorded events
- [ ] Smart alarm scheduling
- [ ] Bedtime reminders
- [ ] Trend visualizations
- [ ] PDF/CSV export
- [ ] Search and filter
- [ ] Swipe actions
- [ ] Pull-to-refresh

### Debug Features

In DEBUG mode, test buttons are available during tracking:
- **🌙 Snore** - Simulate snoring event
- **😷 Cough** - Simulate coughing event
- **💬 Talk** - Simulate sleep talking event

---

## 🎨 Design Philosophy

### Dark Mode Only
The app is permanently locked to dark mode because:
- Used primarily at night
- Reduces eye strain
- Promotes relaxation
- Optimized for low-light environments

### Color Palette
- **Primary**: Deep blue (#1A1A4D)
- **Secondary**: Purple (#331A66)
- **Accent**: Sky blue (#5B9FED)
- **Background**: Very dark blue (#0A0A1F)

### Typography
- **Primary Text**: White (100%)
- **Secondary Text**: White (75%)
- **Tertiary Text**: White (50%)
- **Quaternary Text**: White (30%)

---

## 🤝 Contributing

Contributions are welcome! Please follow these guidelines:

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

---

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

## 🙏 Acknowledgments

- **Apple** - SwiftUI, SwiftData, HealthKit, Accelerate
- **Sleep Research** - Sleep cycle science and optimal wake times
- **Audio DSP** - FFT algorithms and frequency analysis techniques

---

## 📞 Support

For support, please open an issue on GitHub or contact [<EMAIL>](mailto:<EMAIL>).

---

## 🗺️ Roadmap

### Planned Features
- [ ] Apple Watch integration
- [ ] Widgets (lock screen & home screen)
- [ ] Sleep stories narration
- [ ] Cloud sync across devices
- [ ] Siri shortcuts
- [ ] Advanced ML models
- [ ] Social features
- [ ] Sleep environment sensors

---

**Made with ❤️ for better sleep**

*Sweet dreams! 🌙✨*

