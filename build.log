Command line invocation:
    /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild -project sleepsoundcoach.xcodeproj -scheme sleepsoundcoach -destination "platform=iOS Simulator,name=iPhone 17 Pro" clean build

2025-09-30 22:53:10.890 xcodebuild[13171:9514409] [MT] IDERunDestination: Supported platforms for the buildables in the current scheme is empty.
CreateBuildRequest

SendProjectDescription

CreateBuildOperation

** CLEAN SUCCEEDED **

ComputePackagePrebuildTargetDependencyGraph

Prepare packages

CreateBuildRequest

SendProjectDescription

CreateBuildOperation

ComputeTargetDependencyGraph
note: Building targets in dependency order
note: Target dependency graph (1 target)
    Target 'sleepsoundcoach' in project 'sleepsoundcoach' (no dependencies)

GatherProvisioningInputs

CreateBuildDescription

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator26.0.sdk -x c -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/usr/bin/actool --print-asset-tag-combinations --output-format xml1 /Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Assets.xcassets

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/usr/bin/actool --version --output-format xml1

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc --version

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld -version_details

Build description signature: b8e8cc6cf8bb39c560591c6933f062e2
Build description path: /Users/<USER>/Library/Developer/Xcode/DerivedData/sleepsoundcoach-ccfwqwohzeadzzeqevvwwkqxqfik/Build/Intermediates.noindex/XCBuildData/b8e8cc6cf8bb39c560591c6933f062e2.xcbuilddata
error: Multiple commands produce '/Users/<USER>/Library/Developer/Xcode/DerivedData/sleepsoundcoach-ccfwqwohzeadzzeqevvwwkqxqfik/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/Info.plist'
    note: Target 'sleepsoundcoach' (project 'sleepsoundcoach') has copy command from '/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Info.plist' to '/Users/<USER>/Library/Developer/Xcode/DerivedData/sleepsoundcoach-ccfwqwohzeadzzeqevvwwkqxqfik/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/Info.plist'
    note: Target 'sleepsoundcoach' (project 'sleepsoundcoach') has process command with output '/Users/<USER>/Library/Developer/Xcode/DerivedData/sleepsoundcoach-ccfwqwohzeadzzeqevvwwkqxqfik/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/Info.plist'
ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator26.0.sdk /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator26.0-23A339-26257891a6c027eb51374368541b8346.sdkstatcache
    cd /Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach.xcodeproj
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator26.0.sdk -o /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator26.0-23A339-26257891a6c027eb51374368541b8346.sdkstatcache

warning: duplicate output file '/Users/<USER>/Library/Developer/Xcode/DerivedData/sleepsoundcoach-ccfwqwohzeadzzeqevvwwkqxqfik/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/Info.plist' on task: ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/sleepsoundcoach-ccfwqwohzeadzzeqevvwwkqxqfik/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/Info.plist /Users/<USER>/Library/Developer/Xcode/DerivedData/sleepsoundcoach-ccfwqwohzeadzzeqevvwwkqxqfik/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/empty-sleepsoundcoach.plist (in target 'sleepsoundcoach' from project 'sleepsoundcoach')
** BUILD FAILED **


The following build commands failed:
	Building project sleepsoundcoach with scheme sleepsoundcoach
(1 failure)
