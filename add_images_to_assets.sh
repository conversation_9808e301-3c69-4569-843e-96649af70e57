#!/bin/bash

# Script to add images to Assets.xcassets
# Usage: ./add_images_to_assets.sh /path/to/images/folder

IMAGES_DIR="$1"
ASSETS_DIR="sleepsoundcoach/Assets.xcassets"

if [ -z "$IMAGES_DIR" ]; then
    echo "Usage: ./add_images_to_assets.sh /path/to/images/folder"
    exit 1
fi

if [ ! -d "$IMAGES_DIR" ]; then
    echo "Error: Directory $IMAGES_DIR does not exist"
    exit 1
fi

if [ ! -d "$ASSETS_DIR" ]; then
    echo "Error: Assets directory $ASSETS_DIR does not exist"
    exit 1
fi

echo "📸 Adding images to Assets.xcassets..."
echo ""

# Function to create imageset
create_imageset() {
    local image_name="$1"
    local image_path="$2"
    local imageset_dir="$ASSETS_DIR/${image_name}.imageset"
    
    # Create imageset directory
    mkdir -p "$imageset_dir"
    
    # Copy image (assuming @2x version)
    cp "$image_path" "$imageset_dir/${image_name}@2x.png"
    
    # Create Contents.json
    cat > "$imageset_dir/Contents.json" << EOF
{
  "images" : [
    {
      "filename" : "${image_name}@2x.png",
      "idiom" : "universal",
      "scale" : "2x"
    },
    {
      "filename" : "${image_name}@3x.png",
      "idiom" : "universal",
      "scale" : "3x"
    }
  ],
  "info" : {
    "author" : "xcode",
    "version" : 1
  }
}
EOF
    
    # If @3x version exists, copy it
    local image_3x="${<EMAIL>}@3x.png"
    if [ -f "$image_3x" ]; then
        cp "$image_3x" "$imageset_dir/${image_name}@3x.png"
    else
        # Otherwise, copy @2x as @3x
        cp "$image_path" "$imageset_dir/${image_name}@3x.png"
    fi
    
    echo "✅ Added: $image_name"
}

# Process all PNG files in the images directory
for image_file in "$IMAGES_DIR"/*.png; do
    if [ -f "$image_file" ]; then
        # Get filename without path and extension
        filename=$(basename "$image_file")
        image_name="${<EMAIL>}"
        image_name="${<EMAIL>}"
        image_name="${image_name%.png}"
        
        # Skip if already processed (avoid duplicates for @2x and @3x)
        if [ -d "$ASSETS_DIR/${image_name}.imageset" ]; then
            echo "⏭️  Skipped: $image_name (already exists)"
            continue
        fi
        
        create_imageset "$image_name" "$image_file"
    fi
done

echo ""
echo "✅ Done! Images added to Assets.xcassets"
echo ""
echo "Next steps:"
echo "1. Open Xcode"
echo "2. Build and run the app (⌘+R)"
echo "3. Images will appear automatically"

