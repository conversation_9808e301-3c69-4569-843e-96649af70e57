{"buildCommand": {"command": "build", "skipDependencies": false, "style": "buildOnly"}, "configuredTargets": [{"guid": "786d278e9b353e596f159d5bf8b2935cdf19a901a6405ca310256e18e9734334"}], "containerPath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach.xcodeproj", "continueBuildingAfterErrors": false, "dependencyScope": "workspace", "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "build", "activeArchitecture": "arm64", "activeRunDestination": {"disableOnlyActiveArch": false, "platform": "iphonesimulator", "sdk": "iphonesimulator26.0", "sdkVariant": "iphonesimulator", "supportedArchitectures": ["arm64"], "targetArchitecture": "arm64"}, "arenaInfo": {"buildIntermediatesPath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex", "buildProductsPath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products", "derivedDataPath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build", "indexDataStoreFolderPath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Index.noindex/DataStore", "indexEnableDataStore": true, "indexPCHPath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Index.noindex/PrecompiledHeaders", "pchPath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/PrecompiledHeaders"}, "configurationName": "Debug", "overrides": {"commandLine": {"table": {}}, "synthesized": {"table": {"ACTION": "build", "ASSETCATALOG_FILTER_FOR_DEVICE_MODEL": "iPhone18,1", "ASSETCATALOG_FILTER_FOR_DEVICE_OS_VERSION": "26.0", "ASSETCATALOG_FILTER_FOR_THINNING_DEVICE_CONFIGURATION": "iPhone18,1", "BUILD_ACTIVE_RESOURCES_ONLY": "YES", "ENABLE_PREVIEWS": "NO", "ENABLE_XOJIT_PREVIEWS": "YES", "ONLY_ACTIVE_ARCH": "YES", "TARGET_DEVICE_IDENTIFIER": "EB1E9CDC-ACD9-4F11-AC54-F7F9B921FED4", "TARGET_DEVICE_MODEL": "iPhone18,1", "TARGET_DEVICE_OS_VERSION": "26.0", "TARGET_DEVICE_PLATFORM_NAME": "iphonesimulator"}}}}, "schemeCommand": "launch", "showNonLoggedProgress": true, "useDryRun": false, "useImplicitDependencies": true, "useLegacyBuildLocations": false, "useParallelTargets": true}