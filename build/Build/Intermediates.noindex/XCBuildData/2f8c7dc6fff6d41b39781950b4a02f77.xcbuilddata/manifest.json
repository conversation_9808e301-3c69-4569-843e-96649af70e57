{"client": {"name": "basic", "version": 0, "file-system": "device-agnostic", "perform-ownership-analysis": "no"}, "targets": {"": ["<all>"]}, "nodes": {"/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex": {"is-mutated": true}, "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator": {"is-mutated": true}, "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/ExplicitPrecompiledModules": {"is-mutated": true}, "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/SwiftExplicitPrecompiledModules": {"is-mutated": true}, "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products": {"is-mutated": true}, "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator": {"is-mutated": true}, "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app": {"is-mutated": true}, "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/__preview.dylib": {"is-mutated": true}, "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach": {"is-mutated": true}, "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach.debug.dylib": {"is-mutated": true}, "<TRIGGER: CodeSign /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/__preview.dylib normal>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach normal>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach.debug.dylib normal>": {"is-command-timestamp": true}, "<TRIGGER: MkDir /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app>": {"is-command-timestamp": true}}, "commands": {"<all>": {"tool": "phony", "inputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/ExplicitPrecompiledModules", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/SwiftExplicitPrecompiledModules", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/_CodeSignature", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/__preview.dylib", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/SDKStatCaches.noindex/iphonesimulator26.0-23A339-26257891a6c027eb51374368541b8346.sdkstatcache", "<Linked Binary Debug Dylib /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach.debug.dylib>", "<target-sleepsoundcoach-****************************************************************--begin-scanning>", "<target-sleepsoundcoach-****************************************************************--end>", "<target-sleepsoundcoach-****************************************************************--linker-inputs-ready>", "<target-sleepsoundcoach-****************************************************************--modules-ready>", "<workspace-Debug-iphonesimulator26.0-iphonesimulator--stale-file-removal>"], "outputs": ["<all>"]}, "<target-sleepsoundcoach-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/ssu/root.ssu.yaml", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/_CodeSignature", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/__preview.dylib", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach.debug.dylib", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_output/thinned", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_dependencies_thinned", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_generated_info.plist_thinned", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_output/unthinned", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_dependencies_unthinned", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_generated_info.plist_unthinned", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/GeneratedAssetSymbols.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/GeneratedAssetSymbols.h", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_generated_info.plist", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/Assets.car", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_signature", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_output/thinned", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_output/unthinned", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/Info.plist", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/PkgInfo", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.app-Simulated.xcent", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.app-Simulated.xcent.der", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach Swift Compilation Finished", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightData.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/JournalEntry.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SleepSession.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SoundEvent.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/UserPreferences.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/HealthKitService.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightsService.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SoundAnalysisService.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SpeechRecognitionService.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SubscriptionService.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/CoachingView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightsView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/OnboardingView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/MorningReportView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SleepTrackingView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/MainTabView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/ContentView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/Item.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoachApp.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightData.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/JournalEntry.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SleepSession.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SoundEvent.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/UserPreferences.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/HealthKitService.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightsService.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SoundAnalysisService.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SpeechRecognitionService.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SubscriptionService.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/CoachingView.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightsView.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/OnboardingView.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/MorningReportView.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SleepTrackingView.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/MainTabView.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/ContentView.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/Item.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoachApp.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightData.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/JournalEntry.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SleepSession.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SoundEvent.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/UserPreferences.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/HealthKitService.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightsService.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SoundAnalysisService.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SpeechRecognitionService.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SubscriptionService.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/CoachingView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightsView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/OnboardingView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/MorningReportView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SleepTrackingView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/MainTabView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/Item.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoachApp.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-ExecutorLinkFileList-normal-arm64.txt", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/__preview.dylib", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach.debug.dylib", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach_lto.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach_dependency_info.dat", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach Swift Compilation Requirements Finished", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.swiftmodule", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach-linker-args.resp", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.swiftsourceinfo", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.abi.json", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach-Swift.h", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.swiftdoc", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/sleepsoundcoach-Swift.h", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/Entitlements-Simulated.plist", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach-OutputFileMap.json", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.LinkFileList", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.SwiftConstValuesFileList", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.SwiftFileList", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach_const_extract_protocols.json", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/empty-sleepsoundcoach.plist", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-DebugDylibInstallName-normal-arm64.txt", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-DebugDylibPath-normal-arm64.txt", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-all-non-framework-target-headers.hmap", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-all-target-headers.hmap", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-generated-files.hmap", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-own-target-headers.hmap", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-project-headers.hmap", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.DependencyMetadataFileList", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.DependencyStaticMetadataFileList", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.hmap"], "roots": ["/tmp/sleepsoundcoach.dst", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products"], "outputs": ["<target-sleepsoundcoach-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>"]}, "<workspace-Debug-iphonesimulator26.0-iphonesimulator--stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach-786d278e9b353e596f159d5bf8b2935c-VFS-iphonesimulator/all-product-headers.yaml"], "outputs": ["<workspace-Debug-iphonesimulator26.0-iphonesimulator--stale-file-removal>"]}, "P0:::ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator26.0.sdk /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/SDKStatCaches.noindex/iphonesimulator26.0-23A339-26257891a6c027eb51374368541b8346.sdkstatcache": {"tool": "shell", "description": "ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator26.0.sdk /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/SDKStatCaches.noindex/iphonesimulator26.0-23A339-26257891a6c027eb51374368541b8346.sdkstatcache", "inputs": [], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/SDKStatCaches.noindex/iphonesimulator26.0-23A339-26257891a6c027eb51374368541b8346.sdkstatcache", "<ClangStatCache /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/SDKStatCaches.noindex/iphonesimulator26.0-23A339-26257891a6c027eb51374368541b8346.sdkstatcache>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator26.0.sdk", "-o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/SDKStatCaches.noindex/iphonesimulator26.0-23A339-26257891a6c027eb51374368541b8346.sdkstatcache"], "env": {}, "always-out-of-date": true, "working-directory": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach.xcodeproj", "signature": "fb08bc76ac8b764762e6960e3f552e53"}, "P0:::CreateBuildDirectory /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex", "inputs": [], "outputs": ["<CreateBuildDirectory-/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex"]}, "P0:::CreateBuildDirectory /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "inputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex"], "outputs": ["<CreateBuildDirectory-/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator"]}, "P0:::CreateBuildDirectory /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/ExplicitPrecompiledModules": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/ExplicitPrecompiledModules", "inputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex"], "outputs": ["<CreateBuildDirectory-/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/ExplicitPrecompiledModules>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/ExplicitPrecompiledModules"]}, "P0:::CreateBuildDirectory /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/SwiftExplicitPrecompiledModules": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/SwiftExplicitPrecompiledModules", "inputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex"], "outputs": ["<CreateBuildDirectory-/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/SwiftExplicitPrecompiledModules>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/SwiftExplicitPrecompiledModules"]}, "P0:::CreateBuildDirectory /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products", "inputs": [], "outputs": ["<CreateBuildDirectory-/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products"]}, "P0:::CreateBuildDirectory /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator", "inputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products"], "outputs": ["<CreateBuildDirectory-/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator"]}, "P0:::Gate WorkspaceHeaderMapVFSFilesWritten": {"tool": "phony", "inputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach-786d278e9b353e596f159d5bf8b2935c-VFS-iphonesimulator/all-product-headers.yaml"], "outputs": ["<WorkspaceHeaderMapVFSFilesWritten>"]}, "P0:::Gate target-sleepsoundcoach-****************************************************************--AppExtensionInfoPlistGeneratorTaskProducer": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>"], "outputs": ["<target-sleepsoundcoach-****************************************************************--AppExtensionInfoPlistGeneratorTaskProducer>"]}, "P0:::Gate target-sleepsoundcoach-****************************************************************--AppIntentsMetadataTaskProducer": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/ssu/root.ssu.yaml", "<ExtractAppIntentsMetadata /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/Metadata.appintents>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.SwiftConstValuesFileList", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.DependencyMetadataFileList", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.DependencyStaticMetadataFileList"], "outputs": ["<target-sleepsoundcoach-****************************************************************--AppIntentsMetadataTaskProducer>"]}, "P0:::Gate target-sleepsoundcoach-****************************************************************--Barrier-ChangeAlternatePermissions": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--Barrier-ChangePermissions>", "<target-sleepsoundcoach-****************************************************************--will-sign>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>"], "outputs": ["<target-sleepsoundcoach-****************************************************************--Barrier-ChangeAlternatePermissions>"]}, "P0:::Gate target-sleepsoundcoach-****************************************************************--Barrier-ChangePermissions": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--Barrier-StripSymbols>", "<target-sleepsoundcoach-****************************************************************--will-sign>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>"], "outputs": ["<target-sleepsoundcoach-****************************************************************--Barrier-ChangePermissions>"]}, "P0:::Gate target-sleepsoundcoach-****************************************************************--Barrier-CodeSign": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-sleepsoundcoach-****************************************************************--will-sign>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>", "<CodeSign /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app>", "<CodeSign /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/__preview.dylib>", "<CodeSign /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach.debug.dylib>"], "outputs": ["<target-sleepsoundcoach-****************************************************************--Barrier-CodeSign>"]}, "P0:::Gate target-sleepsoundcoach-****************************************************************--Barrier-CopyAside": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--Barrier-GenerateStubAPI>", "<target-sleepsoundcoach-****************************************************************--will-sign>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>"], "outputs": ["<target-sleepsoundcoach-****************************************************************--Barrier-CopyAside>"]}, "P0:::Gate target-sleepsoundcoach-****************************************************************--Barrier-GenerateStubAPI": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--ProductPostprocessingTaskProducer>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>"], "outputs": ["<target-sleepsoundcoach-****************************************************************--Barrier-GenerateStubAPI>"]}, "P0:::Gate target-sleepsoundcoach-****************************************************************--Barrier-RegisterExecutionPolicyException": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--Barrier-CodeSign>", "<target-sleepsoundcoach-****************************************************************--will-sign>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>", "<RegisterExecutionPolicyException /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app>"], "outputs": ["<target-sleepsoundcoach-****************************************************************--Barrier-RegisterExecutionPolicyException>"]}, "P0:::Gate target-sleepsoundcoach-****************************************************************--Barrier-RegisterProduct": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--Barrier-Validate>", "<target-sleepsoundcoach-****************************************************************--will-sign>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>", "<Touch /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app>"], "outputs": ["<target-sleepsoundcoach-****************************************************************--Barrier-RegisterProduct>"]}, "P0:::Gate target-sleepsoundcoach-****************************************************************--Barrier-StripSymbols": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--Barrier-CopyAside>", "<target-sleepsoundcoach-****************************************************************--will-sign>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>"], "outputs": ["<target-sleepsoundcoach-****************************************************************--Barrier-StripSymbols>"]}, "P0:::Gate target-sleepsoundcoach-****************************************************************--Barrier-Validate": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-sleepsoundcoach-****************************************************************--will-sign>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>", "<Validate /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app>"], "outputs": ["<target-sleepsoundcoach-****************************************************************--Barrier-Validate>"]}, "P0:::Gate target-sleepsoundcoach-****************************************************************--CopySwiftPackageResourcesTaskProducer": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>"], "outputs": ["<target-sleepsoundcoach-****************************************************************--CopySwiftPackageResourcesTaskProducer>"]}, "P0:::Gate target-sleepsoundcoach-****************************************************************--CustomTaskProducer": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>"], "outputs": ["<target-sleepsoundcoach-****************************************************************--CustomTaskProducer>"]}, "P0:::Gate target-sleepsoundcoach-****************************************************************--DocumentationTaskProducer": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>"], "outputs": ["<target-sleepsoundcoach-****************************************************************--DocumentationTaskProducer>"]}, "P0:::Gate target-sleepsoundcoach-****************************************************************--ExtensionPointExtractorTaskProducer": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>"], "outputs": ["<target-sleepsoundcoach-****************************************************************--ExtensionPointExtractorTaskProducer>"]}, "P0:::Gate target-sleepsoundcoach-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--GeneratedFilesTaskProducer>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>"], "outputs": ["<target-sleepsoundcoach-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>"]}, "P0:::Gate target-sleepsoundcoach-****************************************************************--GeneratedFilesTaskProducer": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--ProductStructureTaskProducer>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.app-Simulated.xcent", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.app-Simulated.xcent.der", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/Entitlements-Simulated.plist"], "outputs": ["<target-sleepsoundcoach-****************************************************************--GeneratedFilesTaskProducer>"]}, "P0:::Gate target-sleepsoundcoach-****************************************************************--HeadermapTaskProducer": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-all-non-framework-target-headers.hmap", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-all-target-headers.hmap", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-generated-files.hmap", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-own-target-headers.hmap", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-project-headers.hmap", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.hmap"], "outputs": ["<target-sleepsoundcoach-****************************************************************--HeadermapTaskProducer>"]}, "P0:::Gate target-sleepsoundcoach-****************************************************************--InfoPlistTaskProducer": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/Info.plist", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/PkgInfo", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/empty-sleepsoundcoach.plist"], "outputs": ["<target-sleepsoundcoach-****************************************************************--InfoPlistTaskProducer>"]}, "P0:::Gate target-sleepsoundcoach-****************************************************************--ModuleMapTaskProducer": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>"], "outputs": ["<target-sleepsoundcoach-****************************************************************--ModuleMapTaskProducer>"]}, "P0:::Gate target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--TestEntryPointTaskProducerFactory>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>"], "outputs": ["<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>"]}, "P0:::Gate target-sleepsoundcoach-****************************************************************--ProductPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-sleepsoundcoach-****************************************************************--ModuleMapTaskProducer>", "<target-sleepsoundcoach-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-sleepsoundcoach-****************************************************************--InfoPlistTaskProducer>", "<target-sleepsoundcoach-****************************************************************--SanitizerTaskProducer>", "<target-sleepsoundcoach-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-sleepsoundcoach-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-sleepsoundcoach-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-sleepsoundcoach-****************************************************************--TestTargetTaskProducer>", "<target-sleepsoundcoach-****************************************************************--TestHostTaskProducer>", "<target-sleepsoundcoach-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-sleepsoundcoach-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-sleepsoundcoach-****************************************************************--DocumentationTaskProducer>", "<target-sleepsoundcoach-****************************************************************--CustomTaskProducer>", "<target-sleepsoundcoach-****************************************************************--StubBinaryTaskProducer>", "<target-sleepsoundcoach-****************************************************************--AppExtensionInfoPlistGeneratorTaskProducer>", "<target-sleepsoundcoach-****************************************************************--ExtensionPointExtractorTaskProducer>", "<target-sleepsoundcoach-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>"], "outputs": ["<target-sleepsoundcoach-****************************************************************--ProductPostprocessingTaskProducer>"]}, "P0:::Gate target-sleepsoundcoach-****************************************************************--ProductStructureTaskProducer": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--start>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>", "<MkDir /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app>"], "outputs": ["<target-sleepsoundcoach-****************************************************************--ProductStructureTaskProducer>"]}, "P0:::Gate target-sleepsoundcoach-****************************************************************--RealityAssetsTaskProducer": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--HeadermapTaskProducer>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>"], "outputs": ["<target-sleepsoundcoach-****************************************************************--RealityAssetsTaskProducer>"]}, "P0:::Gate target-sleepsoundcoach-****************************************************************--SanitizerTaskProducer": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>"], "outputs": ["<target-sleepsoundcoach-****************************************************************--SanitizerTaskProducer>"]}, "P0:::Gate target-sleepsoundcoach-****************************************************************--StubBinaryTaskProducer": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>"], "outputs": ["<target-sleepsoundcoach-****************************************************************--StubBinaryTaskProducer>"]}, "P0:::Gate target-sleepsoundcoach-****************************************************************--SwiftABIBaselineGenerationTaskProducer": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>"], "outputs": ["<target-sleepsoundcoach-****************************************************************--SwiftABIBaselineGenerationTaskProducer>"]}, "P0:::Gate target-sleepsoundcoach-****************************************************************--SwiftFrameworkABICheckerTaskProducer": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>"], "outputs": ["<target-sleepsoundcoach-****************************************************************--SwiftFrameworkABICheckerTaskProducer>"]}, "P0:::Gate target-sleepsoundcoach-****************************************************************--SwiftPackageCopyFilesTaskProducer": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>"], "outputs": ["<target-sleepsoundcoach-****************************************************************--SwiftPackageCopyFilesTaskProducer>"]}, "P0:::Gate target-sleepsoundcoach-****************************************************************--SwiftStandardLibrariesTaskProducer": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>", "<CopySwiftStdlib /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app>"], "outputs": ["<target-sleepsoundcoach-****************************************************************--SwiftStandardLibrariesTaskProducer>"]}, "P0:::Gate target-sleepsoundcoach-****************************************************************--TAPISymbolExtractorTaskProducer": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>"], "outputs": ["<target-sleepsoundcoach-****************************************************************--TAPISymbolExtractorTaskProducer>"]}, "P0:::Gate target-sleepsoundcoach-****************************************************************--TestEntryPointTaskProducerFactory": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--RealityAssetsTaskProducer>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>"], "outputs": ["<target-sleepsoundcoach-****************************************************************--TestEntryPointTaskProducerFactory>"]}, "P0:::Gate target-sleepsoundcoach-****************************************************************--TestHostTaskProducer": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>"], "outputs": ["<target-sleepsoundcoach-****************************************************************--TestHostTaskProducer>"]}, "P0:::Gate target-sleepsoundcoach-****************************************************************--TestTargetPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--ProductPostprocessingTaskProducer>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>"], "outputs": ["<target-sleepsoundcoach-****************************************************************--TestTargetPostprocessingTaskProducer>"]}, "P0:::Gate target-sleepsoundcoach-****************************************************************--TestTargetTaskProducer": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>"], "outputs": ["<target-sleepsoundcoach-****************************************************************--TestTargetTaskProducer>"]}, "P0:::Gate target-sleepsoundcoach-****************************************************************--copy-headers-completion": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--begin-compiling>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/GeneratedAssetSymbols.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/GeneratedAssetSymbols.h", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "outputs": ["<target-sleepsoundcoach-****************************************************************--copy-headers-completion>"]}, "P0:::Gate target-sleepsoundcoach-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_output/thinned/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_dependencies_thinned", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_generated_info.plist_thinned", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_output/unthinned/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_dependencies_unthinned", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_generated_info.plist_unthinned", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/GeneratedAssetSymbols.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/GeneratedAssetSymbols.h", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_generated_info.plist", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/Assets.car", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_signature", "<MkDir /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_output/thinned>", "<MkDir /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_output/unthinned>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach Swift Compilation Finished", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightData.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/JournalEntry.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SleepSession.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SoundEvent.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/UserPreferences.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/HealthKitService.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightsService.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SoundAnalysisService.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SpeechRecognitionService.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SubscriptionService.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/CoachingView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightsView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/OnboardingView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/MorningReportView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SleepTrackingView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/MainTabView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/ContentView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/Item.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoachApp.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightData.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/JournalEntry.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SleepSession.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SoundEvent.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/UserPreferences.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/HealthKitService.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightsService.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SoundAnalysisService.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SpeechRecognitionService.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SubscriptionService.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/CoachingView.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightsView.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/OnboardingView.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/MorningReportView.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SleepTrackingView.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/MainTabView.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/ContentView.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/Item.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoachApp.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightData.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/JournalEntry.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SleepSession.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SoundEvent.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/UserPreferences.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/HealthKitService.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightsService.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SoundAnalysisService.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SpeechRecognitionService.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SubscriptionService.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/CoachingView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightsView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/OnboardingView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/MorningReportView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SleepTrackingView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/MainTabView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/Item.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoachApp.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-ExecutorLinkFileList-normal-arm64.txt", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "<Linked Binary Preview Injection Dylib /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/__preview.dylib>", "<Linked Binary /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach_lto.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach_dependency_info.dat", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach Swift Compilation Requirements Finished", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.swiftmodule", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach-linker-args.resp", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.swiftsourceinfo", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.abi.json", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach-Swift.h", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.swiftdoc", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach-OutputFileMap.json", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.LinkFileList", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.SwiftFileList", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach_const_extract_protocols.json", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-DebugDylibInstallName-normal-arm64.txt", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-DebugDylibPath-normal-arm64.txt"], "outputs": ["<target-sleepsoundcoach-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>"]}, "P0:::Gate target-sleepsoundcoach-****************************************************************--generated-headers": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--begin-compiling>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/GeneratedAssetSymbols.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/GeneratedAssetSymbols.h", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "outputs": ["<target-sleepsoundcoach-****************************************************************--generated-headers>"]}, "P0:::Gate target-sleepsoundcoach-****************************************************************--swift-generated-headers": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--begin-compiling>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach Swift Compilation Requirements Finished", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.swiftmodule", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach-linker-args.resp", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.swiftsourceinfo", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.abi.json", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach-Swift.h", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.swiftdoc", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/sleepsoundcoach-Swift.h"], "outputs": ["<target-sleepsoundcoach-****************************************************************--swift-generated-headers>"]}, "P0:target-sleepsoundcoach-****************************************************************-:Debug:AppIntentsSSUTraining": {"tool": "shell", "description": "AppIntentsSSUTraining", "inputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/Info.plist", "<ExtractAppIntentsMetadata /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/Metadata.appintents>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.DependencyMetadataFileList", "<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-sleepsoundcoach-****************************************************************--entry>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/ssu/root.ssu.yaml"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsnltrainingprocessor", "--infoplist-path", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/Info.plist", "--temp-dir-path", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/ssu", "--bundle-id", "com.codepoint.sleepsoundcoach", "--product-path", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app", "--extracted-metadata-path", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/Metadata.appintents", "--metadata-file-list", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.DependencyMetadataFileList", "--archive-ssu-assets"], "env": {}, "working-directory": "/Volumes/Untitled/DeskStretch/sleepsoundcoach", "signature": "f8a0e31b3fd4d39ddd3fdb7d22444d7e"}, "P0:target-sleepsoundcoach-****************************************************************-:Debug:CodeSign /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app": {"tool": "code-sign-task", "description": "CodeSign /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app", "inputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/Info.plist/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Assets.xcassets/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/ContentView.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Item.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Models/InsightData.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Models/JournalEntry.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Models/SleepSession.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Models/SoundEvent.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Models/UserPreferences.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Services/HealthKitService.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Services/InsightsService.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Services/SoundAnalysisService.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Services/SpeechRecognitionService.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Services/SubscriptionService.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Views/Coaching/CoachingView.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Views/Insights/InsightsView.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Views/MainTabView.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Views/Onboarding/OnboardingView.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Views/Reports/MorningReportView.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Views/Tracking/SleepTrackingView.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/sleepsoundcoachApp.swift/", "<CodeSign /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach.debug.dylib>", "<CodeSign /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/__preview.dylib>", "<target-sleepsoundcoach-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-sleepsoundcoach-****************************************************************--will-sign>", "<target-sleepsoundcoach-****************************************************************--entry>", "<TRIGGER: Ld /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach normal>", "<TRIGGER: MkDir /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/_CodeSignature", "<CodeSign /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app>", "<TRIGGER: CodeSign /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app>"]}, "P0:target-sleepsoundcoach-****************************************************************-:Debug:CodeSign /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/__preview.dylib": {"tool": "code-sign-task", "description": "CodeSign /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/__preview.dylib", "inputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/Info.plist/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Assets.xcassets/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/ContentView.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Item.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Models/InsightData.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Models/JournalEntry.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Models/SleepSession.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Models/SoundEvent.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Models/UserPreferences.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Services/HealthKitService.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Services/InsightsService.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Services/SoundAnalysisService.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Services/SpeechRecognitionService.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Services/SubscriptionService.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Views/Coaching/CoachingView.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Views/Insights/InsightsView.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Views/MainTabView.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Views/Onboarding/OnboardingView.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Views/Reports/MorningReportView.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Views/Tracking/SleepTrackingView.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/sleepsoundcoachApp.swift/", "<CodeSign /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach.debug.dylib>", "<target-sleepsoundcoach-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-sleepsoundcoach-****************************************************************--will-sign>", "<target-sleepsoundcoach-****************************************************************--entry>", "<TRIGGER: Ld /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/__preview.dylib normal>"], "outputs": ["<CodeSign /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/__preview.dylib>"]}, "P0:target-sleepsoundcoach-****************************************************************-:Debug:CodeSign /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach.debug.dylib": {"tool": "code-sign-task", "description": "CodeSign /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach.debug.dylib", "inputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/Info.plist/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Assets.xcassets/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/ContentView.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Item.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Models/InsightData.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Models/JournalEntry.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Models/SleepSession.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Models/SoundEvent.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Models/UserPreferences.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Services/HealthKitService.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Services/InsightsService.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Services/SoundAnalysisService.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Services/SpeechRecognitionService.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Services/SubscriptionService.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Views/Coaching/CoachingView.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Views/Insights/InsightsView.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Views/MainTabView.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Views/Onboarding/OnboardingView.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Views/Reports/MorningReportView.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Views/Tracking/SleepTrackingView.swift/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/sleepsoundcoachApp.swift/", "<target-sleepsoundcoach-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-sleepsoundcoach-****************************************************************--will-sign>", "<target-sleepsoundcoach-****************************************************************--entry>", "<TRIGGER: Ld /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach.debug.dylib normal>"], "outputs": ["<CodeSign /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach.debug.dylib>"]}, "P0:target-sleepsoundcoach-****************************************************************-:Debug:CompileAssetCatalogVariant thinned /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app /Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant thinned /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app /Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Assets.xcassets", "inputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Assets.xcassets/", "<MkDir /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_output/thinned>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_output/thinned", "<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_output/thinned/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_dependencies_thinned", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_generated_info.plist_thinned"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Assets.xcassets", "--compile", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_output/thinned", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_dependencies_thinned", "--output-partial-info-plist", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_generated_info.plist_thinned", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--filter-for-thinning-device-configuration", "iPhone18,1", "--filter-for-device-os-version", "26.0", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "26.0", "--platform", "iphonesimulator"], "env": {}, "working-directory": "/Volumes/Untitled/DeskStretch/sleepsoundcoach", "control-enabled": false, "deps": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_dependencies_thinned"], "deps-style": "dependency-info", "signature": "fc5d4bf0cbcb0b5508a14730432b79cb"}, "P0:target-sleepsoundcoach-****************************************************************-:Debug:CompileAssetCatalogVariant unthinned /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app /Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant unthinned /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app /Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Assets.xcassets", "inputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Assets.xcassets/", "<MkDir /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_output/unthinned>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_output/unthinned", "<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_output/unthinned/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_dependencies_unthinned", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_generated_info.plist_unthinned"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Assets.xcassets", "--compile", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_output/unthinned", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_dependencies_unthinned", "--output-partial-info-plist", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_generated_info.plist_unthinned", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "26.0", "--platform", "iphonesimulator"], "env": {}, "working-directory": "/Volumes/Untitled/DeskStretch/sleepsoundcoach", "control-enabled": false, "deps": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_dependencies_unthinned"], "deps-style": "dependency-info", "signature": "d4b0d537a4a46874663365f90f6a9138"}, "P0:target-sleepsoundcoach-****************************************************************-:Debug:CopySwiftLibs /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app": {"tool": "embed-swift-stdlib", "description": "CopySwiftLibs /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app", "inputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach.debug.dylib", "<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-sleepsoundcoach-****************************************************************--immediate>"], "outputs": ["<CopySwiftStdlib /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app>"], "deps": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/SwiftStdLibToolInputDependencies.dep"}, "P0:target-sleepsoundcoach-****************************************************************-:Debug:ExtractAppIntentsMetadata": {"tool": "shell", "description": "ExtractAppIntentsMetadata", "inputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Models/InsightData.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Models/JournalEntry.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Models/SleepSession.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Models/SoundEvent.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Models/UserPreferences.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Services/HealthKitService.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Services/InsightsService.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Services/SoundAnalysisService.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Services/SpeechRecognitionService.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Services/SubscriptionService.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Views/Coaching/CoachingView.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Views/Insights/InsightsView.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Views/Onboarding/OnboardingView.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Views/Reports/MorningReportView.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Views/Tracking/SleepTrackingView.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Views/MainTabView.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/ContentView.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Item.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/sleepsoundcoachApp.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightData.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/JournalEntry.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SleepSession.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SoundEvent.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/UserPreferences.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/HealthKitService.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightsService.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SoundAnalysisService.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SpeechRecognitionService.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SubscriptionService.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/CoachingView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightsView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/OnboardingView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/MorningReportView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SleepTrackingView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/MainTabView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/Item.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoachApp.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.DependencyMetadataFileList", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.DependencyStaticMetadataFileList", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach_dependency_info.dat", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.SwiftFileList", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.SwiftConstValuesFileList", "<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-sleepsoundcoach-****************************************************************--entry>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "<ExtractAppIntentsMetadata /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/Metadata.appintents>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsmetadataprocessor", "--toolchain-dir", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain", "--module-name", "sleepsoundcoach", "--sdk-root", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator26.0.sdk", "--xcode-version", "17A324", "--platform-family", "iOS", "--deployment-target", "26.0", "--bundle-identifier", "com.codepoint.sleepsoundcoach", "--output", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app", "--target-triple", "arm64-apple-ios26.0-simulator", "--binary-file", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach", "--dependency-file", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach_dependency_info.dat", "--stringsdata-file", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "--source-file-list", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.SwiftFileList", "--metadata-file-list", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.DependencyMetadataFileList", "--static-metadata-file-list", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.DependencyStaticMetadataFileList", "--swift-const-vals-list", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.SwiftConstValuesFileList", "--compile-time-extraction", "--deployment-aware-processing", "--validate-assistant-intents", "--no-app-shortcuts-localization"], "env": {}, "working-directory": "/Volumes/Untitled/DeskStretch/sleepsoundcoach", "signature": "0839a57b910b169c2fec00f8f4d3f70b"}, "P0:target-sleepsoundcoach-****************************************************************-:Debug:Gate target-sleepsoundcoach-****************************************************************--begin-compiling": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/sleepsoundcoach.dst>", "<CreateBuildDirectory-/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products>", "<CreateBuildDirectory-/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>", "<CreateBuildDirectory-/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/SwiftExplicitPrecompiledModules>", "<CreateBuildDirectory-/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/ExplicitPrecompiledModules>"], "outputs": ["<target-sleepsoundcoach-****************************************************************--begin-compiling>"]}, "P0:target-sleepsoundcoach-****************************************************************-:Debug:Gate target-sleepsoundcoach-****************************************************************--begin-linking": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/sleepsoundcoach.dst>", "<CreateBuildDirectory-/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products>", "<CreateBuildDirectory-/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>", "<CreateBuildDirectory-/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/SwiftExplicitPrecompiledModules>", "<CreateBuildDirectory-/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/ExplicitPrecompiledModules>"], "outputs": ["<target-sleepsoundcoach-****************************************************************--begin-linking>"]}, "P0:target-sleepsoundcoach-****************************************************************-:Debug:Gate target-sleepsoundcoach-****************************************************************--begin-scanning": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/sleepsoundcoach.dst>", "<CreateBuildDirectory-/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products>", "<CreateBuildDirectory-/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>", "<CreateBuildDirectory-/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/SwiftExplicitPrecompiledModules>", "<CreateBuildDirectory-/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/ExplicitPrecompiledModules>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>"], "outputs": ["<target-sleepsoundcoach-****************************************************************--begin-scanning>"]}, "P0:target-sleepsoundcoach-****************************************************************-:Debug:Gate target-sleepsoundcoach-****************************************************************--end": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--entry>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/ssu/root.ssu.yaml", "<CodeSign /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app>", "<CodeSign /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/__preview.dylib>", "<CodeSign /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach.debug.dylib>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_output/thinned/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_dependencies_thinned", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_generated_info.plist_thinned", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_output/unthinned/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_dependencies_unthinned", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_generated_info.plist_unthinned", "<CopySwiftStdlib /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app>", "<ExtractAppIntentsMetadata /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/Metadata.appintents>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/GeneratedAssetSymbols.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/GeneratedAssetSymbols.h", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_generated_info.plist", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/Assets.car", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_signature", "<MkDir /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_output/thinned>", "<MkDir /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_output/unthinned>", "<MkDir /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/Info.plist", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/PkgInfo", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.app-Simulated.xcent", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.app-Simulated.xcent.der", "<RegisterExecutionPolicyException /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach Swift Compilation Finished", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightData.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/JournalEntry.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SleepSession.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SoundEvent.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/UserPreferences.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/HealthKitService.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightsService.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SoundAnalysisService.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SpeechRecognitionService.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SubscriptionService.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/CoachingView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightsView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/OnboardingView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/MorningReportView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SleepTrackingView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/MainTabView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/ContentView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/Item.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoachApp.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightData.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/JournalEntry.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SleepSession.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SoundEvent.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/UserPreferences.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/HealthKitService.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightsService.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SoundAnalysisService.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SpeechRecognitionService.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SubscriptionService.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/CoachingView.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightsView.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/OnboardingView.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/MorningReportView.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SleepTrackingView.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/MainTabView.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/ContentView.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/Item.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoachApp.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightData.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/JournalEntry.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SleepSession.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SoundEvent.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/UserPreferences.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/HealthKitService.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightsService.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SoundAnalysisService.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SpeechRecognitionService.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SubscriptionService.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/CoachingView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightsView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/OnboardingView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/MorningReportView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SleepTrackingView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/MainTabView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/Item.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoachApp.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "<Touch /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app>", "<Validate /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-ExecutorLinkFileList-normal-arm64.txt", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "<Linked Binary Preview Injection Dylib /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/__preview.dylib>", "<Linked Binary /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach_lto.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach_dependency_info.dat", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach Swift Compilation Requirements Finished", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.swiftmodule", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach-linker-args.resp", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.swiftsourceinfo", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.abi.json", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach-Swift.h", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.swiftdoc", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/sleepsoundcoach-Swift.h", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/sleepsoundcoach-Swift.h", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/Entitlements-Simulated.plist", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach-OutputFileMap.json", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.LinkFileList", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.SwiftConstValuesFileList", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.SwiftFileList", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach_const_extract_protocols.json", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/empty-sleepsoundcoach.plist", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-DebugDylibInstallName-normal-arm64.txt", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-DebugDylibPath-normal-arm64.txt", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-all-non-framework-target-headers.hmap", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-all-target-headers.hmap", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-generated-files.hmap", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-own-target-headers.hmap", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-project-headers.hmap", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.DependencyMetadataFileList", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.DependencyStaticMetadataFileList", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.hmap", "<target-sleepsoundcoach-****************************************************************--AppExtensionInfoPlistGeneratorTaskProducer>", "<target-sleepsoundcoach-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-sleepsoundcoach-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-sleepsoundcoach-****************************************************************--Barrier-ChangePermissions>", "<target-sleepsoundcoach-****************************************************************--Barrier-CodeSign>", "<target-sleepsoundcoach-****************************************************************--Barrier-CopyAside>", "<target-sleepsoundcoach-****************************************************************--Barrier-GenerateStubAPI>", "<target-sleepsoundcoach-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-sleepsoundcoach-****************************************************************--Barrier-RegisterProduct>", "<target-sleepsoundcoach-****************************************************************--Barrier-StripSymbols>", "<target-sleepsoundcoach-****************************************************************--Barrier-Validate>", "<target-sleepsoundcoach-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-sleepsoundcoach-****************************************************************--CustomTaskProducer>", "<target-sleepsoundcoach-****************************************************************--DocumentationTaskProducer>", "<target-sleepsoundcoach-****************************************************************--ExtensionPointExtractorTaskProducer>", "<target-sleepsoundcoach-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-sleepsoundcoach-****************************************************************--GeneratedFilesTaskProducer>", "<target-sleepsoundcoach-****************************************************************--HeadermapTaskProducer>", "<target-sleepsoundcoach-****************************************************************--InfoPlistTaskProducer>", "<target-sleepsoundcoach-****************************************************************--ModuleMapTaskProducer>", "<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--ProductPostprocessingTaskProducer>", "<target-sleepsoundcoach-****************************************************************--ProductStructureTaskProducer>", "<target-sleepsoundcoach-****************************************************************--RealityAssetsTaskProducer>", "<target-sleepsoundcoach-****************************************************************--SanitizerTaskProducer>", "<target-sleepsoundcoach-****************************************************************--StubBinaryTaskProducer>", "<target-sleepsoundcoach-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-sleepsoundcoach-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-sleepsoundcoach-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-sleepsoundcoach-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-sleepsoundcoach-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-sleepsoundcoach-****************************************************************--TestEntryPointTaskProducerFactory>", "<target-sleepsoundcoach-****************************************************************--TestHostTaskProducer>", "<target-sleepsoundcoach-****************************************************************--TestTargetPostprocessingTaskProducer>", "<target-sleepsoundcoach-****************************************************************--TestTargetTaskProducer>", "<target-sleepsoundcoach-****************************************************************--copy-headers-completion>", "<target-sleepsoundcoach-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-sleepsoundcoach-****************************************************************--generated-headers>", "<target-sleepsoundcoach-****************************************************************--swift-generated-headers>"], "outputs": ["<target-sleepsoundcoach-****************************************************************--end>"]}, "P0:target-sleepsoundcoach-****************************************************************-:Debug:Gate target-sleepsoundcoach-****************************************************************--entry": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/sleepsoundcoach.dst>", "<CreateBuildDirectory-/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products>", "<CreateBuildDirectory-/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>", "<CreateBuildDirectory-/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/SwiftExplicitPrecompiledModules>", "<CreateBuildDirectory-/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/ExplicitPrecompiledModules>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>"], "outputs": ["<target-sleepsoundcoach-****************************************************************--entry>"]}, "P0:target-sleepsoundcoach-****************************************************************-:Debug:Gate target-sleepsoundcoach-****************************************************************--immediate": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/sleepsoundcoach.dst>", "<CreateBuildDirectory-/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products>", "<CreateBuildDirectory-/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>", "<CreateBuildDirectory-/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/SwiftExplicitPrecompiledModules>", "<CreateBuildDirectory-/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/ExplicitPrecompiledModules>"], "outputs": ["<target-sleepsoundcoach-****************************************************************--immediate>"]}, "P0:target-sleepsoundcoach-****************************************************************-:Debug:Gate target-sleepsoundcoach-****************************************************************--linker-inputs-ready": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--begin-compiling>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-ExecutorLinkFileList-normal-arm64.txt", "<Linked Binary Preview Injection Dylib /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/__preview.dylib>", "<Linked Binary /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach_lto.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach_dependency_info.dat", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach Swift Compilation Requirements Finished", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.swiftmodule", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach-linker-args.resp", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.swiftsourceinfo", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.abi.json", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach-Swift.h", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.swiftdoc", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.LinkFileList", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-DebugDylibInstallName-normal-arm64.txt", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-DebugDylibPath-normal-arm64.txt"], "outputs": ["<target-sleepsoundcoach-****************************************************************--linker-inputs-ready>"]}, "P0:target-sleepsoundcoach-****************************************************************-:Debug:Gate target-sleepsoundcoach-****************************************************************--modules-ready": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--begin-compiling>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach Swift Compilation Requirements Finished", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.swiftmodule", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach-linker-args.resp", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.swiftsourceinfo", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.abi.json", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach-Swift.h", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.swiftdoc", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/sleepsoundcoach-Swift.h"], "outputs": ["<target-sleepsoundcoach-****************************************************************--modules-ready>"]}, "P0:target-sleepsoundcoach-****************************************************************-:Debug:Gate target-sleepsoundcoach-****************************************************************--unsigned-product-ready": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--begin-compiling>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/ssu/root.ssu.yaml", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_output/thinned/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_dependencies_thinned", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_generated_info.plist_thinned", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_output/unthinned/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_dependencies_unthinned", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_generated_info.plist_unthinned", "<CopySwiftStdlib /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app>", "<ExtractAppIntentsMetadata /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/Metadata.appintents>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/GeneratedAssetSymbols.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/GeneratedAssetSymbols.h", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_generated_info.plist", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/Assets.car", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_signature", "<MkDir /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_output/thinned>", "<MkDir /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_output/unthinned>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.app-Simulated.xcent", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.app-Simulated.xcent.der", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach Swift Compilation Finished", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightData.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/JournalEntry.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SleepSession.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SoundEvent.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/UserPreferences.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/HealthKitService.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightsService.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SoundAnalysisService.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SpeechRecognitionService.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SubscriptionService.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/CoachingView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightsView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/OnboardingView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/MorningReportView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SleepTrackingView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/MainTabView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/ContentView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/Item.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoachApp.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightData.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/JournalEntry.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SleepSession.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SoundEvent.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/UserPreferences.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/HealthKitService.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightsService.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SoundAnalysisService.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SpeechRecognitionService.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SubscriptionService.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/CoachingView.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightsView.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/OnboardingView.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/MorningReportView.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SleepTrackingView.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/MainTabView.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/ContentView.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/Item.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoachApp.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightData.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/JournalEntry.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SleepSession.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SoundEvent.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/UserPreferences.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/HealthKitService.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightsService.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SoundAnalysisService.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SpeechRecognitionService.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SubscriptionService.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/CoachingView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightsView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/OnboardingView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/MorningReportView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SleepTrackingView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/MainTabView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/Item.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoachApp.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-ExecutorLinkFileList-normal-arm64.txt", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "<Linked Binary Preview Injection Dylib /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/__preview.dylib>", "<Linked Binary /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach_lto.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach_dependency_info.dat", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach Swift Compilation Requirements Finished", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.swiftmodule", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach-linker-args.resp", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.swiftsourceinfo", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.abi.json", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach-Swift.h", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.swiftdoc", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/sleepsoundcoach-Swift.h", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/Entitlements-Simulated.plist", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach-OutputFileMap.json", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.LinkFileList", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.SwiftConstValuesFileList", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.SwiftFileList", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach_const_extract_protocols.json", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-DebugDylibInstallName-normal-arm64.txt", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-DebugDylibPath-normal-arm64.txt", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.DependencyMetadataFileList", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.DependencyStaticMetadataFileList", "<target-sleepsoundcoach-****************************************************************--Barrier-GenerateStubAPI>"], "outputs": ["<target-sleepsoundcoach-****************************************************************--unsigned-product-ready>"]}, "P0:target-sleepsoundcoach-****************************************************************-:Debug:Gate target-sleepsoundcoach-****************************************************************--will-sign": {"tool": "phony", "inputs": ["<target-sleepsoundcoach-****************************************************************--unsigned-product-ready>"], "outputs": ["<target-sleepsoundcoach-****************************************************************--will-sign>"]}, "P0:target-sleepsoundcoach-****************************************************************-:Debug:GenerateAssetSymbols /Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Assets.xcassets": {"tool": "shell", "description": "GenerateAssetSymbols /Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Assets.xcassets", "inputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Assets.xcassets/", "<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/GeneratedAssetSymbols.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/GeneratedAssetSymbols.h", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Assets.xcassets", "--compile", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_dependencies", "--output-partial-info-plist", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_generated_info.plist", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "26.0", "--platform", "iphonesimulator", "--bundle-identifier", "com.codepoint.sleepsoundcoach", "--generate-swift-asset-symbols", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/GeneratedAssetSymbols.swift", "--generate-objc-asset-symbols", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/GeneratedAssetSymbols.h", "--generate-asset-symbol-index", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "env": {}, "working-directory": "/Volumes/Untitled/DeskStretch/sleepsoundcoach", "control-enabled": false, "signature": "fa8cc744b0192cf37585f10171054a44"}, "P0:target-sleepsoundcoach-****************************************************************-:Debug:LinkAssetCatalog /Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Assets.xcassets": {"tool": "link-assetcatalog", "description": "LinkAssetCatalog /Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Assets.xcassets", "inputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Assets.xcassets/", "<MkDir /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_output/thinned/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_output/unthinned/", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_signature", "<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_generated_info.plist", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/Assets.car"], "deps": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_dependencies"}, "P0:target-sleepsoundcoach-****************************************************************-:Debug:LinkAssetCatalogSignature": {"tool": "link-assetcatalog", "description": "LinkAssetCatalogSignature", "inputs": ["<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_signature"], "always-out-of-date": true}, "P0:target-sleepsoundcoach-****************************************************************-:Debug:MkDir /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_output/thinned": {"tool": "mkdir", "description": "MkDir /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_output/thinned", "inputs": ["<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_output/thinned", "<MkDir /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_output/thinned>"]}, "P0:target-sleepsoundcoach-****************************************************************-:Debug:MkDir /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_output/unthinned": {"tool": "mkdir", "description": "MkDir /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_output/unthinned", "inputs": ["<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_output/unthinned", "<MkDir /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_output/unthinned>"]}, "P0:target-sleepsoundcoach-****************************************************************-:Debug:MkDir /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app": {"tool": "mkdir", "description": "MkDir /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app", "inputs": ["<target-sleepsoundcoach-****************************************************************--start>", "<target-sleepsoundcoach-****************************************************************--immediate>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app", "<MkDir /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app>", "<TRIGGER: MkDir /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app>"]}, "P0:target-sleepsoundcoach-****************************************************************-:Debug:ProcessInfoPlistFile /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/Info.plist /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/empty-sleepsoundcoach.plist": {"tool": "info-plist-processor", "description": "ProcessInfoPlistFile /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/Info.plist /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/empty-sleepsoundcoach.plist", "inputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/empty-sleepsoundcoach.plist", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/assetcatalog_generated_info.plist", "<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--entry>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/Info.plist", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/PkgInfo"]}, "P0:target-sleepsoundcoach-****************************************************************-:Debug:ProcessProductPackaging  /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.app-Simulated.xcent": {"tool": "process-product-entitlements", "description": "ProcessProductPackaging  /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.app-Simulated.xcent", "inputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/Entitlements-Simulated.plist", "<target-sleepsoundcoach-****************************************************************--ProductStructureTaskProducer>", "<target-sleepsoundcoach-****************************************************************--immediate>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.app-Simulated.xcent"]}, "P0:target-sleepsoundcoach-****************************************************************-:Debug:ProcessProductPackagingDER /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.app-Simulated.xcent /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.app-Simulated.xcent.der": {"tool": "shell", "description": "ProcessProductPackagingDER /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.app-Simulated.xcent /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.app-Simulated.xcent.der", "inputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.app-Simulated.xcent", "<target-sleepsoundcoach-****************************************************************--ProductStructureTaskProducer>", "<target-sleepsoundcoach-****************************************************************--immediate>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.app-Simulated.xcent.der"], "args": ["/usr/bin/derq", "query", "-f", "xml", "-i", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.app-Simulated.xcent", "-o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.app-Simulated.xcent.der", "--raw"], "env": {}, "working-directory": "/Volumes/Untitled/DeskStretch/sleepsoundcoach", "signature": "856cea2e9dd494280e62c40745d51350"}, "P0:target-sleepsoundcoach-****************************************************************-:Debug:RegisterExecutionPolicyException /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app": {"tool": "register-execution-policy-exception", "description": "RegisterExecutionPolicyException /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app", "inputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app", "<target-sleepsoundcoach-****************************************************************--Barrier-CodeSign>", "<target-sleepsoundcoach-****************************************************************--will-sign>", "<target-sleepsoundcoach-****************************************************************--entry>"], "outputs": ["<RegisterExecutionPolicyException /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app>"]}, "P0:target-sleepsoundcoach-****************************************************************-:Debug:SwiftDriver Compilation sleepsoundcoach normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation", "description": "SwiftDriver Compilation sleepsoundcoach normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Models/InsightData.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Models/JournalEntry.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Models/SleepSession.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Models/SoundEvent.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Models/UserPreferences.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Services/HealthKitService.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Services/InsightsService.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Services/SoundAnalysisService.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Services/SpeechRecognitionService.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Services/SubscriptionService.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Views/Coaching/CoachingView.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Views/Insights/InsightsView.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Views/Onboarding/OnboardingView.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Views/Reports/MorningReportView.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Views/Tracking/SleepTrackingView.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Views/MainTabView.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/ContentView.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Item.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/sleepsoundcoachApp.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/GeneratedAssetSymbols.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.SwiftFileList", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach-OutputFileMap.json", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach_const_extract_protocols.json", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-generated-files.hmap", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-own-target-headers.hmap", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-all-target-headers.hmap", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-project-headers.hmap", "<ClangStatCache /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/SDKStatCaches.noindex/iphonesimulator26.0-23A339-26257891a6c027eb51374368541b8346.sdkstatcache>", "<target-sleepsoundcoach-****************************************************************--generated-headers>", "<target-sleepsoundcoach-****************************************************************--copy-headers-completion>", "<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach Swift Compilation Finished", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightData.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/JournalEntry.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SleepSession.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SoundEvent.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/UserPreferences.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/HealthKitService.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightsService.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SoundAnalysisService.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SpeechRecognitionService.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SubscriptionService.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/CoachingView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightsView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/OnboardingView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/MorningReportView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SleepTrackingView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/MainTabView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/ContentView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/Item.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoachApp.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightData.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/JournalEntry.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SleepSession.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SoundEvent.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/UserPreferences.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/HealthKitService.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightsService.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SoundAnalysisService.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SpeechRecognitionService.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SubscriptionService.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/CoachingView.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightsView.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/OnboardingView.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/MorningReportView.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SleepTrackingView.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/MainTabView.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/ContentView.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/Item.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoachApp.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightData.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/JournalEntry.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SleepSession.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SoundEvent.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/UserPreferences.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/HealthKitService.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightsService.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SoundAnalysisService.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SpeechRecognitionService.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SubscriptionService.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/CoachingView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightsView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/OnboardingView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/MorningReportView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SleepTrackingView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/MainTabView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/Item.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoachApp.swiftconstvalues", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues"]}, "P0:target-sleepsoundcoach-****************************************************************-:Debug:Touch /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app": {"tool": "shell", "description": "Touch /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app", "inputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app", "<target-sleepsoundcoach-****************************************************************--Barrier-Validate>", "<target-sleepsoundcoach-****************************************************************--will-sign>", "<target-sleepsoundcoach-****************************************************************--entry>"], "outputs": ["<Touch /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app>"], "args": ["/usr/bin/touch", "-c", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app"], "env": {}, "working-directory": "/Volumes/Untitled/DeskStretch/sleepsoundcoach", "signature": "e9a51961ef257253f64dfd88105c374a"}, "P0:target-sleepsoundcoach-****************************************************************-:Debug:Validate /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app": {"tool": "validate-product", "description": "Validate /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app", "inputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/Info.plist", "<target-sleepsoundcoach-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-sleepsoundcoach-****************************************************************--will-sign>", "<target-sleepsoundcoach-****************************************************************--entry>", "<TRIGGER: CodeSign /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app>"], "outputs": ["<Validate /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app>"]}, "P2:::WriteAuxiliaryFile /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach-786d278e9b353e596f159d5bf8b2935c-VFS-iphonesimulator/all-product-headers.yaml": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach-786d278e9b353e596f159d5bf8b2935c-VFS-iphonesimulator/all-product-headers.yaml", "inputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach-786d278e9b353e596f159d5bf8b2935c-VFS-iphonesimulator/all-product-headers.yaml"]}, "P2:target-sleepsoundcoach-****************************************************************-:Debug:ConstructStubExecutorLinkFileList /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-ExecutorLinkFileList-normal-arm64.txt": {"tool": "construct-stub-executor-input-file-list", "description": "ConstructStubExecutorLinkFileList /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-ExecutorLinkFileList-normal-arm64.txt", "inputs": ["/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/lib/libPreviewsJITStubExecutor_no_swift_entry_point.a", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/lib/libPreviewsJITStubExecutor.a", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach.debug.dylib", "<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--begin-linking>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-ExecutorLinkFileList-normal-arm64.txt"]}, "P2:target-sleepsoundcoach-****************************************************************-:Debug:Copy /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.swiftsourceinfo": {"tool": "file-copy", "description": "Copy /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.swiftsourceinfo", "inputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.swiftsourceinfo/", "<target-sleepsoundcoach-****************************************************************--copy-headers-completion>", "<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo"]}, "P2:target-sleepsoundcoach-****************************************************************-:Debug:Copy /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.swiftmodule/arm64-apple-ios-simulator.abi.json /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.abi.json": {"tool": "file-copy", "description": "Copy /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.swiftmodule/arm64-apple-ios-simulator.abi.json /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.abi.json", "inputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.abi.json/", "<target-sleepsoundcoach-****************************************************************--copy-headers-completion>", "<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.swiftmodule/arm64-apple-ios-simulator.abi.json"]}, "P2:target-sleepsoundcoach-****************************************************************-:Debug:Copy /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.swiftmodule/arm64-apple-ios-simulator.swiftdoc /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.swiftdoc": {"tool": "file-copy", "description": "Copy /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.swiftmodule/arm64-apple-ios-simulator.swiftdoc /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.swiftdoc", "inputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.swiftdoc/", "<target-sleepsoundcoach-****************************************************************--copy-headers-completion>", "<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.swiftmodule/arm64-apple-ios-simulator.swiftdoc"]}, "P2:target-sleepsoundcoach-****************************************************************-:Debug:Copy /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.swiftmodule": {"tool": "file-copy", "description": "Copy /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.swiftmodule", "inputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.swiftmodule/", "<target-sleepsoundcoach-****************************************************************--copy-headers-completion>", "<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.swiftmodule/arm64-apple-ios-simulator.swiftmodule"]}, "P2:target-sleepsoundcoach-****************************************************************-:Debug:Ld /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/__preview.dylib normal": {"tool": "shell", "description": "Ld /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/__preview.dylib normal", "inputs": ["<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--begin-linking>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/__preview.dylib", "<Linked Binary Preview Injection Dylib /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/__preview.dylib>", "<TRIGGER: Ld /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/__preview.dylib normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-ios26.0-simulator", "-dynamiclib", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator26.0.sdk", "-O0", "-L/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator", "-F/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator", "-install_name", "@rpath/sleepsoundcoach.debug.dylib", "-dead_strip", "-rdynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-objc_abi_version", "-<PERSON><PERSON><PERSON>", "2", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach_dependency_info.dat", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__entitlements", "-<PERSON><PERSON><PERSON>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.app-Simulated.xcent", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__ents_der", "-<PERSON><PERSON><PERSON>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.app-Simulated.xcent.der", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/__preview.dylib"], "env": {"PATH": "/Applications/Xcode.app/Contents/SharedFrameworks/SwiftBuild.framework/Versions/A/PlugIns/SWBBuildService.bundle/Contents/PlugIns/SWBUniversalPlatformPlugin.bundle/Contents/Frameworks/SWBUniversalPlatform.framework/Resources:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/libexec:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/local/bin:/Applications/Xcode.app/Contents/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/usr/local/bin:/opt/homebrew/Cellar/dart/3.2.3/libexec/bin/:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin://Applications/Topaz Photo AI.app/Contents/Resources/bin:/Library/Apple/usr/bin:/Users/<USER>/.deno/bin:/Users/<USER>/.codeium/windsurf/bin:/opt/homebrew/opt/libpq/bin:/usr/local/gradle/gradle-8.5/bin:/opt/homebrew/opt/openjdk@17/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/opt/homebrew/opt/openjdk/bin:/Users/<USER>/Documents/flutter/bin:/Users/<USER>/Library/Android/sdk/platform-tools"}, "working-directory": "/Volumes/Untitled/DeskStretch/sleepsoundcoach", "signature": "2f8d49667453371bb8a53d7178ae510a"}, "P2:target-sleepsoundcoach-****************************************************************-:Debug:Ld /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach normal": {"tool": "shell", "description": "Ld /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach normal", "inputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach.debug.dylib", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-DebugDylibPath-normal-arm64.txt", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-DebugDylibInstallName-normal-arm64.txt", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-ExecutorLinkFileList-normal-arm64.txt", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.app-Simulated.xcent", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.app-Simulated.xcent.der", "<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--begin-linking>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach", "<Linked Binary /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach>", "<TRIGGER: Ld /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-ios26.0-simulator", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator26.0.sdk", "-O0", "-L/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator", "-F/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/Frameworks", "-rdynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-objc_abi_version", "-<PERSON><PERSON><PERSON>", "2", "-e", "___debug_blank_executor_main", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__debug_dylib", "-<PERSON><PERSON><PERSON>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-DebugDylibPath-normal-arm64.txt", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__debug_instlnm", "-<PERSON><PERSON><PERSON>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-DebugDylibInstallName-normal-arm64.txt", "-<PERSON><PERSON><PERSON>", "-filelist", "-<PERSON><PERSON><PERSON>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-ExecutorLinkFileList-normal-arm64.txt", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__entitlements", "-<PERSON><PERSON><PERSON>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.app-Simulated.xcent", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__ents_der", "-<PERSON><PERSON><PERSON>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.app-Simulated.xcent.der", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach.debug.dylib", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach"], "env": {"PATH": "/Applications/Xcode.app/Contents/SharedFrameworks/SwiftBuild.framework/Versions/A/PlugIns/SWBBuildService.bundle/Contents/PlugIns/SWBUniversalPlatformPlugin.bundle/Contents/Frameworks/SWBUniversalPlatform.framework/Resources:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/libexec:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/local/bin:/Applications/Xcode.app/Contents/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/usr/local/bin:/opt/homebrew/Cellar/dart/3.2.3/libexec/bin/:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin://Applications/Topaz Photo AI.app/Contents/Resources/bin:/Library/Apple/usr/bin:/Users/<USER>/.deno/bin:/Users/<USER>/.codeium/windsurf/bin:/opt/homebrew/opt/libpq/bin:/usr/local/gradle/gradle-8.5/bin:/opt/homebrew/opt/openjdk@17/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/opt/homebrew/opt/openjdk/bin:/Users/<USER>/Documents/flutter/bin:/Users/<USER>/Library/Android/sdk/platform-tools"}, "working-directory": "/Volumes/Untitled/DeskStretch/sleepsoundcoach", "signature": "618f45a619ffe8ec97bc89454518e788"}, "P2:target-sleepsoundcoach-****************************************************************-:Debug:Ld /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach.debug.dylib normal": {"tool": "shell", "description": "Ld /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach.debug.dylib normal", "inputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightData.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/JournalEntry.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SleepSession.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SoundEvent.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/UserPreferences.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/HealthKitService.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightsService.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SoundAnalysisService.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SpeechRecognitionService.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SubscriptionService.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/CoachingView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/InsightsView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/OnboardingView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/MorningReportView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/SleepTrackingView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/MainTabView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/ContentView.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/Item.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoachApp.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.LinkFileList", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.app-Simulated.xcent", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.app-Simulated.xcent.der", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator", "<target-sleepsoundcoach-****************************************************************--generated-headers>", "<target-sleepsoundcoach-****************************************************************--swift-generated-headers>", "<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--begin-linking>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach.debug.dylib", "<Linked Binary Debug Dylib /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach.debug.dylib>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach_lto.o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach_dependency_info.dat", "<TRIGGER: Ld /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach.debug.dylib normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-ios26.0-simulator", "-dynamiclib", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator26.0.sdk", "-O0", "-L/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "-L/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator", "-F/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "-F/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator", "-filelist", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.LinkFileList", "-install_name", "@rpath/sleepsoundcoach.debug.dylib", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/Frameworks", "-dead_strip", "-<PERSON><PERSON><PERSON>", "-object_path_lto", "-<PERSON><PERSON><PERSON>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach_lto.o", "-rdynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-objc_abi_version", "-<PERSON><PERSON><PERSON>", "2", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach_dependency_info.dat", "-fobjc-link-runtime", "-L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator", "-L/usr/lib/swift", "-<PERSON><PERSON><PERSON>", "-add_ast_path", "-<PERSON><PERSON><PERSON>", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.swiftmodule", "@/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach-linker-args.resp", "-<PERSON><PERSON><PERSON>", "-alias", "-<PERSON><PERSON><PERSON>", "_main", "-<PERSON><PERSON><PERSON>", "___debug_main_executable_dylib_entry_point", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-o", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Products/Debug-iphonesimulator/sleepsoundcoach.app/sleepsoundcoach.debug.dylib"], "env": {"PATH": "/Applications/Xcode.app/Contents/SharedFrameworks/SwiftBuild.framework/Versions/A/PlugIns/SWBBuildService.bundle/Contents/PlugIns/SWBUniversalPlatformPlugin.bundle/Contents/Frameworks/SWBUniversalPlatform.framework/Resources:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/libexec:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/local/bin:/Applications/Xcode.app/Contents/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/usr/local/bin:/opt/homebrew/Cellar/dart/3.2.3/libexec/bin/:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin://Applications/Topaz Photo AI.app/Contents/Resources/bin:/Library/Apple/usr/bin:/Users/<USER>/.deno/bin:/Users/<USER>/.codeium/windsurf/bin:/opt/homebrew/opt/libpq/bin:/usr/local/gradle/gradle-8.5/bin:/opt/homebrew/opt/openjdk@17/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/opt/homebrew/opt/openjdk/bin:/Users/<USER>/Documents/flutter/bin:/Users/<USER>/Library/Android/sdk/platform-tools"}, "working-directory": "/Volumes/Untitled/DeskStretch/sleepsoundcoach", "deps": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach_dependency_info.dat"], "deps-style": "dependency-info", "signature": "85f26d13a8a88c34c14942d58d95ca55"}, "P2:target-sleepsoundcoach-****************************************************************-:Debug:SwiftDriver Compilation Requirements sleepsoundcoach normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation-requirement", "description": "SwiftDriver Compilation Requirements sleepsoundcoach normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Models/InsightData.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Models/JournalEntry.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Models/SleepSession.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Models/SoundEvent.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Models/UserPreferences.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Services/HealthKitService.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Services/InsightsService.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Services/SoundAnalysisService.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Services/SpeechRecognitionService.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Services/SubscriptionService.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Views/Coaching/CoachingView.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Views/Insights/InsightsView.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Views/Onboarding/OnboardingView.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Views/Reports/MorningReportView.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Views/Tracking/SleepTrackingView.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Views/MainTabView.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/ContentView.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/Item.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach/sleepsoundcoachApp.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/GeneratedAssetSymbols.swift", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.SwiftFileList", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach-OutputFileMap.json", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach_const_extract_protocols.json", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-generated-files.hmap", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-own-target-headers.hmap", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-all-target-headers.hmap", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-project-headers.hmap", "<ClangStatCache /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/SDKStatCaches.noindex/iphonesimulator26.0-23A339-26257891a6c027eb51374368541b8346.sdkstatcache>", "<target-sleepsoundcoach-****************************************************************--copy-headers-completion>", "<target-sleepsoundcoach-****************************************************************--ModuleVerifierTaskProducer>", "<target-sleepsoundcoach-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach Swift Compilation Requirements Finished", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.swiftmodule", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach-linker-args.resp", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.swiftsourceinfo", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.abi.json", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach-Swift.h", "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.swiftdoc"]}, "P2:target-sleepsoundcoach-****************************************************************-:Debug:SwiftMergeGeneratedHeaders /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/sleepsoundcoach-Swift.h /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach-Swift.h": {"tool": "swift-header-tool", "description": "SwiftMergeGeneratedHeaders /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/sleepsoundcoach-Swift.h /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach-Swift.h", "inputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach-Swift.h", "<target-sleepsoundcoach-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/sleepsoundcoach-Swift.h"]}, "P2:target-sleepsoundcoach-****************************************************************-:Debug:WriteAuxiliaryFile /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/Entitlements-Simulated.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/Entitlements-Simulated.plist", "inputs": ["<target-sleepsoundcoach-****************************************************************--immediate>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/DerivedSources/Entitlements-Simulated.plist"]}, "P2:target-sleepsoundcoach-****************************************************************-:Debug:WriteAuxiliaryFile /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach-OutputFileMap.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach-OutputFileMap.json", "inputs": ["<target-sleepsoundcoach-****************************************************************--immediate>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach-OutputFileMap.json"]}, "P2:target-sleepsoundcoach-****************************************************************-:Debug:WriteAuxiliaryFile /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.LinkFileList", "inputs": ["<target-sleepsoundcoach-****************************************************************--immediate>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.LinkFileList"]}, "P2:target-sleepsoundcoach-****************************************************************-:Debug:WriteAuxiliaryFile /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.SwiftConstValuesFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.SwiftConstValuesFileList", "inputs": ["<target-sleepsoundcoach-****************************************************************--immediate>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.SwiftConstValuesFileList"]}, "P2:target-sleepsoundcoach-****************************************************************-:Debug:WriteAuxiliaryFile /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.SwiftFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.SwiftFileList", "inputs": ["<target-sleepsoundcoach-****************************************************************--immediate>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach.SwiftFileList"]}, "P2:target-sleepsoundcoach-****************************************************************-:Debug:WriteAuxiliaryFile /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach_const_extract_protocols.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach_const_extract_protocols.json", "inputs": ["<target-sleepsoundcoach-****************************************************************--immediate>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/Objects-normal/arm64/sleepsoundcoach_const_extract_protocols.json"]}, "P2:target-sleepsoundcoach-****************************************************************-:Debug:WriteAuxiliaryFile /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/empty-sleepsoundcoach.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/empty-sleepsoundcoach.plist", "inputs": ["<target-sleepsoundcoach-****************************************************************--immediate>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/empty-sleepsoundcoach.plist"]}, "P2:target-sleepsoundcoach-****************************************************************-:Debug:WriteAuxiliaryFile /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-DebugDylibInstallName-normal-arm64.txt": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-DebugDylibInstallName-normal-arm64.txt", "inputs": ["<target-sleepsoundcoach-****************************************************************--immediate>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-DebugDylibInstallName-normal-arm64.txt"]}, "P2:target-sleepsoundcoach-****************************************************************-:Debug:WriteAuxiliaryFile /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-DebugDylibPath-normal-arm64.txt": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-DebugDylibPath-normal-arm64.txt", "inputs": ["<target-sleepsoundcoach-****************************************************************--immediate>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-DebugDylibPath-normal-arm64.txt"]}, "P2:target-sleepsoundcoach-****************************************************************-:Debug:WriteAuxiliaryFile /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-all-non-framework-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-all-non-framework-target-headers.hmap", "inputs": ["<target-sleepsoundcoach-****************************************************************--immediate>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-all-non-framework-target-headers.hmap"]}, "P2:target-sleepsoundcoach-****************************************************************-:Debug:WriteAuxiliaryFile /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-all-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-all-target-headers.hmap", "inputs": ["<target-sleepsoundcoach-****************************************************************--immediate>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-all-target-headers.hmap"]}, "P2:target-sleepsoundcoach-****************************************************************-:Debug:WriteAuxiliaryFile /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-generated-files.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-generated-files.hmap", "inputs": ["<target-sleepsoundcoach-****************************************************************--immediate>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-generated-files.hmap"]}, "P2:target-sleepsoundcoach-****************************************************************-:Debug:WriteAuxiliaryFile /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-own-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-own-target-headers.hmap", "inputs": ["<target-sleepsoundcoach-****************************************************************--immediate>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-own-target-headers.hmap"]}, "P2:target-sleepsoundcoach-****************************************************************-:Debug:WriteAuxiliaryFile /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-project-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-project-headers.hmap", "inputs": ["<target-sleepsoundcoach-****************************************************************--immediate>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach-project-headers.hmap"]}, "P2:target-sleepsoundcoach-****************************************************************-:Debug:WriteAuxiliaryFile /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.DependencyMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.DependencyMetadataFileList", "inputs": ["<target-sleepsoundcoach-****************************************************************--immediate>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.DependencyMetadataFileList"]}, "P2:target-sleepsoundcoach-****************************************************************-:Debug:WriteAuxiliaryFile /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.DependencyStaticMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.DependencyStaticMetadataFileList", "inputs": ["<target-sleepsoundcoach-****************************************************************--immediate>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.DependencyStaticMetadataFileList"]}, "P2:target-sleepsoundcoach-****************************************************************-:Debug:WriteAuxiliaryFile /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.hmap", "inputs": ["<target-sleepsoundcoach-****************************************************************--immediate>"], "outputs": ["/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/Build/Intermediates.noindex/sleepsoundcoach.build/Debug-iphonesimulator/sleepsoundcoach.build/sleepsoundcoach.hmap"]}}}