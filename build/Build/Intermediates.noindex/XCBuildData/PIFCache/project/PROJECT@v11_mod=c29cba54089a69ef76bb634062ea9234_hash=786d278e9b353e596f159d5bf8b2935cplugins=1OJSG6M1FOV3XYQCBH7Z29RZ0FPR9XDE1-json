{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "DEVELOPMENT_TEAM": "RPJ5ZSN3Z7", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "26.0", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "SDKROOT": "iphoneos", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG $(inherited)", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "786d278e9b353e596f159d5bf8b2935c53db8eec3ad1d43e159b00a739e206d6", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEVELOPMENT_TEAM": "RPJ5ZSN3Z7", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "26.0", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "SDKROOT": "iphoneos", "SWIFT_COMPILATION_MODE": "wholemodule", "VALIDATE_PRODUCT": "YES"}, "guid": "786d278e9b353e596f159d5bf8b2935cc0df482c51c73a93a4021bd77567eca9", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "786d278e9b353e596f159d5bf8b2935c0b61d638a0a771ecf98fcae7b32054ee", "path": "InsightData.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "786d278e9b353e596f159d5bf8b2935ccfb45514a463314cc9fcdf853341508a", "path": "JournalEntry.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "786d278e9b353e596f159d5bf8b2935ccf0b638a99219f4861249c13f66bdabb", "path": "SleepSession.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "786d278e9b353e596f159d5bf8b2935c02b8d1c39791f5e090f4b3a955edce77", "path": "SoundEvent.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "786d278e9b353e596f159d5bf8b2935caa74ea645a7f0791cbc165a2ee2344d6", "path": "UserPreferences.swift", "sourceTree": "<group>", "type": "file"}], "guid": "786d278e9b353e596f159d5bf8b2935c6d9cbffc7b0c8f5a61dbb924f4242553", "name": "Models", "path": "Models", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "786d278e9b353e596f159d5bf8b2935c5ac8aab83e4bcfd834a4c7a3c14f070d", "path": "HealthKitService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "786d278e9b353e596f159d5bf8b2935c19e0511f602eddce52d29891980a8807", "path": "InsightsService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "786d278e9b353e596f159d5bf8b2935c93ce46693d4497f133a2b5282fc8de03", "path": "SoundAnalysisService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "786d278e9b353e596f159d5bf8b2935c39031cd05c77383b938c5891f7fe724b", "path": "SpeechRecognitionService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "786d278e9b353e596f159d5bf8b2935c863c4e764bda6efba52cf1af6ae2e79b", "path": "SubscriptionService.swift", "sourceTree": "<group>", "type": "file"}], "guid": "786d278e9b353e596f159d5bf8b2935cd3e4d3597bdd97a0640536612900d440", "name": "Services", "path": "Services", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "786d278e9b353e596f159d5bf8b2935c9e310ed1ec5bd84847d4925a08c99943", "path": "CoachingView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "786d278e9b353e596f159d5bf8b2935cd8defc2ecf395340a9b4c75cb572e2e9", "name": "Coaching", "path": "Coaching", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "786d278e9b353e596f159d5bf8b2935c74102bf5ae468acacf842783fe4829cd", "path": "InsightsView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "786d278e9b353e596f159d5bf8b2935c3677c8cc5aebbcda1e1fbd0455f7a3f9", "name": "Insights", "path": "Insights", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "786d278e9b353e596f159d5bf8b2935c7db9c62979365750cb732c472e0b763e", "path": "OnboardingView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "786d278e9b353e596f159d5bf8b2935c5b651ab24e4a8aa52ea9fc83f809c333", "name": "Onboarding", "path": "Onboarding", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "786d278e9b353e596f159d5bf8b2935c36e57372fc8b32359662fb6dc386849d", "path": "MorningReportView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "786d278e9b353e596f159d5bf8b2935c424aa2892b6f5a8a86c9929f7f28bf0e", "name": "Reports", "path": "Reports", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "786d278e9b353e596f159d5bf8b2935c9d747b7c7376cc7e41a216bf2a842514", "path": "SleepTrackingView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "786d278e9b353e596f159d5bf8b2935c6711a212f4cd781c501a2a8a705847f6", "name": "Tracking", "path": "Tracking", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "786d278e9b353e596f159d5bf8b2935c9cd07cf240bbb9e78f90f907b8c94f70", "path": "MainTabView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "786d278e9b353e596f159d5bf8b2935c96fd30884ea97b59ac0fc5da306d06fc", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}, {"fileType": "folder.assetcatalog", "guid": "786d278e9b353e596f159d5bf8b2935ceb12594141a98e3c7f785cb300008f5b", "path": "Assets.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "786d278e9b353e596f159d5bf8b2935ccf3d1933f5478736a178c3c050021e1c", "path": "ContentView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "786d278e9b353e596f159d5bf8b2935c0460b9196f840199511fab1096742999", "path": "<PERSON><PERSON>.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.entitlements", "guid": "786d278e9b353e596f159d5bf8b2935c39b005e8b020071a18e91f2794a1b81d", "path": "sleepsoundcoach.entitlements", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "786d278e9b353e596f159d5bf8b2935c3827fc5017b99bb1745531d18d95f63a", "path": "sleepsoundcoachApp.swift", "sourceTree": "<group>", "type": "file"}], "guid": "786d278e9b353e596f159d5bf8b2935c9f4b0e200159352dde0aef6a258c5557", "name": "sleepsoundcoach", "path": "sleepsoundcoach", "sourceTree": "<group>", "type": "group"}, {"guid": "786d278e9b353e596f159d5bf8b2935cf90de8164ab7748be2cb5883d2e586f1", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "786d278e9b353e596f159d5bf8b2935c81a771daaff47c163bbe1074c08c2d33", "name": "sleepsoundcoach", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "786d278e9b353e596f159d5bf8b2935c", "path": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/sleepsoundcoach.xcodeproj", "projectDirectory": "/Volumes/Untitled/DeskStretch/sleepsoundcoach", "targets": ["TARGET@v11_hash=1a28c957d46e70ee11503c680c987631"]}