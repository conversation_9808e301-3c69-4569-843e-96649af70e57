{"buildConfigurations": [{"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_TEAM": "RPJ5ZSN3Z7", "ENABLE_PREVIEWS": "YES", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_KEY_UIApplicationSceneManifest_Generation": "YES", "INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents": "YES", "INFOPLIST_KEY_UILaunchScreen_Generation": "YES", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad": "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone": "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.codepoint.sleepsoundcoach", "PRODUCT_NAME": "$(TARGET_NAME)", "STRING_CATALOG_GENERATE_SYMBOLS": "YES", "SWIFT_APPROACHABLE_CONCURRENCY": "YES", "SWIFT_DEFAULT_ACTOR_ISOLATION": "MainActor", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "786d278e9b353e596f159d5bf8b2935ccc6a6c5584b63caf67260969bb5f8712", "name": "Debug"}, {"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_TEAM": "RPJ5ZSN3Z7", "ENABLE_PREVIEWS": "YES", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_KEY_UIApplicationSceneManifest_Generation": "YES", "INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents": "YES", "INFOPLIST_KEY_UILaunchScreen_Generation": "YES", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad": "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone": "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.codepoint.sleepsoundcoach", "PRODUCT_NAME": "$(TARGET_NAME)", "STRING_CATALOG_GENERATE_SYMBOLS": "YES", "SWIFT_APPROACHABLE_CONCURRENCY": "YES", "SWIFT_DEFAULT_ACTOR_ISOLATION": "MainActor", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "786d278e9b353e596f159d5bf8b2935cbcf9833710503b57ad20cb9338261675", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "786d278e9b353e596f159d5bf8b2935c0b61d638a0a771ecf98fcae7b32054ee", "guid": "786d278e9b353e596f159d5bf8b2935c9a5aec53d4a98042f23659687332dd67"}, {"fileReference": "786d278e9b353e596f159d5bf8b2935ccfb45514a463314cc9fcdf853341508a", "guid": "786d278e9b353e596f159d5bf8b2935cd5478bec53e2a8531541e37c3f4eba8e"}, {"fileReference": "786d278e9b353e596f159d5bf8b2935ccf0b638a99219f4861249c13f66bdabb", "guid": "786d278e9b353e596f159d5bf8b2935ce8ac6cb2be1f0e44cd90443f4d402af0"}, {"fileReference": "786d278e9b353e596f159d5bf8b2935c02b8d1c39791f5e090f4b3a955edce77", "guid": "786d278e9b353e596f159d5bf8b2935c987f8ff957cd2ed9b6e9a5f9bfbf4545"}, {"fileReference": "786d278e9b353e596f159d5bf8b2935caa74ea645a7f0791cbc165a2ee2344d6", "guid": "786d278e9b353e596f159d5bf8b2935cf9ea5a5ce5b4c71f40055dd2de84373a"}, {"fileReference": "786d278e9b353e596f159d5bf8b2935c5ac8aab83e4bcfd834a4c7a3c14f070d", "guid": "786d278e9b353e596f159d5bf8b2935c20ec22ce751aefdbb5dab6575f3c542b"}, {"fileReference": "786d278e9b353e596f159d5bf8b2935c19e0511f602eddce52d29891980a8807", "guid": "786d278e9b353e596f159d5bf8b2935cebfdbc0363299de271cf27021788448c"}, {"fileReference": "786d278e9b353e596f159d5bf8b2935c93ce46693d4497f133a2b5282fc8de03", "guid": "786d278e9b353e596f159d5bf8b2935c477e132cf80d40ebdf778030129b746b"}, {"fileReference": "786d278e9b353e596f159d5bf8b2935c39031cd05c77383b938c5891f7fe724b", "guid": "786d278e9b353e596f159d5bf8b2935c533a27484f210757e90eee13a6654c2f"}, {"fileReference": "786d278e9b353e596f159d5bf8b2935c863c4e764bda6efba52cf1af6ae2e79b", "guid": "786d278e9b353e596f159d5bf8b2935c19e38e21e4089de9fb2889d7481410d0"}, {"fileReference": "786d278e9b353e596f159d5bf8b2935c9e310ed1ec5bd84847d4925a08c99943", "guid": "786d278e9b353e596f159d5bf8b2935cb00127a743f18d239c1ee0291d01cd62"}, {"fileReference": "786d278e9b353e596f159d5bf8b2935c74102bf5ae468acacf842783fe4829cd", "guid": "786d278e9b353e596f159d5bf8b2935c0fe52af77d0e170bab0839e5a79df88c"}, {"fileReference": "786d278e9b353e596f159d5bf8b2935c7db9c62979365750cb732c472e0b763e", "guid": "786d278e9b353e596f159d5bf8b2935c29acce265a7b06bc5bc61cf4b02dee1f"}, {"fileReference": "786d278e9b353e596f159d5bf8b2935c36e57372fc8b32359662fb6dc386849d", "guid": "786d278e9b353e596f159d5bf8b2935c4f321a84e5a9b945a65fd1b6ff7c311d"}, {"fileReference": "786d278e9b353e596f159d5bf8b2935c9d747b7c7376cc7e41a216bf2a842514", "guid": "786d278e9b353e596f159d5bf8b2935c88e1bea03d3c5834067cc9e3d0bf93ca"}, {"fileReference": "786d278e9b353e596f159d5bf8b2935c9cd07cf240bbb9e78f90f907b8c94f70", "guid": "786d278e9b353e596f159d5bf8b2935c61f6719093b9354433ad0648e637fc81"}, {"fileReference": "786d278e9b353e596f159d5bf8b2935ccf3d1933f5478736a178c3c050021e1c", "guid": "786d278e9b353e596f159d5bf8b2935ca30cdb9aebf2c402877647e5205d9559"}, {"fileReference": "786d278e9b353e596f159d5bf8b2935c0460b9196f840199511fab1096742999", "guid": "786d278e9b353e596f159d5bf8b2935c67fb7e79405ea5f196e00b9f249a983c"}, {"fileReference": "786d278e9b353e596f159d5bf8b2935c3827fc5017b99bb1745531d18d95f63a", "guid": "786d278e9b353e596f159d5bf8b2935c5e6c2e6ebcbb15e60821c574fd1ab263"}], "guid": "786d278e9b353e596f159d5bf8b2935c36f840bf8677e12af9b74cbc1aa02d1e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "786d278e9b353e596f159d5bf8b2935c73e452e934a4be58b48274c532957829", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "786d278e9b353e596f159d5bf8b2935ceb12594141a98e3c7f785cb300008f5b", "guid": "786d278e9b353e596f159d5bf8b2935cd98da5694501ddb6db9589c98bc23d34"}], "guid": "786d278e9b353e596f159d5bf8b2935c2f39417e3f188c1bbf1a4d937145eda7", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "786d278e9b353e596f159d5bf8b2935cdf19a901a6405ca310256e18e9734334", "name": "sleepsoundcoach", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "786d278e9b353e596f159d5bf8b2935c054504e7a94ad59cf34013cf717739f9", "name": "sleepsoundcoach.app", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.application", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}