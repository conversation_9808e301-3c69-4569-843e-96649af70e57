[{"isFramework": true, "moduleName": "AVFAudio", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/AVFAudio.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": false, "moduleName": "AVFoundation", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/AVFoundation.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": true, "moduleName": "Accessibility", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/Accessibility.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": true, "moduleName": "AudioToolbox", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/AudioToolbox.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": true, "moduleName": "BackgroundAssets", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/BackgroundAssets.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": true, "moduleName": "Charts", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/Charts.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": true, "moduleName": "Combine", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/Combine.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": false, "moduleName": "CoreAudio", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/CoreAudio.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": true, "moduleName": "CoreData", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/CoreData.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": false, "moduleName": "CoreFoundation", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/CoreFoundation.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": true, "moduleName": "CoreGraphics", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/CoreGraphics.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": false, "moduleName": "CoreImage", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/CoreImage.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": false, "moduleName": "CoreLocation", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/CoreLocation.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": false, "moduleName": "CoreMIDI", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/CoreMIDI.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": true, "moduleName": "CoreML", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/CoreML.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": false, "moduleName": "CoreMedia", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/CoreMedia.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": true, "moduleName": "CoreText", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/CoreText.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": true, "moduleName": "CoreTransferable", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/CoreTransferable.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": true, "moduleName": "CoreVideo", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/CoreVideo.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": true, "moduleName": "CryptoKit", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/CryptoKit.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": false, "moduleName": "<PERSON>", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/Darwin.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": true, "moduleName": "DataDetection", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/DataDetection.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": true, "moduleName": "DeveloperToolsSupport", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/DeveloperToolsSupport.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": false, "moduleName": "Dispatch", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/Dispatch.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": false, "moduleName": "Distributed", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/Distributed.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": true, "moduleName": "ExtensionFoundation", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/ExtensionFoundation.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": true, "moduleName": "FileProvider", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/FileProvider.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": true, "moduleName": "Foundation", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/Foundation.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": true, "moduleName": "HealthKit", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/HealthKit.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": true, "moduleName": "LocalAuthentication", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/LocalAuthentication.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": false, "moduleName": "Metal", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/Metal.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": false, "moduleName": "NaturalLanguage", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/NaturalLanguage.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": true, "moduleName": "Network", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/Network.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": false, "moduleName": "OSLog", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/OSLog.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": false, "moduleName": "ObjectiveC", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/ObjectiveC.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": false, "moduleName": "Observation", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/Observation.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": false, "moduleName": "QuartzCore", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/QuartzCore.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": true, "moduleName": "SoundAnalysis", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/SoundAnalysis.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": false, "moduleName": "Spatial", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/Spatial.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": true, "moduleName": "Speech", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/Speech.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": true, "moduleName": "StoreKit", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/StoreKit.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": false, "moduleName": "Swift", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/Swift.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": true, "moduleName": "SwiftData", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/SwiftData.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": false, "moduleName": "SwiftOnoneSupport", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/SwiftOnoneSupport.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": true, "moduleName": "SwiftUI", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/SwiftUI.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": true, "moduleName": "SwiftUICore", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/SwiftUICore.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": true, "moduleName": "Symbols", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/Symbols.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": false, "moduleName": "Synchronization", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/Synchronization.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": false, "moduleName": "System", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/System.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": true, "moduleName": "UIKit", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/UIKit.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": false, "moduleName": "UniformTypeIdentifiers", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/UniformTypeIdentifiers.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": false, "moduleName": "XPC", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/XPC.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": false, "moduleName": "_Builtin_float", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/_Builtin_float.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": false, "moduleName": "_Concurrency", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/_Concurrency.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": false, "moduleName": "_DarwinFoundation1", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/_DarwinFoundation1.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": false, "moduleName": "_DarwinFoundation2", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/_DarwinFoundation2.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": false, "moduleName": "_DarwinFoundation3", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/_DarwinFoundation3.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": false, "moduleName": "_StringProcessing", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/_StringProcessing.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": true, "moduleName": "_SwiftData_SwiftUI", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/_SwiftData_SwiftUI.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": false, "moduleName": "os", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/os.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"isFramework": false, "moduleName": "simd", "modulePath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/simd.swiftmodule/arm64-apple-ios-simulator.swiftmodule"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/AVFAudio.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/AVFAudio-9UMI2IWOZANR943NX183LZB3D.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "AVFAudio"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/AVFoundation.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/AVFoundation-3929EQ250UPBSIVW11UMWABN.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "AVFoundation"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/AVRouting.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/AVRouting-B3769X17JNT36ZN6RR7Q3XF85.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "AVRouting"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/Accessibility.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/Accessibility-8H46GRU9OTLHX9JGHAVQM6ZIB.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "Accessibility"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/AudioToolbox.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/AudioToolbox-BGJ6R6N21I7LNK0BJP6U4DRVB.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "AudioToolbox"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/BackgroundAssets.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/BackgroundAssets-D44EEOT6J8OGCY8I2512ES9CE.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "BackgroundAssets"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/CFNetwork.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/CFNetwork-5M7A7TX4P7PKL0EKBWUNWRX2D.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "CFNetwork"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/CoreAudio.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/CoreAudio-2QHGGKBI0H57H66OTAVDRSQ25.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "CoreAudio"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/CoreAudioTypes.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/CoreAudioTypes-2YK25L3USQ47TGNUQ7DEE4048.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "CoreAudioTypes"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/CoreData.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/CoreData-5LVOXQZ3AEAULYJSIC017PV7W.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "CoreData"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/CoreFoundation.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/CoreFoundation-3AVSU0P8XRUDHW7D4QIC9Q85I.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "CoreFoundation"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/CoreGraphics.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/CoreGraphics-DNEXPW4ZU8V6EX5JXP7P8QTKA.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "CoreGraphics"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/CoreImage.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/CoreImage-3IZPUHOLR1264S1YAWDK0YVXP.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "CoreImage"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/CoreLocation.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/CoreLocation-BESZG590N3BUX0433IA4SNSEK.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "CoreLocation"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/CoreMIDI.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/CoreMIDI-DL0MC0CV3EY7HGW76751R2XY5.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "CoreMIDI"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/CoreML.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/CoreML-3ZN0DYWQ7LGOHKIXK2M2SQHV3.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "CoreML"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/CoreMedia.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/CoreMedia-AUURFD69L3OR4P0413I254Y96.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "CoreMedia"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/CoreText.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/CoreText-305EYVHL1OVA6TUFR6KSVKHW2.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "CoreText"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/CoreTransferable.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/CoreTransferable-2OQXE7D73475R9JM0TRH9LAEQ.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "CoreTransferable"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/CoreVideo.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/CoreVideo-9D2N5J3H0C434V2UFO8IND2DU.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "CoreVideo"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/usr/include/Darwin.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/Darwin-DXUPCCN8AVSLSYY457L9H0EF.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "<PERSON>"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/DataDetection.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/DataDetection-ES7MLD2NKKEPUWS0BDAY2M09N.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "DataDetection"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/DeveloperToolsSupport.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/DeveloperToolsSupport-3ZVMTLCUIS6AD2JY89944AJFO.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "DeveloperToolsSupport"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/usr/include/dispatch.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/Dispatch-3SXNPULYGEZ0ZP1ELN1UJMQW.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "Dispatch"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/ExtensionFoundation.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/ExtensionFoundation-6113WD67GGJWLXH9Y0BKXZ3TB.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "ExtensionFoundation"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/FileProvider.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/FileProvider-6QXAU9PVRD1J5DFF871RBJAH6.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "FileProvider"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/Foundation.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/Foundation-5N7UDMAVS27E631D7C2482HC3.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "Foundation"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/HealthKit.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/HealthKit-BLGWLSVCFMMSNXCPF8LH9IDKF.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "HealthKit"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/IOSurface.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/IOSurface-2KJEED9AIUMVJ2PCD4NG7G1QD.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "I<PERSON><PERSON>face"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/ImageIO.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/ImageIO-C4QCW6G1OD57QV5L2KFJJOYWU.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "ImageIO"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/LocalAuthentication.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/LocalAuthentication-1PUE2CZCR8RVETX2ONBYHNWTW.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "LocalAuthentication"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/usr/include/DarwinBasic.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/MachO-DFVVC9UX50WY4BE2RG8T22M00.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "MachO"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/MediaToolbox.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/MediaToolbox-2GR9208AWXPADQ2XW4EZJ11BS.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "MediaToolbox"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/Metal.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/Metal-DNTR7IW2JF5XSMT5PZVJA44ZQ.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "Metal"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/NaturalLanguage.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/NaturalLanguage-6JGK7W1ZF98E4J99UEBVMFB6R.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "NaturalLanguage"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/Network.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/Network-3VACTVRK770YW3LW1K300M6ZD.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "Network"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/OSLog.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/OSLog-6W7K0K7HLI5WKA8Y77HIFZUA.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "OSLog"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/usr/include/ObjectiveC.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/ObjectiveC-9Q8SBBXHAATRNRDJ11NO1O2KX.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "ObjectiveC"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/OpenGLES.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/OpenGLES-569306V30KVC98CM6LMKARPH8.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "OpenGLES"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/QuartzCore.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/QuartzCore-4H8XWDP5Q74EX0A0NH3NAHR9M.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "QuartzCore"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/Security.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/Security-CIT3ZSLQ7FZ11H3J7WJBY6DHF.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "Security"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/SoundAnalysis.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/SoundAnalysis-8J3812PXO5CISCGIXF24D7RSC.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "SoundAnalysis"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/usr/include/Spatial/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/Spatial-9KB21BVY1GIQNORYBIJONHIOB.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "Spatial"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/Speech.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/Speech-8MWMFIMO4SQOUA9HLCVUW7L3M.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "Speech"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/StoreKit.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/StoreKit-ALXO2Q90PBDZB4I8DFI61G3U1.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "StoreKit"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/usr/lib/swift/shims/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/SwiftShims-569P1BCS0IAKHCMLRQ1T3KK02.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "<PERSON><PERSON><PERSON><PERSON>"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/SwiftUI.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/SwiftUI-48N3EW0QJE7IWZCVORHK8OTYD.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "SwiftUI"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/SwiftUICore.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/SwiftUICore-33W5DCQTCK452EEGHB6WZQC53.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "SwiftUICore"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/Symbols.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/Symbols-B5VB3QYVH7F356O1PKERS7OFS.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "Symbols"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/UIKit.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/UIKit-602GID7K8MGCRERNCQEA3M3VY.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "UIKit"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/SubFrameworks/UIUtilities.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/UIUtilities-1A5JX4PZS5D9M1BGFH3AMC1PS.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "UIUtilities"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/UniformTypeIdentifiers.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/UniformTypeIdentifiers-4XE2CM4CHZXEEFQWTRT39Z27Q.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "UniformTypeIdentifiers"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/UserNotifications.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/UserNotifications-41TI6W4LNGXV4SCYU0BQUSER.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "UserNotifications"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/usr/include/xpc.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/XPC-8YECY2AJ9XUB65JI0L69YCTVR.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "XPC"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/usr/include/DarwinFoundation1.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/_AvailabilityInternal-BOKAPP208XDR97UBGKA9NMRL5.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "_AvailabilityInternal"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/_Builtin_float-ECLFZVH6MCDD0HLP2TXKG9UGY.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "_Builtin_float"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/_Builtin_intrinsics-7I03WLBHYOM7OA97Y9649RZSP.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "_Builtin_intrinsics"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/_Builtin_inttypes-ABZE0T0NIVKJZTNF39VJPVB3N.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "_Builtin_inttypes"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/_Builtin_limits-4NM0BNMGUETZR4O3Z9L19YECU.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "_Builtin_limits"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/_Builtin_stdarg-99YS2R216X9XAHRTS9JOKYW61.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "_Builtin_stdarg"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/_Builtin_stdatomic-2V7WJDSKW09YJ2ZKF3FFOBKZO.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "_Builtin_stdatomic"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/_Builtin_stdbool-BALXXHDZWPCOH53H2G8PUW0IW.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "_Builtin_stdbool"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/_Builtin_stddef-AMGRS5UGKNI2VZB14PRCCZ95I.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "_Builtin_stddef"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/_Builtin_stdint-BN10DNJB2G0AOO6TIL0IW2NIS.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "_Builtin_stdint"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/_Builtin_tgmath-AUTLBMHO13PDAMUV1PITTFW0F.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "_Builtin_tgmath"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/usr/include/DarwinFoundation1.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/_DarwinFoundation1-DFL9DK0ELNZGR6VGNBLQSVXBC.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "_DarwinFoundation1"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/usr/include/DarwinFoundation2.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/_DarwinFoundation2-5WLPCC772IIUNBWJWHL24O3KK.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "_DarwinFoundation2"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/usr/include/DarwinFoundation3.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/_DarwinFoundation3-9CGI0O98UWZ8Q4E0VQUOT6SM7.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "_DarwinFoundation3"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/_LocationEssentials.framework/Modules/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/_LocationEssentials-2RO6H4DMHJEH1SCIL0KFEVXQW.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "_LocationEssentials"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/usr/lib/swift/shims/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/_SwiftConcurrencyShims-4151ZPTR7K4GTSNPXRRAGV73Z.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "_SwiftConcurrencyShims"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/usr/include/dnssd.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/dnssd-9VCPY46JFDGCDH0OWJGTSRHDD.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "dnssd"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/usr/include/os.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/os-EDDUHE80D92UEDHK2RN8YUCJU.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "os"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/usr/include/os.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/os_object-T6JCJNCTR705QUPC0S90R4GN.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "os_object"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/usr/include/os.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/os_workgroup-3B3PEXYE4O2CHC7B99HOUHY2G.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "os_workgroup"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/ptrauth-8KHSWVJBUTXVNUXYHI3DYJVF7.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "ptrauth"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/ptrcheck-57YU1XOWW1Z0ZR3LLC6A2S0EY.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "ptrcheck"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/usr/include/simd/module.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/simd-7WYVSMXHO9DMHPAZA6NOUWH67.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "simd"}, {"clangModuleMapPath": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/usr/include/DarwinFoundation2.modulemap", "clangModulePath": "/Volumes/Untitled/DeskStretch/sleepsoundcoach/build/ModuleCache.noindex/sys_types-A4AYGQW4Y9VJPEMBVI6QM0O52.pcm", "isBridgingHeaderDependency": false, "isFramework": false, "moduleName": "sys_types"}]