-Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/AVFAudio.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/AVFoundation.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/Accessibility.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/AudioToolbox.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/BackgroundAssets.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/Charts.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/Combine.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/CoreAudio.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/CoreData.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/CoreFoundation.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/CoreGraphics.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/CoreImage.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/CoreLocation.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/CoreMIDI.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/CoreML.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/CoreMedia.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/CoreText.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/CoreTransferable.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/CoreVideo.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/CryptoKit.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/Darwin.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/DataDetection.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/DeveloperToolsSupport.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/Dispatch.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/Distributed.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/ExtensionFoundation.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/FileProvider.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/Foundation.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/HealthKit.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/LocalAuthentication.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/Metal.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/NaturalLanguage.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/Network.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/OSLog.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/ObjectiveC.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/Observation.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/QuartzCore.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/SoundAnalysis.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/Spatial.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/Speech.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/StoreKit.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/Swift.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/SwiftData.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/SwiftOnoneSupport.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/SwiftUI.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/SwiftUICore.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/Symbols.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/Synchronization.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/System.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/UIKit.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/UniformTypeIdentifiers.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/XPC.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/_Builtin_float.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/_Concurrency.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/_DarwinFoundation1.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/_DarwinFoundation2.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/_DarwinFoundation3.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/_StringProcessing.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/_SwiftData_SwiftUI.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/os.swiftmodule/arm64-apple-ios-simulator.swiftmodule -Xlinker -add_ast_path -Xlinker /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/26.0/simd.swiftmodule/arm64-apple-ios-simulator.swiftmodule